plugins {
    java
    id("com.github.johnrengelman.shadow") version "7.1.2"
}

group = "me.nik"
version = "*******"
description = "Alice"

java {
    sourceCompatibility = JavaVersion.VERSION_1_8
    targetCompatibility = JavaVersion.VERSION_1_8
}

repositories {
    mavenCentral()
    maven("https://jitpack.io")
    maven("https://repo.dmulloy2.net/nexus/repository/public/")
    maven("https://oss.sonatype.org/content/repositories/snapshots")
    maven("https://hub.spigotmc.org/nexus/content/repositories/snapshots/")
    maven("https://repo.viaversion.com")
    maven("https://nexus.badbones69.com/repository/maven-releases/")
    maven("https://repo.extendedclip.com/content/repositories/placeholderapi/")
    mavenLocal()
}

dependencies {
    // PlaceholderAPI
    compileOnly("me.clip:placeholderapi:2.10.9") {
        exclude(group = "org.jetbrains", module = "annotations")
    }

    // ViaVersion
    compileOnly("us.myles:viaversion:3.2.1") {
        exclude(group = "us.myles", module = "viaversion-common")
        exclude(group = "us.myles", module = "viaversion-bukkit")
        exclude(group = "us.myles", module = "viaversion-bungee")
        exclude(group = "us.myles", module = "viaversion-fabric")
        exclude(group = "us.myles", module = "viaversion-sponge")
        exclude(group = "us.myles", module = "viaversion-velocity")
        exclude(group = "net.md-5", module = "bungeecord-chat")
    }

    // Floodgate
    implementation("com.github.GeyserMC.Floodgate:floodgate-bukkit:e2970e0057")

    // ProtocolSupport
    compileOnly("com.github.ProtocolSupport:ProtocolSupport:master-04834effe2-1") {
        exclude(group = "org.apache.commons", module = "commons-lang3")
        exclude(group = "it.unimi.dsi", module = "fastutil")
    }

    // CrazyEnchantments
    compileOnly("me.badbones69:crazyenchantments-plugin:1.8-Dev-Build-v8")

    // Local Maven dependencies
    compileOnly("maven-local-repo:ecoenchants-api:latest")
    compileOnly("maven-local-repo:mcmmo-api:latest")
    compileOnly("maven-local-repo:advancedenchantments-api:latest")

    // ProtocolLib
    compileOnly("com.comphenix.protocol:ProtocolLib:4.6.0") {
        exclude(group = "net.bytebuddy", module = "byte-buddy")
    }

    // Spigot API
    compileOnly("org.spigotmc:spigot-api:1.16.5-R0.1-SNAPSHOT") {
        exclude(group = "commons-lang", module = "commons-lang")
        exclude(group = "com.google.guava", module = "guava")
        exclude(group = "net.md-5", module = "bungeecord-chat")
    }

    // BungeeCord API
    compileOnly("net.md-5:bungeecord-api:1.16-R0.4-SNAPSHOT") {
        exclude(group = "net.md-5", module = "bungeecord-config")
        exclude(group = "net.md-5", module = "bungeecord-event")
        exclude(group = "net.md-5", module = "bungeecord-protocol")
        exclude(group = "io.netty", module = "netty-transport-native-unix-common")
        exclude(group = "com.google.guava", module = "guava")
        exclude(group = "net.md-5", module = "bungeecord-chat")
    }

    // Dependencies that will be shaded
    implementation("commons-io:commons-io:2.11.0")
    implementation("org.slf4j:slf4j-api:1.7.36")
    implementation("com.zaxxer:HikariCP:4.0.3")
    implementation("com.codahale.metrics:metrics-core:4.2.15")
}

tasks {
    processResources {
        filteringCharset = "UTF-8"
        filesMatching(listOf("**/*.yml", "**/*.yaml", "**/*.properties")) {
            expand(
                "version" to project.version,
                "name" to project.name,
                "description" to project.description
            )
        }
    }

    shadowJar {
        archiveBaseName.set("Alice")
        archiveClassifier.set("")

        // Include only specific dependencies
        dependencies {
            include(dependency("me.nik:.*"))
            include(dependency("commons-io:commons-io"))
            include(dependency("org.slf4j:.*"))
            include(dependency("com.zaxxer:.*"))
            include(dependency("com.codahale.metrics:.*"))
        }

        // Relocations to avoid conflicts
        relocate("org.apache.commons.io", "alice_libs.apachecommons")
        relocate("com.zaxxer.hikari", "alice_libs.hikari")
        relocate("org.slf4j", "alice_libs.slf4j")
    }

    build {
        dependsOn(shadowJar)
    }

    // Set default tasks (equivalent to Maven's defaultGoal)
    defaultTasks("clean", "build", "shadowJar")
}