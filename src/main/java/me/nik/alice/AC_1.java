package me.nik.alice;

import me.nik.alice.api.PunishAnimation;
import org.bukkit.entity.Player;

public class AC extends PunishAnimation {
   private static String[] Y3;

   public AC() {
      super(Y3[0]);
   }

   private static void mb() {
      Y3 = new String[]{"NONE"};
   }

   static {
      mb();
   }

   @Override
   public void execute(Player var1, String var2) {
      gE.h5(var2);
   }
}
