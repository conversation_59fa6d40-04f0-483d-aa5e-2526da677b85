package me.nik.alice;

import java.util.ArrayList;
import me.nik.alice.AZ.1;
import org.apache.commons.lang.WordUtils;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

public class AZ extends EJ {
   private static String[] 3V;
   private final String player;

   public AZ(QZ var1, Alice var2, String var3) {
      super(var1, var2);
      this.player = var3;
   }

   static {
      Kv();
   }

   @Override
   protected void Ch() {
      this.F7();
      this.dx.setMaxStackSize(1);
      ArrayList var1 = new ArrayList();

      for (hL var3 : this.plugin.dx().dx().dx(this.player)) {
         ArrayList var4;
         (var4 = new ArrayList()).add(3V[2]);
         var4.add(3V[3] + var3.gz());
         var4.add(3V[4]);
         var4.add(3V[5] + var3.getCheck());
         var4.add(3V[6]);
         var4.add(3V[7] + var3.x());
         var4.add(3V[8]);
         var4.add(3V[9]);

         String[] var5;
         for (String var8 : var5 = WordUtils.wrap(var3.getInformation(), 35).split(System.lineSeparator())) {
            var4.add(3V[10].concat(String.valueOf(var8)));
         }

         var1.add(this.dx(Material.PAPER, 1, Dq.sB(3V[11] + var3.getPlayer()), var4));
      }

      if (!var1.isEmpty()) {
         for (int var10 = 0; var10 < super.jD; var10++) {
            this.index = super.jD * this.DB + var10;
            if (this.index >= var1.size()) {
               break;
            }

            Object var10000 = var1.get(this.index);

            try {
               var10000.equals(null);
            } catch (Exception var9) {
               continue;
            }

            this.dx.addItem(new ItemStack[]{(ItemStack)var1.get(this.index)});
         }
      }
   }

   private static void Kv() {
      3V = new String[]{
         "&c", "'s logs", "", "&8\u00bb &7Server: &f", "", "&8\u00bb &7Check: &f", "", "&8\u00bb &7Timestamp: &f", "", "&8\u00bb &7Information:", "&f", "&6"
      };
   }

   // $VF: Unable to simplify switch on enum
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   @Override
   public void dx(InventoryClickEvent var1) {
      Player var2 = (Player)var1.getWhoClicked();
      ItemStack var3;
      if ((var3 = var1.getCurrentItem()).getItemMeta().hasDisplayName()) {
         switch (1.Aa[var3.getType().ordinal()]) {
            case 1:
               var2.closeInventory();
               new pl(this.dx, this.plugin).iv();
               return;
            case 2:
               String var4 = ChatColor.stripColor(var3.getItemMeta().getDisplayName());
               byte var5 = -1;
               switch (var4.hashCode()) {
                  case -1133036644:
                     if (Integer.valueOf(-1133036644).equals(var4.hashCode())) {
                        var5 = 1;
                     }
                     break;
                  case 473267736:
                     if (Integer.valueOf(473267736).equals(var4.hashCode())) {
                        var5 = 0;
                     }
               }

               switch (var5) {
                  case 0:
                     if (this.DB != 0) {
                        this.DB--;
                        super.iv();
                        return;
                     }
                     break;
                  case 1:
                     this.DB++;
                     super.iv();
               }
         }
      }
   }

   @Override
   protected String yM() {
      return Dq.sB(3V[0] + this.player + 3V[1]);
   }

   @Override
   protected int og() {
      return 54;
   }
}
