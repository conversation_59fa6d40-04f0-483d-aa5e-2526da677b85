package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import org.bukkit.Location;

public class A extends PacketCheck {
   private static String[] Uw;

   public A(UC var1) {
      super(var1, CheckType.SPEED, Uw[0], Category.MOVE, 2.0F);
   }

   static {
      Ag();
   }

   @Override
   public String sB() {
      return Uw[1];
   }

   private static void Ag() {
      Uw = new String[]{"Sprint", "Checks for irregular sprint directions", "Angle: ", " expected: ", " DY: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && this.Aa.dx().dx().og(1500L) <= 0
         && this.Aa.dx().hW() >= 10
         && this.Aa.dx().p6() >= 20
         && this.Aa.dx().a() <= 0
         && this.Aa.dx().Aa().getY() % 0.5 == 0.0
         && this.Aa.dx().tV() >= 5
         && this.Aa.dx().P() >= 40
         && this.Aa.dx().cO() >= 20
         && this.Aa.dx().yV() >= 20
         && this.Aa.dx().R2() >= 40) {
         ES var12;
         Location var2 = (var12 = this.Aa.dx()).Aa();
         double var5 = (double)var12.og().clone().subtract(var2.clone()).toVector().angle(var2.getDirection());
         Qk var14 = this.Aa.dx();
         qn var3;
         float var4 = (var3 = this.Aa.dx()).b();
         double var10 = 1.59;
         if (var14.tr()) {
            var10 = 1.59 - Math.abs(var14.cO() * 2.5);
         }

         if (var12.Qt()) {
            var10 -= 0.0125;
         }

         if (var4 > 12.5F) {
            var10 -= 0.3;
         }

         if (var4 > 85.0F) {
            var10 -= 0.5;
         }

         if (var4 > 125.0F) {
            var10 -= 0.5;
         }

         if (Math.abs(var3.getPitch()) > 80.0F) {
            var10 -= 0.05;
         }

         if (var12.tk() < 5) {
            var10 -= 0.9;
         }

         boolean var13 = (var4 == 90.0F || var4 == 180.0F || var4 == 270.0F) && var5 >= 1.5 || var4 >= 90.0F && var5 >= 3.122;
         if (var5 < var10 || var13) {
            this.Aa(Uw[2] + var5 + Uw[3] + var10 + Uw[4] + var4);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.125);
         }
      }
   }
}
