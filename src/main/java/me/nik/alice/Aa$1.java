package me.nik.alice;

import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

class Aa$1 extends BukkitRunnable {
   final Aa dx;
   int count;
   final String dx;
   final Player dx;

   Aa$1(Aa var1, Player var2, String var3) {
      this.dx = var1;
      this.dx = var2;
      this.dx = var3;
      this.count = 0;
   }

   public void run() {
      if (!this.dx.isOnline()) {
         gE.h5(this.dx);
         this.cancel();
      } else {
         this.dx.sendMessage(Aa.dx());
         if (this.count++ > 30) {
            gE.h5(this.dx);
            this.cancel();
         }
      }
   }
}
