package me.nik.alice;

import me.nik.alice.Aa.1;
import me.nik.alice.api.PunishAnimation;
import org.bukkit.entity.Player;

public class Aa extends PunishAnimation {
   private static String Aa;
   private static String[] T7;

   private static void Ff() {
      T7 = new String[]{"ERROR", "&4&l&k%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%%"};
   }

   static {
      Ff();
   }

   @Override
   public void execute(Player var1, String var2) {
      new rq(Aa).Fh(var1);
      new 1(this, var1, var2).runTaskTimer(Alice.dx(), 0L, 1L);
   }

   static String dx() {
      return Aa;
   }

   public Aa() {
      super(T7[0]);
      Aa = Dq.sB(T7[1]);
   }
}
