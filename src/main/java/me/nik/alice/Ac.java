package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Ac extends PacketCheck {
   private static String[] vn;

   static {
      Az();
   }

   @Override
   public String sB() {
      return vn[1];
   }

   private static void Az() {
      vn = new String[]{"Friction", "Checks for invalid friction", "Delta: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().Qt() && this.Aa.dx().u() > 2) {
         ES var9;
         double var3 = (var9 = this.Aa.dx()).Zp() * 0.91F + 0.025999999F;
         double var5 = var9.F7() - var3;
         double var7 = 1.0E-12 + (var9.b().size() > 0 ? 0.125 : 0.0);
         Qk var10 = this.Aa.dx();
         var7 += var10.tr() ? Math.abs(var10.cO()) * 2.25 : 0.0;
         if (var5 > var7) {
            this.Aa(vn[2].concat(String.valueOf(var5)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.og();
         }
      }
   }

   public Ac(UC var1) {
      super(var1, CheckType.SPEED, vn[0], Category.MOVE, 3.0F);
   }
}
