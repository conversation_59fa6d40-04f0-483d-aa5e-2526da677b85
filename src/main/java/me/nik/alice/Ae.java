package me.nik.alice;

import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;

public final class Ae {
   private static String[] F2;
   private static final List b = new ArrayList();

   private static void lI() {
      F2 = new String[]{"abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ"};
   }

   protected static String zP() {
      return (String)b.get(s8.og(0.0, (double)(b.size() - 1)));
   }

   protected static int Zh() {
      return s8.og(500.0, 1000.0);
   }

   static {
      lI();
      char[] var0 = F2[0].toCharArray();
      StringBuilder var1 = new StringBuilder();
      int var2 = var0.length;
      ThreadLocalRandom var3 = ThreadLocalRandom.current();

      for (int var4 = 0; var4 < 400; var4++) {
         var1.append(var0[var3.nextInt(var2)]);
         if (var1.length() == 8) {
            b.add(var1.toString());
            var1.setLength(0);
         }
      }
   }

   private Ae() {
   }
}
