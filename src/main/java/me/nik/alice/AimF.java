package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class zP extends PacketCheck {
   private static String[] gN;

   public zP(UC var1) {
      super(var1, CheckType.AIM, gN[1], Category.COMBAT);
   }

   @Override
   public String sB() {
      return gN[0];
   }

   static {
      Dc();
   }

   @Override
   public void dx(DH var1) {
      if (var1.PU() && !this.Aa.dx().yM() && this.Aa.dx().tV() >= 5 && !this.Aa.dx().Aa(3)) {
         float var2;
         qn var6;
         double var4 = (double)(var2 = (var6 = this.Aa.dx()).b()) / var6.dx().u();
         if (var2 > 60.0F && (double)var2 == var4) {
            this.og(gN[2].concat(String.valueOf(var4)));
         }
      }
   }

   private static void Dc() {
      gN = new String[]{"Checks for snap", "F", "Delta: "};
   }
}
