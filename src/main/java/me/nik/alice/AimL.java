package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class DB extends PacketCheck {
   private static String[] B9;

   @Override
   public String sB() {
      return B9[1];
   }

   public DB(UC var1) {
      super(var1, CheckType.AIM, B9[0], Category.COMBAT, 10.0F);
   }

   private static void Ku() {
      B9 = new String[]{"L", "Checks for impossible rotation constants", "Constant yaw: ", " Constant pitch: ", " expected: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         qn var14;
         float var2 = (var14 = this.Aa.dx()).b();
         float var3 = var14.UH();
         if (!(var2 <= 0.0F) && !(var3 <= 0.0F) && !(var2 > 30.0F) && !(var3 > 30.0F)) {
            long var6;
            ZQ var15;
            if ((var6 = (var15 = var14.dx()).sX()) >= 30L) {
               double var8 = var15.nZ();
               double var10 = var15.Eu();
               double var12 = var14.dx().pO() ? 0.001 : (Double)Rk.AC().get(var6);
               if (var8 < var12 && var10 < var12) {
                  this.Aa(B9[2] + var8 + B9[3] + var10 + B9[4] + var12);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.75);
               }
            }
         }
      }
   }

   static {
      Ku();
   }
}
