package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

@h5
public class jD extends PacketCheck {
   private static String[] Tf;

   public jD(UC var1) {
      super(var1, CheckType.AIM, Tf[1], Category.COMBAT, 15.0F);
   }

   static {
      vh();
   }

   private static void vh() {
      Tf = new String[]{"Checks if the player's rotations follows entity renderer", "M", "(Yaw) Delta: ", "(Pitch) Delta: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         qn var15;
         float var2 = (var15 = this.Aa.dx()).b();
         float var3 = var15.UH();
         if (!(var2 <= 0.0F) && !(var3 <= 0.0F) && !(var2 > 30.0F) && !(var3 > 30.0F)) {
            ZQ var16;
            if ((var16 = var15.dx()).h5()) {
               float var4 = var16.zP();
               long var7 = var16.sX();
               if (var4 != -1.0F && var7 != -1L) {
                  if (var7 != 145L && var7 != 111L && var7 != 67L) {
                     float var17;
                     float var10000 = var17 = var4 * 0.6F + 0.2F;
                     var4 = var10000 * var10000 * var17 * 1.2F;
                     boolean var5 = false;
                     if (!(var2 < var4 * 5.0F)) {
                        float var6 = (float)var16.tm() * var4;
                        double var13;
                        if ((var13 = (double)Math.abs(var2 - var6)) <= 1.4E-45) {
                           var5 = true;
                           this.Aa(Tf[2].concat(String.valueOf(var13)));
                        }
                     }

                     if (!(var3 < var4 * 5.0F)) {
                        float var19 = (float)var16.Ch() * var4;
                        double var20;
                        if ((var20 = (double)Math.abs(var3 - var19)) <= 1.4E-45) {
                           var5 = true;
                           this.Aa(Tf[3].concat(String.valueOf(var20)));
                        }
                     }

                     if (var5) {
                        if (this.Aa() > this.dx()) {
                           this.sX();
                           return;
                        }
                     } else {
                        this.og();
                     }
                  }
               }
            }
         }
      }
   }

   @Override
   public String sB() {
      return Tf[0];
   }
}
