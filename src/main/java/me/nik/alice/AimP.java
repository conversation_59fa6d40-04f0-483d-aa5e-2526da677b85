package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

@h5
public class Zm extends PacketCheck {
   private static String[] hm;
   private final V0 sB = new V0(50);

   private static void yj() {
      hm = new String[]{"P", "Checks if the player's rotations follow entity renderer by using heuristics", "D: ", " A: "};
   }

   @Override
   public String sB() {
      return hm[1];
   }

   @Override
   public void dx(DH var1) {
      boolean var20 = wb2e;
      if (var1.R() && !this.Aa.dx().yM()) {
         ZQ var2;
         float var3;
         qn var21;
         if ((var3 = (var2 = (var21 = this.Aa.dx()).dx()).zP()) == -1.0F) {
            this.sB.clear();
         } else {
            float var25;
            float var10000 = var25 = var3 * 0.6F + 0.2F;
            var3 = var10000 * var10000 * var25 * 1.2F;
            float var4 = var21.b();
            float var22 = var21.UH();
            if (!(var4 <= 1.0F) && !(var22 <= 1.0F)) {
               float var5 = (float)var2.tm() * var3;
               float var24 = (float)var2.Ch() * var3;
               double var11 = (double)Math.abs(var4 - var5);
               double var13 = (double)Math.abs(var22 - var24);
               double var15 = Math.abs(var11 - var13);
               this.sB.add(var15);
               if (this.sB.h5()) {
                  int var23 = l.AC(this.sB);
                  double var18 = l.Zh(this.sB);
                  boolean var27;
                  if ((var23 != 0 || var18 != 0.0) && (var23 <= 10 || !(var18 > 3.0)) && var23 != this.sB.size()) {
                     var27 = false;
                  } else {
                     var27 = true;
                     if (var20) {
                        throw null;
                     }
                  }

                  if (var27) {
                     this.Aa(hm[2] + var23 + hm[3] + var18);
                     if (this.Aa() > this.dx()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.og();
                  }
               }
            }
         }
      }
   }

   public Zm(UC var1) {
      super(var1, CheckType.AIM, hm[0], Category.COMBAT, 1.0F);
   }

   static {
      yj();
   }
}
