package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class bI extends PacketCheck {
   private static String[] 63;

   private static void Mo() {
      63 = new String[]{"R", "Checks if the player's rotations follow entity renderer too much", "YD: ", " PD: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         ZQ var2;
         qn var8;
         float var3 = (var2 = (var8 = this.Aa.dx()).dx()).zP();
         long var5;
         if ((var5 = var2.sX()) != 145L && var5 != 111L && var5 != 67L) {
            if (var3 != -1.0F) {
               float var11;
               float var10000 = var11 = var3 * 0.6F + 0.2F;
               float var12 = var10000 * var10000 * var11 * 1.2F;
               if (!(var8.b() < var12 * 5.0F) && !(var8.UH() < var12 * 5.0F)) {
                  var3 = var8.h5();
                  float var4 = var8.Fh();
                  float var16 = var8.getYaw();
                  float var9 = var8.getPitch();
                  float var6 = var16 - var3 - (var16 - var3) % var12;
                  var6 = var3 + var6;
                  var3 = var9 - var4 - (var9 - var4) % var12;
                  float var7 = var4 + var3;
                  float var13 = Math.abs(var6 - var16);
                  float var10 = Math.abs(var7 - var9);
                  if (Math.abs(var13 - var10) == 0.0F) {
                     this.Aa(63[2] + var13 + 63[3] + var10);
                     if (this.Aa() > this.dx()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.25);
                  }
               }
            }
         }
      }
   }

   static {
      Mo();
   }

   @Override
   public String sB() {
      return 63[1];
   }

   public bI(UC var1) {
      super(var1, CheckType.AIM, 63[0], Category.COMBAT, 10.0F);
   }
}
