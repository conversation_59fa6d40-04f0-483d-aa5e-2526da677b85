package me.nik.alice;

import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryType;

public class C extends f2 {
   private static String[] XZ;

   @Override
   public boolean og(Player var1) {
      return var1.getOpenInventory().getType() == InventoryType.ANVIL;
   }

   private static void NS() {
      XZ = new String[]{"AnvilLot"};
   }

   @Override
   public boolean Aa(Player var1) {
      return false;
   }

   @Override
   public boolean dx(Player var1) {
      return false;
   }

   public C() {
      super(XZ[0]);
   }

   static {
      NS();
   }
}
