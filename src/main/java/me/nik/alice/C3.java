package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class C3 extends PacketCheck {
   private static String[] p6;

   public C3(UC var1) {
      super(var1, CheckType.SPEED, p6[0], Category.MOVE, 1.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().jA() && this.Aa.dx().R2() >= 20) {
         ES var9;
         float var2 = (float)(var9 = this.Aa.dx()).DB();
         float var3 = var9.u() > 0 ? JA.dx(this.Aa) : JA.Aa(this.Aa);
         Qk var4;
         var3 = (float)((var4 = this.Aa.dx()).tr() ? (double)var3 + Math.abs(var4.cO()) : (double)var3);
         if (var9.yk() < 20) {
            var3 += 0.75F;
         }

         double var7 = var9.F7();
         if (var2 > var3 && var7 >= 0.2) {
            this.Aa(p6[2] + var2 + p6[3] + var3 + p6[4] + var7);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.05);
         }
      }
   }

   @Override
   public String sB() {
      return p6[1];
   }

   static {
      c0();
   }

   private static void c0() {
      p6 = new String[]{"Acceleration", "Checks for high speed acceleration", "Acceleration: ", " expected: ", " delta xz: "};
   }
}
