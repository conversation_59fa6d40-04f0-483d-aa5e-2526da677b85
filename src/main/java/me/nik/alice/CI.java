package me.nik.alice;

import java.util.HashSet;
import java.util.Set;
import me.nik.alice.api.PunishAnimation;

public class CI {
   private final Set Aa = new HashSet();

   public void dx(PunishAnimation var1) {
      this.Aa.add(var1);
   }

   public CI() {
      this.Aa.add(new AC());
      this.Aa.add(new og());
      this.Aa.add(new dx());
      this.Aa.add(new Aa());
   }

   public PunishAnimation dx() {
      return (PunishAnimation)gE.dx(this.Aa);
   }

   public Set dx() {
      return this.Aa;
   }
}
