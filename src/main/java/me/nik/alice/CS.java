package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class CS extends PacketCheck {
   private static String[] dn;

   @Override
   public String sB() {
      return dn[1];
   }

   public CS(UC var1) {
      super(var1, CheckType.PACKET, dn[0], Category.WORLD, 1.0F);
   }

   static {
      3H();
   }

   @Override
   public void dx(DH var1) {
      if (var1.Lh()) {
         boolean var2 = this.Aa.dx().Fh() < 35L;
         if (this.Aa.dx().Qt() == 0 && !var2) {
            this.Aa(dn[2]);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.25);
         }
      }
   }

   private static void _H/* $VF was: 3H*/() {
      dn = new String[]{"Y", "Checks for inventory actions while fighting", "Window click packet while fighting"};
   }
}
