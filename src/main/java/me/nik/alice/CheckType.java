package me.nik.alice;

public enum CheckType {
   AIM(CheckType.r7[1]),
   CLICKING(CheckType.r7[3]),
   PACKET(CheckType.r7[5]),
   FLY(CheckType.r7[7]),
   AUTOFISH(CheckType.r7[9]),
   KILLAURA(CheckType.r7[11]),
   SCAFFOLD(CheckType.r7[13]),
   SPEED(CheckType.r7[15]),
   MOTION(CheckType.r7[17]),
   NOFALL(CheckType.r7[19]),
   JESUS(CheckType.r7[21]),
   ESP(CheckType.r7[23]),
   BARITONE(CheckType.r7[25]),
   VEHICLE(CheckType.r7[27]),
   ELYTR<PERSON>(CheckType.r7[29]),
   TIMER(CheckType.r7[31]),
   REA<PERSON>(CheckType.r7[33]),
   VELOCITY(CheckType.r7[35]),
   INVENTORY(CheckType.r7[37]),
   INTERACT(CheckType.r7[39]),
   FASTCLIMB(CheckType.r7[41]),
   <PERSON>RAY(CheckType.r7[43]),
   HITBOX(CheckType.r7[45]);

   private final String checkName;
   private static final CheckType[] dx;
   private static String[] r7;

   private CheckType(String var3) {
      this.checkName = var3;
   }

   public String getCheckName() {
      return this.checkName;
   }

   static {
      7s();
      dx = new CheckType[]{AIM, CLICKING, PACKET, FLY, AUTOFISH, KILLAURA, SCAFFOLD, SPEED, MOTION, NOFALL, JESUS, ESP, BARITONE, VEHICLE, ELYTRA, TIMER, REACH, VELOCITY, INVENTORY, INTERACT, FASTCLIMB, XRAY, HITBOX};
   }

   private static void 7s() {
      r7 = new String[]{
         "Aim", "Aim", "Clicking", "Clicking", "Packet", "Packet", "Fly", "Fly", "AutoFish", "AutoFish", "KillAura", "KillAura", "Scaffold", "Scaffold", "Speed", "Speed", "Motion", "Motion", "NoFall", "NoFall", "Jesus", "Jesus", "ESP", "ESP", "Baritone", "Baritone", "Vehicle", "Vehicle", "Elytra", "Elytra", "Timer", "Timer", "Reach", "Reach", "Velocity", "Velocity", "Inventory", "Inventory", "Interact", "Interact", "FastClimb", "FastClimb", "XRay", "XRay", "Hitbox", "Hitbox"
      };
   }
}
