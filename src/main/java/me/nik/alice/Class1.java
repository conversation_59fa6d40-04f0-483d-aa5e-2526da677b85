package me.nik.alice;

import java.lang.invoke.ConstantCallSite;
import java.lang.invoke.MethodHandle;
import java.lang.invoke.MethodType;
import java.lang.invoke.MethodHandles.Lookup;

public class Class1 {
   public static Object md_Object_4(Object var0, Object var1, Object var2, Object var3, Object var4, Object var5, Object var6, Object var7) {
      try {
         char[] var8 = var4.toString().toCharArray();
         char[] var9 = new char[var8.length];
         char[] var10 = var5.toString().toCharArray();
         char[] var11 = new char[var10.length];
         char[] var12 = var6.toString().toCharArray();
         char[] var13 = new char[var12.length];
         String var14 = new String(new byte[]{106, 111, 65, 89, 100, 113, 51, 55, 90, 110, 69, 54, 82});
         String var15 = new String(new byte[]{52, 101, 99, 52, 111, 118, 106, 53, 57, 122, 115, 115, 106, 103, 49});
         String var16 = new String(new byte[]{52, 76, 100, 89, 109, 88, 109, 100, 85, 80, 49, 116, 116, 56, 50, 87, 98, 82});

         for (int var17 = 0; var17 < var8.length; var17++) {
            switch (var17 % 4) {
               case 0:
                  var9[var17] = (char)(var8[var17] ^ var14.hashCode());
                  break;
               case 1:
                  var9[var17] = (char)(var8[var17] ^ var7.toString().hashCode());
                  break;
               case 2:
                  var9[var17] = (char)(var8[var17] ^ var15.hashCode());
                  break;
               default:
                  var9[var17] = (char)(var8[var17] ^ var16.hashCode());
            }
         }

         for (int var20 = 0; var20 < var10.length; var20++) {
            switch (var20 % 4) {
               case 0:
                  var11[var20] = (char)(var10[var20] ^ var14.hashCode());
                  break;
               case 1:
                  var11[var20] = (char)(var10[var20] ^ var7.toString().hashCode());
                  break;
               case 2:
                  var11[var20] = (char)(var10[var20] ^ var15.hashCode());
                  break;
               default:
                  var11[var20] = (char)(var10[var20] ^ var16.hashCode());
            }
         }

         for (int var21 = 0; var21 < var12.length; var21++) {
            switch (var21 % 4) {
               case 0:
                  var13[var21] = (char)(var12[var21] ^ var14.hashCode());
                  break;
               case 1:
                  var13[var21] = (char)(var12[var21] ^ var7.toString().hashCode());
                  break;
               case 2:
                  var13[var21] = (char)(var12[var21] ^ var15.hashCode());
                  break;
               default:
                  var13[var21] = (char)(var12[var21] ^ var16.hashCode());
            }
         }

         int var22 = (Integer)var3;
         MethodHandle var18;
         switch (var22) {
            case 0:
               var18 = ((Lookup)var0)
                  .findStatic(
                     Class.forName(new String(var9)),
                     new String(var11),
                     MethodType.fromMethodDescriptorString(new String(var13), Class1.class.getClassLoader())
                  );
               break;
            case 1:
               var18 = ((Lookup)var0)
                  .findVirtual(
                     Class.forName(new String(var9)),
                     new String(var11),
                     MethodType.fromMethodDescriptorString(new String(var13), Class1.class.getClassLoader())
                  );
               break;
            default:
               throw new BootstrapMethodError();
         }

         var18 = var18.asType((MethodType)var2);
         return new ConstantCallSite(var18);
      } catch (Exception var19) {
         throw new Class7();
      }
   }

   public static String md_String_5(String var0, int var1) {
      StringBuilder var4 = new StringBuilder();
      char[] var5 = var0.toCharArray();

      for (int var7 = 0; var7 < var5.length; var7++) {
         switch (var7 % 4) {
            case 0:
               var4.append((char)(var5[var7] ^ var1));
               break;
            case 1:
               var4.append((char)(var5[var7] ^ 1769178879));
               break;
            case 2:
               var4.append((char)(var5[var7] ^ -240157324));
               break;
            default:
               var4.append((char)(var5[var7] ^ 20488));
         }
      }

      return var4.toString();
   }
}
