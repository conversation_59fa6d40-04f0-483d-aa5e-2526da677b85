package me.nik.alice;

import me.nik.alice.checks.PacketCheck;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;

public class yV extends PacketCheck {
   private long og;
   private long AC;
   private long sB;
   private final V0 dC = new V0(20);
   private static String[] zH;

   @Override
   public void dx(DH var1) {
      boolean var19 = ZjL0;
      if (var1.i2() && !this.Aa.dx().UH()) {
         long var2;
         long var4 = (var2 = var1.getTimeStamp()) - this.og;
         this.og = var2;
         if (var4 > 0L) {
            if (var4 > 10000L) {
               this.dC.clear();
            }

            this.dC.add(var4);
            if (this.dC.h5()) {
               ArrayList var20;
               Collections.sort(var20 = new ArrayList(this.dC));
               long var7 = Math.abs((Long)var20.get(0) - (Long)var20.get(var20.size() - 1));
               long var9 = this.AC;
               this.AC = var7;
               long var11 = this.sB;
               this.sB = var9;
               long var13 = Math.abs(var7 - var9 - var11);
               double var15 = l.sB(Arrays.asList(var7, var9, var11));
               long var17;
               boolean var10000;
               if ((var17 = this.Aa.dx().sB()) > 7L && (var13 <= 50L || var15 <= 2.5)) {
                  var10000 = true;
                  if (var19) {
                     throw null;
                  }
               } else {
                  var10000 = false;
               }

               if (var10000) {
                  this.Aa(zH[2] + var17 + zH[3] + var13 + zH[4] + var15);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.og();
               }
            }
         }
      }
   }

   @Override
   public String sB() {
      return zH[1];
   }

   static {
      aH();
   }

   public yV(UC var1) {
      super(var1, VL.Aa, zH[0], b.dx, 1.0F);
   }

   private static void aH() {
      zH = new String[]{"A", "Checks for repeated range differences", "CPS: ", " range diff: ", " range dev: "};
   }
}
