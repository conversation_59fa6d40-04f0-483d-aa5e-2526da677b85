package me.nik.alice;

public class tk extends PacketCheck {
   private final V0 dC = new V0(20);
   private static String[] kg;
   private final V0 Zh = new V0(5);
   private long og;

   static {
      Zp();
   }

   @Override
   public String sB() {
      return kg[1];
   }

   public tk(UC var1) {
      super(var1, VL.Aa, kg[0], b.dx, 2.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.i2() && !this.Aa.dx().UH()) {
         long var2;
         long var4 = (var2 = var1.getTimeStamp()) - this.og;
         this.og = var2;
         if (var4 > 0L) {
            if (var4 > 10000L) {
               this.dC.clear();
            }

            this.dC.add(var4);
            if (this.dC.h5()) {
               double var6 = l.Zh(this.dC);
               this.Zh.add(var6);
               if (this.Zh.h5()) {
                  double var8;
                  if ((var8 = l.sB(this.Zh)) < 3.0) {
                     this.Aa(kg[2].concat(String.valueOf(var8)));
                     if (this.Aa() > this.dx()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.og();
                  }
               }
            }
         }
      }
   }

   private static void Zp() {
      kg = new String[]{"D", "Checks for repeated click patterns", "PD: "};
   }
}
