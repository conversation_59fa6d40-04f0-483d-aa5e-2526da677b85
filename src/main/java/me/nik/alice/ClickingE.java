package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Eu extends PacketCheck {
   private long og;
   private double sB;
   private double AC;
   private static String[] Oe;
   private final V0 dC = new V0(20);

   public Eu(UC var1) {
      super(var1, CheckType.CLICKING, Oe[0], Category.COMBAT, 3.0F);
   }

   static {
      Cq();
   }

   @Override
   public void dx(DH var1) {
      if (var1.i2() && !this.Aa.dx().UH()) {
         long var2;
         long var4 = (var2 = var1.getTimeStamp()) - this.og;
         this.og = var2;
         if (var4 > 0L) {
            if (var4 > 10000L) {
               this.dC.clear();
            }

            this.dC.add(var4);
            if (this.dC.h5()) {
               double var6 = l.Zh(this.dC);
               double var8 = l.sB(this.dC);
               double var10 = this.sB;
               this.sB = var6;
               double var12 = this.AC;
               this.AC = var8;
               double var14 = Math.abs(var6 - var10);
               double var16 = Math.abs(var8 - var12);
               if (var14 < 2.5 && var16 < 2.5) {
                  this.Aa(Oe[2] + var14 + Oe[3] + var16);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.75);
               }
            }
         }
      }
   }

   private static void Cq() {
      Oe = new String[]{"E", "Checks for small differences between clicks", "AverageDiff: ", " deviationDiff: "};
   }

   @Override
   public String sB() {
      return Oe[1];
   }
}
