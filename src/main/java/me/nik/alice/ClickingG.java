package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class u extends PacketCheck {
   private static String[] ZW;
   private final V0 dC = new V0(20);
   private long og;
   private double Zh;
   private double dC;

   @Override
   public String sB() {
      return ZW[1];
   }

   static {
      p0();
   }

   @Override
   public void dx(DH var1) {
      if (var1.i2() && !this.Aa.dx().UH()) {
         long var2;
         long var4 = (var2 = var1.getTimeStamp()) - this.og;
         this.og = var2;
         if (var4 > 0L) {
            if (var4 > 10000L) {
               this.dC.clear();
            }

            this.dC.add(var4);
            if (this.dC.h5()) {
               double var6 = l.Aa(this.dC);
               double var8 = this.dC;
               this.dC = var6;
               double var10 = Math.abs(var6 - var8);
               double var12 = this.Zh;
               this.Zh = var10;
               double var14 = Math.abs(var10 - var12);
               long var16 = this.Aa.dx().sB();
               if (var14 < 0.5 && var16 > 7L) {
                  this.Aa(ZW[2].concat(String.valueOf(var14)));
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.5);
               }
            }
         }
      }
   }

   public u(UC var1) {
      super(var1, CheckType.CLICKING, ZW[0], Category.COMBAT, 2.0F);
   }

   private static void p0() {
      ZW = new String[]{"G", "Checks for small kurtosis difference", "Kurtosis diff: "};
   }
}
