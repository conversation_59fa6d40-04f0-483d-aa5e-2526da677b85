package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class D1 extends PacketCheck {
   private static String[] YY;

   static {
      Ts();
   }

   private static void Ts() {
      YY = new String[]{"B", "Checks for invalid fall distance", "Fall distance: ", " Delta Y: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && !(this.Aa.getPlayer().getLocation().getY() < 4.0)
         && !this.Aa.dx().DB()
         && !this.Aa.dx().dx().DP()
         && !this.Aa.dx().tr()
         && this.Aa.dx().GE() >= 40
         && this.Aa.dx().dx().og(2000L) <= 0) {
         ES var8;
         if (!((var8 = this.Aa.dx()).Ch() >= 0.0) && (double)var8.dC() != 0.0) {
            double var3 = Math.abs(var8.Ch());
            float var2 = var8.dC();
            double var6 = var3 - (double)var2;
            if (var8.M3() > 6 && var6 >= 0.0 && var3 >= 1.0) {
               this.Aa(YY[2] + var2 + YY[3] + var3);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.125);
            }
         }
      }
   }

   @Override
   public String sB() {
      return YY[1];
   }

   public D1(UC var1) {
      super(var1, CheckType.NOFALL, YY[0], Category.WORLD, 2.0F);
   }
}
