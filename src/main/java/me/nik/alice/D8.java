package me.nik.alice;

import java.io.File;
import java.io.Reader;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.stream.Stream;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.plugin.java.JavaPlugin;
import org.yaml.snakeyaml.DumperOptions;

public class D8 extends Y {
   private static String[] uu;
   private final q dx;
   private final File file;
   private int Zp;

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private String VL() {
      boolean var3 = 2Nz7;
      if (!(this.dx instanceof YamlConfiguration)) {
         throw new UnsupportedOperationException(uu[5]);
      } else {
         YamlConfiguration var1 = (YamlConfiguration)this.dx;

         label68: {
            ReflectiveOperationException var10000;
            label63: {
               Field var14;
               try {
                  var11 = YamlConfiguration.class.getDeclaredField(uu[6]);
                  var14 = var11;
               } catch (ReflectiveOperationException var9) {
                  var10000 = var9;
                  boolean var10001 = false;
                  break label63;
               }

               Field var2;
               try {
                  var2 = var14;
                  var11.setAccessible(true);
               } catch (ReflectiveOperationException var8) {
                  var10000 = var8;
                  boolean var15 = false;
                  break label63;
               }

               Field var12 = var2;
               YamlConfiguration var16 = var1;

               try {
                  (var10 = (DumperOptions)var12.get(var16)).setWidth(Integer.MAX_VALUE);
               } catch (ReflectiveOperationException var7) {
                  var10000 = var7;
                  boolean var17 = false;
                  break label63;
               }

               try {
                  var13 = Stream.of(DumperOptions.class.getDeclaredMethods());
                  var19 = D8::dx;
               } catch (ReflectiveOperationException var6) {
                  var10000 = var6;
                  boolean var18 = false;
                  break label63;
               }

               try {
                  if (!var13.anyMatch(var19)) {
                     break label68;
                  }
               } catch (ReflectiveOperationException var5) {
                  var10000 = var5;
                  boolean var20 = false;
                  break label63;
               }

               try {
                  var10.setIndicatorIndent(2);
                  break label68;
               } catch (ReflectiveOperationException var4) {
                  var10000 = var4;
                  boolean var21 = false;
               }
            }

            var10000.printStackTrace();
            return var1.saveToString();
         }

         if (var3) {
            throw null;
         } else {
            return var1.saveToString();
         }
      }
   }

   public void dx(boolean var1) {
      String var2 = this.VL();
      this.dx.dx(var2, this.file, var1);
   }

   public static D8 dx(JavaPlugin var0, File var1) {
      return new q(var0).dx(var1);
   }

   public void dx(File var1, boolean var2) {
      String var3 = this.VL();
      this.dx.dx(var3, var1, var2);
   }

   private static void jI() {
      uu = new String[]{"", "_COMMENT_", " ", "_COMMENT_", " ", "Cannot get config string of non-YamlConfiguration", "yamlOptions"};
   }

   public void dx(String var1, Object var2, String[] var3) {
      if (!this.contains(var1)) {
         int var4;
         String var8 = (var4 = var1.lastIndexOf(46)) == -1 ? uu[0] : var1.substring(0, var4) + '.';

         for (String var7 : var3) {
            this.set(var8 + this.dx.UH() + uu[1] + this.Zp, uu[2].concat(String.valueOf(var7)));
            this.Zp++;
         }
      }

      this.set(var1, var2);
   }

   public void dx(String[] var1) {
      for (String var4 : var1) {
         this.set(this.dx.UH() + uu[3] + this.Zp, uu[4].concat(String.valueOf(var4)));
         this.Zp++;
      }
   }

   static {
      jI();
   }

   public void Zh(File var1) {
      this.dx(var1, false);
   }

   public void hW() {
      this.dx(false);
   }

   private static boolean dx(Method var0) {
      return Integer.valueOf(-976355623).equals(var0.getName().hashCode());
   }

   public D8(Reader var1, File var2, int var3, JavaPlugin var4) {
      super(YamlConfiguration.loadConfiguration(var1));
      this.Zp = var3;
      this.dx = new q(var4);
      this.file = var2;
   }

   public void zP() {
      this.dx = YamlConfiguration.loadConfiguration(this.dx.dx(this.file));
   }
}
