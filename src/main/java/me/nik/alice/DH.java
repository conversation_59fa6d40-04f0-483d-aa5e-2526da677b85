package me.nik.alice;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.EnumWrappers.EntityUseAction;

public class DH {
   private final PacketType dx;
   private final long timeStamp;
   private final PacketContainer dx;

   public boolean CS() {
      return this.dx == Client.BLOCK_PLACE;
   }

   public boolean uX() {
      return this.dx == Server.KEEP_ALIVE;
   }

   public PacketContainer dx() {
      return this.dx;
   }

   public boolean yk() {
      return this.dx == Client.RESOURCE_PACK_STATUS;
   }

   public boolean PU() {
      return this.dx == Client.POSITION_LOOK;
   }

   public boolean GE() {
      return this.dx == Client.SPECTATE;
   }

   public boolean Lh() {
      return this.dx == Client.WINDOW_CLICK;
   }

   public boolean qx() {
      return this.dx == Server.ABILITIES;
   }

   public boolean xx() {
      return this.dx == Client.LOOK;
   }

   public boolean E() {
      return this.dx == Client.CLIENT_COMMAND;
   }

   public boolean R() {
      return this.dx == Client.LOOK || this.dx == Client.POSITION_LOOK;
   }

   public boolean Sh() {
      return this.dx == Client.VEHICLE_MOVE;
   }

   public boolean d() {
      return this.dx == Client.USE_ITEM;
   }

   public boolean R2() {
      return this.dx == Server.EXPLOSION;
   }

   public boolean J4() {
      return this.dx == Client.HELD_ITEM_SLOT;
   }

   public boolean P() {
      return this.dx == Client.POSITION;
   }

   public PacketType dx() {
      return this.dx;
   }

   public boolean K() {
      return this.dx == Client.SETTINGS;
   }

   public boolean pQ() {
      return this.dx == Client.POSITION || this.dx == Client.POSITION_LOOK;
   }

   public long getTimeStamp() {
      return this.timeStamp;
   }

   public boolean Ej() {
      return this.dx == Client.STEER_VEHICLE;
   }

   public boolean Vl() {
      return this.dx == Client.CHAT;
   }

   public boolean SP() {
      return this.dx == Server.ENTITY_VELOCITY;
   }

   public boolean uD() {
      return this.dx == Client.BLOCK_DIG;
   }

   public boolean hB() {
      return this.dx == Client.KEEP_ALIVE;
   }

   public DH(PacketContainer var1) {
      this.dx = var1;
      this.dx = var1.getType();
      this.timeStamp = System.currentTimeMillis();
   }

   public boolean tV() {
      return this.dx == Client.ENTITY_ACTION;
   }

   public boolean Tq() {
      return this.dx == Client.USE_ENTITY && this.dx.getEntityUseActions().read(0) == EntityUseAction.ATTACK;
   }

   public boolean tU() {
      return this.dx == Client.TRANSACTION;
   }

   public boolean qa() {
      return this.dx == Server.POSITION;
   }

   public boolean iK() {
      return this.dx == Client.SET_CREATIVE_SLOT;
   }

   public boolean i2() {
      return this.dx == Client.ARM_ANIMATION;
   }

   public boolean Wx() {
      return this.dx == Server.TRANSACTION;
   }

   public boolean y() {
      return this.dx == Client.ABILITIES;
   }

   public boolean M3() {
      return this.dx == Client.FLYING || this.dx == Client.POSITION || this.dx == Client.POSITION_LOOK || this.dx == Client.LOOK;
   }
}
