package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class DP extends PacketCheck {
   private static String[] 3T;

   public DP(UC var1) {
      super(var1, CheckType.PACKET, 3T[0], Category.WORLD);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().tr() && !this.Aa.dx().QJ() && this.Aa.dx().cO() >= 20 && this.Aa.dx().yV() >= 20) {
         double var3;
         ES var5;
         if ((var3 = (var5 = this.Aa.dx()).Ch()) > 0.0 && var5.cO() && !var5.tk()) {
            this.og(3T[2].concat(String.valueOf(var3)));
         }
      }
   }

   static {
      Wm();
   }

   private static void Wm() {
      3T = new String[]{"BB", "Checks for invalid ground status", "Delta Y: "};
   }

   @Override
   public String sB() {
      return 3T[1];
   }
}
