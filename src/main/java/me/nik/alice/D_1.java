package me.nik.alice;

import java.lang.reflect.InvocationTargetException;
import java.nio.charset.StandardCharsets;
import java.util.List;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.plugin.messaging.PluginMessageListener;

public class D implements Listener, PluginMessageListener {
   private static String[] Qu;
   private long x;
   private final Alice plugin;
   private static final String b = w.v() ? Qu[6] : Qu[7];

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   @EventHandler(
      priority = EventPriority.HIGHEST,
      ignoreCancelled = true
   )
   public void dx(PlayerJoinEvent var1) {
      Player var5 = var1.getPlayer();

      IllegalAccessException var10000;
      label33: {
         String[] var8;
         byte var10002;
         try {
            var6 = var5.getClass();
            var8 = Qu;
            var10002 = 0;
         } catch (InvocationTargetException | IllegalAccessException var4) {
            var10000 = var4;
            boolean var10001 = false;
            break label33;
         }

         Object[] var10003;
         try {
            var7 = TY.dx(var6, var8[var10002], new Class[]{String.class});
            var10 = var5;
            var12 = new Object[1];
            var10003 = var12;
         } catch (InvocationTargetException | IllegalAccessException var3) {
            var10000 = var3;
            boolean var9 = false;
            break label33;
         }

         var10003[0] = b;

         try {
            var7.invoke(var10, var12);
            return;
         } catch (InvocationTargetException | IllegalAccessException var2) {
            var10000 = var2;
            boolean var11 = false;
         }
      }

      var10000.printStackTrace();
   }

   public D(Alice var1) {
      this.plugin = var1;
      var1.getServer().getMessenger().registerIncomingPluginChannel(var1, b, this);
   }

   public void onPluginMessageReceived(String var1, Player var2, byte[] var3) {
      if (l.og(this.x) >= 2000L) {
         this.x = System.currentTimeMillis();
         var1 = Dq.dC(new String(var3, StandardCharsets.UTF_8).substring(1));
         wf.Aa(this::dx, 40L);
      }
   }

   private static void oJ() {
      Qu = new String[]{"addChannel", "%client%", "%player%", "%client%", "%player%", "%client%", "MC|Brand", "minecraft:brand"};
   }

   static {
      oJ();
   }

   private void dx(Player var1, String var2) {
      UC var3;
      UC var10000 = var3 = this.plugin.dx().dx(var1);

      try {
         var10000.toString();
      } catch (Exception var6) {
         return;
      }

      var3.dC(var2);
      List var4;
      List var10 = var4 = me.nik.alice.UN.dx.x.Aa();

      label45: {
         try {
            var10.hashCode();
         } catch (Exception var7) {
            break label45;
         }

         if (!var4.isEmpty()) {
            for (String var5 : var4) {
               if (var2.toLowerCase().contains(var5.toLowerCase())) {
                  var3.sB(F.b.getMessage().replace(Qu[1], var2));
                  break;
               }
            }
         }
      }

      if (me.nik.alice.UN.dx.gz.dC()) {
         String var9;
         if (me.nik.alice.UN.dx.tm.dC() && !Integer.valueOf(951084891).equals(var2.toUpperCase().hashCode())) {
            var9 = F.Fh.getMessage().replace(Qu[2], var1.getName()).replace(Qu[3], var2);
         } else {
            var9 = F.h5.getMessage().replace(Qu[4], var1.getName()).replace(Qu[5], var2);
         }

         Alice.getAPI().sendAlert(var9);
      }
   }
}
