package me.nik.alice;

import java.util.logging.Logger;
import java.util.regex.Pattern;
import org.bukkit.Bukkit;
import org.bukkit.ChatColor;

public final class Dq {
   private static final Pattern dx = Pattern.compile(Dq.eN[6]);
   private static String[] eN;

   static {
      hk();
   }

   private Dq() {
   }

   public static String sB(String var0) {
      return ChatColor.translateAlternateColorCodes('&', var0);
   }

   public static String dC(String var0) {
      return ChatColor.stripColor(dx.matcher(var0).replaceAll(eN[0]));
   }

   public static void GE() {
      Logger var0;
      (var0 = Bukkit.getLogger()).warning(eN[1]);
      var0.warning(eN[2]);
      var0.warning(eN[3]);
      var0.warning(eN[4]);
      var0.warning(eN[5]);
   }

   private static void hk() {
      eN = new String[]{
         "",
         "-=-=-=-=-=-=-=-=-=-=- Alice -=-=-=-=-=-=-=-=-=-=-",
         "There has been an error, If this is the first time",
         "That you see this Error you can simply ignore it.",
         "If not, Please provide the following error to Nik.",
         "-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-=-",
         "(?i)&[0-9A-FK-OR]"
      };
   }
}
