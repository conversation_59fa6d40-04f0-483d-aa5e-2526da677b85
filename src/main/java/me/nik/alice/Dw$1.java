package me.nik.alice;

import org.bukkit.Material;

class Dw$1 {
   static final int[] Aa = new int[Material.values().length];

   static {
      try {
         Aa[Material.PAPER.ordinal()] = 1;
      } catch (NoSuchFieldError var2) {
      }

      int[] var10000 = Aa;

      try {
         var10000[Material.BARRIER.ordinal()] = 2;
      } catch (NoSuchFieldError var1) {
      }

      try {
         Aa[Material.BOOK.ordinal()] = 3;
      } catch (NoSuchFieldError var0) {
      }
   }
}
