package me.nik.alice;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;
.1;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.Check;
import org.apache.commons.lang.WordUtils;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

public class Dw extends EJ {
   private static String[] Vm;
   private static final List og;

   public Dw(QZ var1, Alice var2) {
      super(var1, var2);
   }

   @Override
   protected int og() {
      return 54;
   }

   private static void eb() {
      Vm = new String[]{
         "&cCombat Checks",
         "You do not have permission to enable or disable checks!",
         "&6",
         "&6",
         " (",
         ")",
         "",
         "&8\u00bb &7Enabled: ",
         "",
         "&8\u00bb &7Description:",
         "&f",
         "",
         "&fClick to toggle this check"
      };
   }

   @Override
   protected void Ch() {
      this.F7();
      ArrayList var1 = new ArrayList();
      Iterator var2 = og.iterator();

      while (var2.hasNext()) {
         Check var3;
         String var4 = (var3 = (Check)var2.next()).dC().isEmpty() ? Vm[2] + var3.AC() : Vm[3] + var3.AC() + Vm[4] + var3.dC() + Vm[5];
         ArrayList var5;
         (var5 = new ArrayList()).add(Vm[6]);
         var5.add(Vm[7] + this.dx(var3));
         var5.add(Vm[8]);
         var5.add(Vm[9]);

         for (String var8 : var10 = WordUtils.wrap(var3.sB(), 35).split(System.lineSeparator())) {
            var5.add(Vm[10].concat(String.valueOf(var8)));
         }

         var5.add(Vm[11]);
         var5.add(Vm[12]);
         var1.add(this.dx(Material.PAPER, 1, var4, var5));
      }

      if (!var1.isEmpty()) {
         for (int var9 = 0; var9 < super.jD; var9++) {
            this.index = super.jD * this.DB + var9;
            if (this.index >= var1.size()) {
               break;
            }

            if (var1.get(this.index) != null) {
               this.dx.addItem(new ItemStack[]{(ItemStack)var1.get(this.index)});
            }
         }
      }
   }

   // $VF: Unable to simplify switch on enum
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   @Override
   public void dx(InventoryClickEvent var1) {
      boolean var3 = tJ5O;
      Player var2 = (Player)var1.getWhoClicked();
      ItemStack var4;
      if ((var4 = var1.getCurrentItem()).getItemMeta().hasDisplayName()) {
         switch (1.Aa[var4.getType().ordinal()]) {
            case 1:
               if (!var2.hasPermission(ZM.tm.h5())) {
                  var2.sendMessage(F.dx.getMessage() + Vm[1]);
                  return;
               }

               this.dx(var4);
               this.getInventory().clear();
               this.Ch();
               return;
            case 2:
               var2.closeInventory();
               new AH(this.dx, this.plugin).iv();
               return;
            case 3:
               String var5 = ChatColor.stripColor(var4.getItemMeta().getDisplayName());
               byte var6 = -1;
               switch (var5.hashCode()) {
                  case -1133036644:
                     if (Integer.valueOf(-1133036644).equals(var5.hashCode())) {
                        var6 = 1;
                     }
                     break;
                  case 473267736:
                     if (Integer.valueOf(473267736).equals(var5.hashCode())) {
                        var6 = 0;
                        if (var3) {
                           throw null;
                        }
                     }
               }

               switch (var6) {
                  case 0:
                     if (this.DB != 0) {
                        this.DB--;
                        super.iv();
                        return;
                     }
                     break;
                  case 1:
                     this.DB++;
                     super.iv();
               }
         }
      }
   }

   static {
      eb();
      GK var0;
      (var0 = new GK(null)).DB();
      og = (List)var0.dC().stream().filter(Dw::dx).collect(Collectors.toList());
   }

   private static boolean dx(Check var0) {
      return var0.dx() == Category.COMBAT;
   }

   @Override
   protected String yM() {
      return Dq.sB(Vm[0]);
   }
}
