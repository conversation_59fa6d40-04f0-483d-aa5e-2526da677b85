package me.nik.alice;

import java.util.ArrayDeque;
import java.util.Deque;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientTransaction;
import me.nik.alice.wrappers.WrapperPlayServerTransaction;

public class E extends PacketCheck {
   private static String[] Dj;
   private final Deque dx = new ArrayDeque();

   private static void Ys() {
      Dj = new String[]{"J", "Checks for transaction disablers", "Disabler, ID: "};
   }

   public E(UC var1) {
      super(var1, CheckType.PACKET, Dj[0], Category.WORLD, 10.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.Wx()) {
         WrapperPlayServerTransaction var4;
         if ((var4 = new WrapperPlayServerTransaction(var1.dx())).getWindowId() == 0) {
            this.dx.add(var4.getActionNumber());
         }
      } else {
         if (var1.tU()) {
            WrapperPlayClientTransaction var2;
            if ((var2 = new WrapperPlayClientTransaction(var1.dx())).getWindowId() != 0) {
               return;
            }

            short var3 = var2.getActionNumber();
            if (!this.dx.contains(var3)) {
               this.Aa(Dj[2].concat(String.valueOf(var3)));
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.25);
               this.dx.remove(var3);
            }
         }
      }
   }

   static {
      Ys();
   }

   @Override
   public String sB() {
      return Dj[1];
   }
}
