package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class EG extends PacketCheck {
   private static String[] Fl;

   static {
      Rm();
   }

   @Override
   public String sB() {
      return Fl[1];
   }

   public EG(UC var1) {
      super(var1, CheckType.KILLAURA, Fl[0], Category.COMBAT, 3.0F);
   }

   private static void Rm() {
      Fl = new String[]{"D", "Checks for multi aura", "Attacked targets: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.Tq()) {
         int var5 = this.Aa.dx().rc();
         long var3;
         boolean var2 = (var3 = this.Aa.dx().Fh()) < 25L || var3 > 100L;
         if (var5 > 1 && !var2) {
            this.Aa(Fl[2].concat(String.valueOf(var5)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.25);
         }
      }
   }
}
