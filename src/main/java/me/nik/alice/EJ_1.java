package me.nik.alice;

import org.bukkit.Material;

public abstract class EJ extends lk {
   private static String[] k2;
   protected int index;
   protected int jD;
   protected int DB = 0;

   private static void py() {
      k2 = new String[]{"&6Previous Page", "&cExit", "&6Next Page"};
   }

   public EJ(QZ var1, Alice var2) {
      super(var1, var2);
      this.jD = 45;
      this.index = 0;
   }

   static {
      py();
   }

   public void F7() {
      this.dx.setItem(48, this.dx(Material.BOOK, 1, k2[0], null));
      this.dx.setItem(49, this.dx(Material.BARRIER, 1, k2[1], null));
      this.dx.setItem(50, this.dx(Material.BOOK, 1, k2[2], null));
   }
}
