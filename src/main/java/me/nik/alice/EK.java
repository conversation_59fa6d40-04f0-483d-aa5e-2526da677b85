package me.nik.alice;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

public class EK {
   private boolean running;
   private final Queue dx = new ConcurrentLinkedQueue();

   public Queue dx() {
      return this.dx;
   }

   public boolean isRunning() {
      return this.running;
   }

   public void Zp() {
      this.dx.clear();
   }

   public void Aa(boolean var1) {
      this.running = var1;
   }

   public boolean dx(String var1) {
      if (this.dx.contains(var1)) {
         return false;
      } else {
         this.dx.add(var1);
         return true;
      }
   }
}
