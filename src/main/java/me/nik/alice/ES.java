package me.nik.alice;

import com.comphenix.protocol.events.PacketContainer;
import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import me.nik.alice.wrappers.WrapperPlayClientLook;
import me.nik.alice.wrappers.WrapperPlayClientPosition;
import me.nik.alice.wrappers.WrapperPlayClientPositionLook;
import me.nik.fastmath.FastMath;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.data.Waterlogged;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.entity.Vehicle;

public class ES {
   private boolean a;
   private int N7;
   private Location og;
   private float h5;
   private Location AC;
   private boolean tk;
   private int Sx;
   private int IK;
   private int Zb;
   private int Zh;
   private int cf;
   private double cO;
   private int qJ;
   private boolean yV;
   private double jA;
   private boolean Zp;
   private int sp;
   private double jD;
   private double Zm;
   private int EZ;
   private double h5;
   private int Vl;
   private double a;
   private int k0;
   private int rr;
   private final UC Aa;
   private int F7;
   private int qx;
   private boolean Qt;
   private int qa;
   private double tk;
   private boolean bI;
   private double yV;
   private int nA;
   private final vR dx;
   private int i2;
   private List UH = new ArrayList();
   private float Zh;
   private int d;
   private int hW;
   private int y;
   private int Tq;
   private boolean DB;
   private boolean Zm;
   private boolean cO;
   private int GD;
   private double rc;
   private boolean rc;
   private final QU dx;
   private double b;
   private double bI;
   private int CS;
   private int PP;
   private boolean Eu;
   private final kf dx;
   private double Qt;
   private int v;
   private static String[] 9R;
   private boolean jA;
   private boolean jD;

   public int nZ() {
      return this.EZ;
   }

   public int pQ() {
      return this.Sx;
   }

   public void pQ() {
      this.i2 = 0;
      this.Vl++;
   }

   public boolean nZ() {
      return this.DB;
   }

   public int DP() {
      return this.Zh;
   }

   public int QJ() {
      return this.y;
   }

   public boolean Q() {
      return this.a;
   }

   public boolean Eu() {
      return this.rc;
   }

   public float dC() {
      return this.Zh;
   }

   public double x() {
      return this.Qt;
   }

   public int a() {
      return this.GD;
   }

   public int Q() {
      return this.d;
   }

   public boolean cO() {
      return this.Zp;
   }

   public int tr() {
      return this.Tq;
   }

   public void AC(DH var1) {
      Player var2;
      Player var10000 = var2 = this.Aa.getPlayer();

      try {
         var10000.toString();
      } catch (Exception var12) {
         return;
      }

      World var22 = var2.getWorld();
      PacketContainer var3 = var1.dx();
      if (var1.P()) {
         WrapperPlayClientPosition var19;
         double var32 = (var19 = new WrapperPlayClientPosition(var3)).getX();
         double var34 = var19.getY();
         double var36 = var19.getZ();
         float var28 = this.Aa().getYaw();
         float var30 = this.Aa().getPitch();
         boolean var20 = var19.getOnGround();
         this.DB = this.Zp;
         this.Zp = var20;
         if (var20) {
            this.F7 = 0;
            this.IK++;
         } else {
            this.F7++;
            this.IK = 0;
         }

         Location var21 = new Location(var22, var32, var34, var36, var28, var30);
         Location var25 = this.Aa() != null ? this.Aa() : var21;
         this.dx(var25, var21);
      } else if (var1.PU()) {
         WrapperPlayClientPositionLook var16;
         double var31 = (var16 = new WrapperPlayClientPositionLook(var3)).getX();
         double var33 = var16.getY();
         double var35 = var16.getZ();
         float var27 = var16.getYaw();
         float var29 = var16.getPitch();
         boolean var17 = var16.getOnGround();
         this.DB = this.Zp;
         this.Zp = var17;
         if (var17) {
            this.F7 = 0;
            this.IK++;
         } else {
            this.F7++;
            this.IK = 0;
         }

         Location var18 = new Location(var22, var31, var33, var35, var27, var29);
         Location var24 = this.Aa() != null ? this.Aa() : var18;
         this.dx(var24, var18);
      } else {
         if (var1.xx()) {
            WrapperPlayClientLook var13 = new WrapperPlayClientLook(var3);
            double var6 = this.Aa().getX();
            double var8 = this.Aa().getY();
            double var10 = this.Aa().getZ();
            float var26 = var13.getYaw();
            float var4 = var13.getPitch();
            boolean var14 = var13.getOnGround();
            this.DB = this.Zp;
            this.Zp = var14;
            if (var14) {
               this.F7 = 0;
               this.IK++;
            } else {
               this.F7++;
               this.IK = 0;
            }

            Location var15 = new Location(var22, var6, var8, var10, var26, var4);
            Location var23 = this.Aa() != null ? this.Aa() : var15;
            this.dx(var23, var15);
         }
      }
   }

   public double Qt() {
      return this.cO;
   }

   public Location og() {
      Location var10000 = this.AC;

      try {
         var10000.hashCode();
      } catch (Exception var1) {
         this.AC = this.Aa.getPlayer().getLocation();
      }

      return this.AC;
   }

   public double Zp() {
      return this.b;
   }

   public double Ch() {
      return this.jA;
   }

   public QU dx() {
      return this.dx;
   }

   public double F7() {
      return this.yV;
   }

   public double rc() {
      return this.tk;
   }

   public boolean a() {
      return this.tk;
   }

   public int Sh() {
      return this.N7;
   }

   private void Sh() {
      if (this.k0++ >= 2) {
         this.UH = JA.dx(this.Aa.getPlayer(), 2.0);
         this.rc = l.dx(this.UH, ES::dx);
         this.k0 = 0;
      }
   }

   public double zP() {
      return this.rc;
   }

   public double DB() {
      return this.a;
   }

   public int u() {
      return this.F7;
   }

   public double hW() {
      return this.Zm;
   }

   public boolean u() {
      return this.Qt;
   }

   public boolean rc() {
      return this.jA;
   }

   public boolean QJ() {
      return this.cO;
   }

   public int o4() {
      return this.qx;
   }

   public boolean yV() {
      return this.Eu;
   }

   public List b() {
      return this.UH;
   }

   public int R() {
      return this.CS;
   }

   public double iv() {
      return this.bI;
   }

   public vR dx() {
      return this.dx;
   }

   public int E() {
      return this.Zb;
   }

   public int yk() {
      return this.rr;
   }

   public Location Aa() {
      Location var10000 = this.og;

      try {
         var10000.hashCode();
      } catch (Exception var1) {
         this.og = this.Aa.getPlayer().getLocation();
      }

      return this.og;
   }

   public float Zh() {
      return this.h5;
   }

   public int xx() {
      return this.Vl;
   }

   private void dx(Location var1, Location var2) {
      this.AC = var1;
      this.og = var2;
      double var3 = this.jD;
      double var5 = var2.getX() - var1.getX();
      this.Qt = var3;
      this.jD = var5;
      double var7 = this.jA;
      double var9 = var2.getY() - var1.getY();
      this.bI = var7;
      this.jA = var9;
      double var11 = this.cO;
      double var13 = Math.abs(var7 - var9);
      this.tk = var11;
      this.cO = var13;
      double var15 = this.rc;
      double var17 = var2.getZ() - var1.getZ();
      this.Zm = var15;
      this.rc = var17;
      double var19 = this.yV;
      double var21 = FastMath.hypot(var5, var17);
      this.b = var19;
      this.yV = var21;
      double var23 = this.a;
      double var25 = Math.abs(var19 - var21);
      this.h5 = var23;
      this.a = var25;
      this.sB(var2);
   }

   private void sB(Location var1) {
      if (this.yV != 0.0 || this.jA != 0.0) {
         Player var2;
         Player var10000 = var2 = this.Aa.getPlayer();

         try {
            var10000.toString();
         } catch (Exception var4) {
            return;
         }

         this.EZ = fD.sB(var1) ? 0 : this.EZ + 1;
         if (this.EZ != 0) {
            this.Sh();
            this.AC(var1);
            if (this.Vl > 1) {
               this.dx.dC(var1);
            }

            this.dx.yk();
            boolean var3 = this.jD;
            boolean var5 = var1.getY() % 0.015625 < 1.0E-4;
            this.Qt = var3;
            this.jD = var5;
            this.CS = var5 ? this.CS + 1 : 0;
            this.d = var5 ? 0 : this.d + 1;
            this.GD = !this.Zp && !this.jD ? this.GD + 1 : 0;
            this.dx.AC(var2);
            float var6 = var2.getFallDistance();
            this.h5 = this.Zh;
            this.Zh = var6;
         }
      }
   }

   public int tk() {
      return this.IK;
   }

   public kf dx() {
      return this.dx;
   }

   private void AC(Location var1) {
      this.yV = fD.dx(var1);
      boolean var2 = false;
      boolean var3 = false;
      boolean var4 = false;
      boolean var5 = false;
      boolean var6 = false;
      boolean var7 = false;
      boolean var8 = false;
      boolean var9 = false;
      boolean var10 = false;
      boolean var11 = false;
      boolean var12 = false;
      boolean var13 = false;
      boolean var14 = false;
      boolean var15 = false;
      boolean var16 = false;
      boolean var17 = false;
      boolean var18 = false;
      boolean var19 = false;
      boolean var20 = false;
      Iterator var21 = fD.dx(var1, 1).iterator();

      while (var21.hasNext()) {
         Block var22;
         String var23 = (var22 = (Block)var21.next()).getType().toString();
         if (!var20) {
            var20 = !w.v()
               && (
                  Integer.valueOf(-1922487510).equals(var23.hashCode())
                     || Integer.valueOf(1496991257).equals(var23.hashCode())
                     || Integer.valueOf(1661554375).equals(var23.hashCode())
                     || var22.getBlockData() instanceof Waterlogged && ((Waterlogged)var22.getBlockData()).isWaterlogged()
               );
         }

         if (!var2) {
            var2 = var23.contains(9R[0]) || Integer.valueOf(2555596).equals(var23.hashCode()) || var23.contains(9R[1]) || var23.contains(9R[2]);
         }

         if (!var3) {
            var3 = var2 || var23.contains(9R[3]) || var23.contains(9R[4]);
         }

         if (!var5) {
            var5 = var23.contains(9R[5]);
         }

         if (!var6) {
            var6 = var23.contains(9R[6]);
         }

         if (!var7) {
            var7 = Integer.valueOf(975296534).equals(var23.hashCode());
         }

         if (!var8) {
            var8 = Integer.valueOf(85812).equals(var23.hashCode()) || Integer.valueOf(1993163294).equals(var23.hashCode());
         }

         if (!var9) {
            var9 = Integer.valueOf(-2057029598).equals(var23.hashCode()) || var23.contains(9R[7]);
         }

         if (!var10) {
            var10 = Integer.valueOf(942687056).equals(var23.hashCode());
         }

         if (!var11) {
            var11 = var23.contains(9R[8]);
         }

         if (!var12) {
            var12 = var23.contains(9R[9]);
         }

         if (!var13) {
            var13 = !w.v() && Integer.valueOf(2037637705).equals(var23.hashCode());
         }

         if (!var14) {
            var14 = !w.v() && Integer.valueOf(1670807136).equals(var23.hashCode());
         }

         if (!var15) {
            var15 = !w.v() && Integer.valueOf(1147342860).equals(var23.hashCode());
         }

         if (!var18) {
            var18 = var23.contains(9R[10]);
         }

         if (!var16) {
            var16 = var23.contains(9R[11]);
         }

         if (!var17) {
            var17 = Integer.valueOf(-2048088471).equals(var23.hashCode());
         }

         if (!var4) {
            var4 = Integer.valueOf(82365687).equals(var23.hashCode())
               || Integer.valueOf(-1010548308).equals(var23.hashCode())
               || Integer.valueOf(2329312).equals(var23.hashCode())
               || Integer.valueOf(1214000011).equals(var23.hashCode());
         }

         if (!var19) {
            var19 = Integer.valueOf(942700363).equals(var23.hashCode());
         }
      }

      this.Eu = var20;
      this.nA = !var2 && !fD.AC(var1) ? this.nA + 1 : 0;
      if (!var3) {
         var3 = this.nA == 0 || fD.Aa(var1);
      }

      this.Vl = var3 ? this.Vl + 1 : 0;
      this.i2 = var3 ? 0 : this.i2 + 1;
      this.rr = !var5 && !fD.og(var1) ? this.rr + 1 : 0;
      this.qx = var6 ? this.qx + 1 : 0;
      this.qa = var6 ? 0 : this.qa + 1;
      this.y = var4 ? this.y + 1 : 0;
      this.cf = var4 ? 0 : this.cf + 1;
      this.Tq = var7 ? this.Tq + 1 : 0;
      this.v = var7 ? 0 : this.v + 1;
      this.hW = var8 ? this.hW + 1 : 0;
      this.N7 = var8 ? 0 : this.N7 + 1;
      this.Zh = var9 ? this.Zh + 1 : 0;
      this.Sx = var9 ? 0 : this.Sx + 1;
      this.sp = var10 ? 0 : this.sp + 1;
      this.PP = var11 ? 0 : this.PP + 1;
      this.Zm = var12;
      this.jA = var13;
      this.bI = var14;
      this.qJ = var15 ? 0 : this.qJ + 1;
      this.a = var18;
      this.Zb = var17 ? 0 : this.Zb + 1;
      this.cO = var16;
      this.tk = var19;
   }

   public int yV() {
      return this.PP;
   }

   public int pO() {
      return this.hW;
   }

   public int P() {
      return this.cf;
   }

   private static boolean dx(Entity var0) {
      return var0 instanceof Vehicle;
   }

   public int M3() {
      return this.i2;
   }

   public int R2() {
      return this.v;
   }

   public double jD() {
      return this.h5;
   }

   public boolean jA() {
      return this.Zm;
   }

   public boolean Zm() {
      return this.bI;
   }

   public boolean bI() {
      return this.Qt && this.DB && !this.jD && !this.Zp && this.jA > 0.0;
   }

   public ES(UC var1) {
      this.Aa = var1;
      this.dx = new QU();
      this.dx = new vR(var1);
      this.dx = new kf(var1);
   }

   public int bI() {
      return this.sp;
   }

   public int p6() {
      return this.qa;
   }

   public boolean tk() {
      return this.jD;
   }

   public double tm() {
      return this.jD;
   }

   public boolean Qt() {
      return this.yV;
   }

   static {
      rQ();
   }

   public int cO() {
      return this.nA;
   }

   public int Eu() {
      return this.qJ;
   }

   private static void rQ() {
      9R = new String[]{"SLAB", "CARPET", "PLANT", "SNOW", "LILY", "TRAP", "ICE", "VINE", "STAIRS", "PISTON", "BED", "SHULKER"};
   }
}
