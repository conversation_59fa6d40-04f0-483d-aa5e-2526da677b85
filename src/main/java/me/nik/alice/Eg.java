package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientVehicleMove;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Horse;

public class Eg extends PacketCheck {
   private int F7;
   private double VL;
   private static String[] yw;

   @Override
   public void dx(DH var1) {
      if (var1.Sh() && !this.Aa.dx().WB()) {
         Entity var2;
         Entity var10000 = var2 = this.Aa.getPlayer().getVehicle();

         try {
            var10000.getClass();
         } catch (Exception var13) {
            return;
         }

         boolean var3 = var2 instanceof Horse;
         double var5 = new WrapperPlayClientVehicleMove(var1.dx()).getY();
         double var7 = this.VL;
         this.VL = var5;
         double var9 = var5 - var7;
         double var11 = var3 ? 0.5 : 0.2;
         if (var3 && this.Aa.dx().zP() < 100) {
            var11 += 5.0;
         }

         this.F7 = !var2.isOnGround() ? this.F7 + 1 : 0;
         if (var9 > var11 && this.F7 > 3) {
            this.Aa(yw[2] + this.F7 + yw[3] + var2.getType() + yw[4] + var9);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.125);
         }
      }
   }

   public Eg(UC var1) {
      super(var1, CheckType.VEHICLE, yw[1], Category.MOVE, 3.0F);
   }

   private static void dc() {
      yw = new String[]{"Checks for vehicle flight", "A", "Fly ticks: ", " vehicle: ", " delta Y: "};
   }

   static {
      dc();
   }

   @Override
   public String sB() {
      return yw[0];
   }
}
