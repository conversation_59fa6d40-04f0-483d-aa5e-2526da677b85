package me.nik.alice;

import java.util.function.Predicate;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientInstanceOfFlying;
import me.nik.fastmath.FastMath;

public class Ej extends PacketCheck {
   private final Predicate og;
   private static String[] j8;
   private final Predicate Aa = Ej::broadCast;

   private static boolean Aa(Float var0) {
      return Float.isNaN(var0) || Float.isInfinite(var0) || var0 == Float.MAX_VALUE || var0 == Float.MIN_VALUE;
   }

   private static void Af() {
      j8 = new String[]{"X", "Checks for dangerous position packets", "X: ", " Y: ", " Z: ", " Yaw: ", " Pitch: ", "Horizontal: ", " Vertical: "};
   }

   @Override
   public void dx(DH var1) {
      boolean var21 = dW29;
      if (var1.M3()) {
         WrapperPlayClientInstanceOfFlying var22;
         double var3 = (var22 = new WrapperPlayClientInstanceOfFlying(var1.dx())).getX();
         double var5 = var22.getY();
         double var7 = var22.getZ();
         float var2 = var22.getYaw();
         float var23 = var22.getPitch();
         boolean var9 = false;
         if (var3 != 0.0 && var5 != 0.0 && var7 != 0.0) {
            boolean var10000;
            if (!this.Aa.test(var3) && !this.Aa.test(var5) && !this.Aa.test(var7)) {
               var10000 = false;
            } else {
               var10000 = true;
               if (var21) {
                  throw null;
               }
            }

            if (var10000) {
               var9 = true;
            }
         }

         if (!var9 && var2 != 0.0F && var23 != 0.0F) {
            boolean var26;
            if (!this.og.test(var2) && !this.og.test(var23)) {
               var26 = false;
            } else {
               var26 = true;
               if (var21) {
                  throw null;
               }
            }

            if (var26) {
               var9 = true;
            }
         }

         if (var9) {
            this.og(j8[2] + var3 + j8[3] + var5 + j8[4] + var7 + j8[5] + var2 + j8[6] + var23);
            this.Aa.sB(F.sX.getMessage());
         }

         ES var10;
         if (!((var10 = this.Aa.dx()).dC() > 20.0F)
            && this.Aa.dx().Vm() >= 20
            && this.Aa.dx().gz() >= 60
            && this.Aa.AC() >= 10000L
            && this.Aa.dx().UH() >= 80
            && this.Aa.dx().Ch() >= 100
            && this.Aa.dx().sX() >= 40) {
            Qk var24 = this.Aa.dx();
            MN var25 = this.Aa.dx();
            double var15 = Math.abs(var10.F7());
            double var17 = Math.abs(var10.Ch());
            double var27 = 5.0 + (double)var10.dx().sB(2000L) + (double)var10.dx().og(2000L);
            double var10001;
            if (this.Aa.dx().WB() < 60) {
               var10001 = 3.5;
               if (var21) {
                  throw null;
               }
            } else {
               var10001 = 0.0;
            }

            var27 = var27 + var10001 + (double)(FastMath.max(0.2F, this.Aa.getPlayer().getWalkSpeed()) - 0.2F);
            if (var24.tr()) {
               var10001 = Math.abs(var24.getVelocityY() + var24.cO());
               if (var21) {
                  throw null;
               }
            } else {
               var10001 = 0.0;
            }

            var27 += var10001;
            if (var24.Wx() < 60) {
               var10001 = 50.0;
               if (var21) {
                  throw null;
               }
            } else {
               var10001 = 0.0;
            }

            var27 += var10001;
            if (var25.GE() < 40) {
               var10001 = Math.abs(var25.Zm() + var25.jA());
               if (var21) {
                  throw null;
               }
            } else {
               var10001 = 0.0;
            }

            double var19 = var27 + var10001;
            if (!(var15 >= var19) && !(var17 >= var19)) {
               this.Aa(0.025);
            } else {
               this.Aa(j8[7] + var15 + j8[8] + var17);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            }
         }
      }
   }

   static {
      Af();
   }

   public Ej(UC var1) {
      super(var1, CheckType.PACKET, j8[0], Category.WORLD, 1.0F);
      this.og = Ej::Aa;
   }

   private static boolean dx(Double var0) {
      return Double.isNaN(var0) || Double.isInfinite(var0) || var0 == Double.MAX_VALUE || var0 == Double.MIN_VALUE;
   }

   @Override
   public String sB() {
      return j8[1];
   }
}
