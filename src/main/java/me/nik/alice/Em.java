package me.nik.alice;

public class Em {
   private boolean WB;
   private static String[] Jd;
   private boolean Vm;
   private final UC Aa;
   private boolean sX;
   private boolean gz;
   private boolean yM;
   private boolean UH;
   private boolean tm;

   public boolean yM() {
      return this.tm || this.Aa.dx().o4();
   }

   static {
      yg();
   }

   public Em(UC var1) {
      this.Aa = var1;
   }

   public boolean Fh() {
      return this.UH || this.Aa.dx().o4();
   }

   public boolean VL() {
      return this.sX || this.Aa.dx().tr() || this.Aa.dx().o4() || this.Aa.dx().Zp() < me.nik.alice.UN.dx.Eu.Aa();
   }

   public boolean WB() {
      return this.Vm || this.Aa.dx().GE() < 20 || this.Aa.dx().Zp() < me.nik.alice.UN.dx.Eu.Aa();
   }

   public boolean sX() {
      return this.WB || this.Aa.dx().GE() < 20 || this.Aa.dx().Zp() < me.nik.alice.UN.dx.Eu.Aa();
   }

   public void Qt() {
      Qk var1 = this.Aa.dx();
      ES var2 = this.Aa.dx();
      uq var3 = this.Aa.dx();
      MN var4 = this.Aa.dx();
      qn var5 = this.Aa.dx();
      Fc var6 = this.Aa.dx();
      double var7;
      double var9 = (var7 = var1.tr() ? Math.abs(var1.cO() + var1.getVelocityY()) : 0.0) + 0.5;
      double var11 = var7 + 0.8;
      boolean var18 = var2.Ch() <= var9 && var2.F7() <= var9;
      boolean var21 = var2.Ch() < var11 && var2.F7() < var11;
      int var10 = var2.dx().AC(5000L);
      double var16 = 0.8 + (var10 > 0 ? 0.75 * (double)var10 : 0.0) + var7;
      boolean var20 = var2.Ch() < var16 && var2.F7() < var16;
      boolean var8 = var3.WB() < 100;
      boolean var22 = var3.sX() < 80
         || var3.Fh() < 20
         || var3.F7() < 20
         || var3.Ch() < 20
         || var2.dx().p6() && var18
         || var2.Sh() < 20 && var18
         || var3.Zp() < me.nik.alice.UN.dx.Eu.Aa()
         || var2.Eu()
         || var2.nZ() < 60 && var21
         || var2.pQ() < 20 && var18
         || var2.P() < 25 && var20 && !var8
         || var3.Vm() < 20;
      boolean var23 = w.Sx() && (var2.dx().dx(1250L) > 0 || var3.gz() < 80);
      boolean var19 = !w.v()
         && (
            var8
               || var2.E() < 60
               || var2.rc() && var18
               || var2.QJ() && var18
               || var2.yV() && var20
               || var2.dx().Aa(1250L) > 0 && var18
               || var2.Eu() < 40 && var21
               || var2.Zm() && var18
         );
      this.UH = var22 || var23 || var19;
      this.yM = var2.Sh() < 10
         || var3.gz() < 10
         || var2.yk() < 20
         || var2.Qt()
         || var4.o4()
         || var2.dx().Aa(2500L) > 0
         || var3.Zp() < 20
         || JA.dx(this.Aa.getPlayer(), Jd[0])
         || var2.yV()
         || var2.P() < 10;
      this.sX = var3.Vm() < 40
         || var2.E() < 20
         || var3.gz() < 60
         || var3.WB() < 60
         || var2.yV()
         || JA.sB(this.Aa.getPlayer())
         || var3.sX() < 40
         || var2.yk() < 20
         || var2.Eu();
      this.gz = var3.F7() || var3.Zp() || var3.dx(2) || var3.x() < 20 || var3.yM() < 20;
      this.tm = var6.Qt() > 3 || var5.tV() < 3 || Math.abs(var5.getPitch()) == 90.0F || var3.Vm() < 10;
      this.WB = !w.Sx()
         || !this.Aa.getPlayer().isGliding()
         || var3.WB() < 60
         || var2.P() < 60
         || var2.nZ() < 40 && var2.F7() < 9.0 && var2.Ch() < 9.0
         || var2.pQ() < 40;
      this.Vm = !w.Sx() || var2.jA();
   }

   public boolean UH() {
      return this.gz;
   }

   private static void yg() {
      Jd = new String[]{"NETHERITE"};
   }

   public boolean b() {
      return this.yM;
   }
}
