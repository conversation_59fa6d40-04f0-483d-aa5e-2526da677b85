package me.nik.alice;

enum Ey$dx {
   dx(9),
   Aa(8),
   og(12),
   AC(6),
   sB(4),
   dC(2);

   private final int YL;
   private static final Ey$dx[] dx;
   private static String[] 5w;

   private Ey$dx(int var3) {
      this.YL = var3;
   }

   private int K() {
      return this.YL;
   }

   static int dx(Ey$dx var0) {
      return var0.K();
   }

   static {
      bz();
      dx = new Ey$dx[]{dx, Aa, og, AC, sB, dC};
   }

   private static void bz() {
      5w = new String[]{"NETHERITE", "DIAMOND", "GOLD", "IRON", "STONE", "WOOD"};
   }
}
