package me.nik.alice;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffectType;

public final class Ey {
   private static String[] Xh;
   private static final Map sB = new HashMap();

   private static Material[] dx(int var0) {
      return new Material[var0];
   }

   public static long dx(Block var0, Player var1) {
      String var2 = var0.getType().toString();
      if (!sB.containsKey(var2)) {
         return 0L;
      } else {
         ItemStack var3 = JA.dx(var1);
         boolean var9 = !var0.getDrops(var3).isEmpty();
         double var5 = (double)((Float)sB.get(var2)).floatValue() * (var9 ? 1.5 : 5.0);
         double var7 = (double)dx(var3);
         if (var3.containsEnchantment(Enchantment.DIG_SPEED)) {
            int var10 = var3.getEnchantmentLevel(Enchantment.DIG_SPEED);
            var7 += (double)((float)(var10 * var10 + 1));
         }

         if (var1.hasPotionEffect(PotionEffectType.FAST_DIGGING)) {
            var7 *= 1.0 + 0.2 * (double)JA.dx(var1, PotionEffectType.FAST_DIGGING);
         }

         if (var1.hasPotionEffect(PotionEffectType.SLOW_DIGGING)) {
            var7 /= (double)(3 ^ JA.dx(var1, PotionEffectType.SLOW_DIGGING));
         }

         double var10001 = var5 / var7;
         return (long)(var5 / var7 * 1000.0);
      }
   }

   private Ey() {
   }

   private static void So() {
      Xh = new String[]{
         "SHEARS",
         "SWORD",
         "BED",
         "ICE",
         "CAKE",
         "WEB",
         "SPONGE",
         "WOOL",
         "GLASS",
         "SPAWNER",
         "BAMBOO",
         "COBWEB",
         "HAY_BLOCK",
         "CHEST",
         "DIRT",
         "HAY_BLOCK",
         "MAGMA",
         "PISTON_BASE",
         "PISTON_EXTENSION",
         "PISTON_MOVING_PIECE",
         "PISTON_STICKY_BASE",
         "SAND",
         "SOUL_SAND",
         "CONCRETE_POWDER",
         "TRAP_DOOR",
         "ACACIA_DOOR",
         "BIRCH_DOOR",
         "DARK_OAK_DOOR",
         "JUNGLE_DOOR",
         "OAK_DOOR",
         "SPRUCE_DOOR",
         "COAL_ORE",
         "REDSTONE_ORE",
         "QUARTZ_ORE",
         "LAPIS_ORE",
         "IRON_ORE",
         "GOLD_ORE",
         "EMERALD_ORE",
         "DIAMOND_ORE",
         "BEACON",
         "GOLD_BLOCK",
         "HOPPER",
         "LAPIS_BLOCK",
         "OBSERVER",
         "IRON_TRAPDOOR",
         "IRON_DOOR",
         "IRON_DOOR_BLOCK",
         "ANVIL",
         "COAL_BLOCK",
         "DIAMOND_BLOCK",
         "EMERALD_BLOCK",
         "ENCHANTMENT_TABLE",
         "IRON_BARDING",
         "IRON_BLOCK",
         "REDSTONE_BLOCK",
         "ACACIA_FENCE",
         "ACACIA_FENCE_GATE",
         "BIRCH_FENCE",
         "BIRCH_FENCE_GATE",
         "DARK_OAK_FENCE",
         "DARK_OAK_FENCE_GATE",
         "JUNGLE_FENCE",
         "JUNGLE_FENCE_GATE",
         "FENCE",
         "FENCE_GATE",
         "SPRUCE_FENCE",
         "SPRUCE_FENCE_GATE",
         "LOG",
         "LOG_2",
         "WOOD",
         "ACACIA_STAIRS",
         "SMOOTH_STAIRS",
         "BIRCH_WOOD_STAIRS",
         "BRICK_STAIRS",
         "COBBLESTONE_STAIRS",
         "DARK_OAK_STAIRS",
         "JUNGLE_WOOD_STAIRS",
         "NETHER_BRICK_STAIRS",
         "WOOD_STAIRS",
         "SPRUCE_WOOD_STAIRS",
         "ACACIA_SLAB",
         "PURPUR_SLAB",
         "STONE_SLAB2",
         "SMOOTH_BRICK",
         "BRICK",
         "CAULDRON",
         "COBBLESTONE",
         "COBBLE_WALL",
         "JUKEBOX",
         "MOSSY_COBBLESTONE",
         "NETHER_BRICK",
         "RED_NETHER_BRICK",
         "BLACK_SHULKER_BOX",
         "BLUE_SHULKER_BOX",
         "BROWN_SHULKER_BOX",
         "CYAN_SHULKER_BOX",
         "GRAY_SHULKER_BOX",
         "GREEN_SHULKER_BOX",
         "LIGHT_BLUE_SHULKER_BOX",
         "LIGHT_GRAY_SHULKER_BOX",
         "LIME_SHULKER_BOX",
         "MAGENTA_SHULKER_BOX",
         "ORANGE_SHULKER_BOX",
         "PINK_SHULKER_BOX",
         "PURPLE_SHULKER_BOX",
         "RED_SHULKER_BOX",
         "SHULKER_BOX",
         "WHITE_SHULKER_BOX",
         "YELLOW_SHULKER_BOX",
         "PURPUR_STAIRS",
         "BOOKSHELF",
         "PURPUR_BLOCK",
         "PURPUR_PILLAR",
         "STONE",
         "QUARTZ_STAIRS",
         "RED_SANDSTONE_STAIRS",
         "SANDSTONE_STAIRS",
         "RED_SANDSTONE",
         "SANDSTONE",
         "WOOL",
         "END_BRICKS",
         "NOTE_BLOCK",
         "QUARTZ_BLOCK",
         "ACTIVATOR_RAIL",
         "RAILS",
         "POWERED_RAIL",
         "DETECTOR_RAIL",
         "PUMPKIN",
         "JACK_O_LANTERN",
         "MELON_BLOCK",
         "NETHER_WART_BLOCK",
         "SIGN",
         "WALL_SIGN",
         "ENDER_CHEST",
         "OBSIDIAN",
         "DISPENSER",
         "DROPPER",
         "FURNACE",
         "SPONGE",
         "CLAY",
         "GRASS",
         "GRAVEL",
         "MYCEL",
         "GRASS_PATH",
         "BLACK_GLAZED_TERRACOTTA",
         "BLUE_GLAZED_TERRACOTTA",
         "BROWN_GLAZED_TERRACOTTA",
         "CYAN_GLAZED_TERRACOTTA",
         "GRAY_GLAZED_TERRACOTTA",
         "GREEN_GLAZED_TERRACOTTA",
         "LIGHT_BLUE_GLAZED_TERRACOTTA",
         "LIGHT_GRAY_GLAZED_TERRACOTTA",
         "LIME_GLAZED_TERRACOTTA",
         "MAGENTA_GLAZED_TERRACOTTA",
         "ORANGE_GLAZED_TERRACOTTA",
         "PINK_GLAZED_TERRACOTTA",
         "PURPLE_GLAZED_TERRACOTTA",
         "RED_GLAZED_TERRACOTTA",
         "WHITE_GLAZED_TERRACOTTA",
         "YELLOW_GLAZED_TERRACOTTA",
         "CONCRETE",
         "WORKBENCH",
         "CHEST",
         "TRAPPED_CHEST"
      };
   }

   static {
      So();
      Material[] var0 = (Material[])Arrays.stream(Material.values())
         .filter(Material::isBlock)
         .filter(Material::isSolid)
         .filter(Material::isOccluding)
         .toArray(Ey::dx);
      if (!w.v()) {
         ArrayList var1;
         (var1 = new ArrayList()).add(Xh[2]);
         var1.add(Xh[3]);
         var1.add(Xh[4]);
         var1.add(Xh[5]);
         var1.add(Xh[6]);
         var1.add(Xh[7]);
         var1.add(Xh[8]);
         var1.add(Xh[9]);
         var1.add(Xh[10]);
         var1.add(Xh[11]);
         var1.add(Xh[12]);
         var1.add(Xh[13]);
         int var2 = var0.length;

         for (int var3 = 0; var3 < var2; var3++) {
            Material var4;
            String var5 = (var4 = var0[var3]).toString();
            if (!((double)var4.getHardness() <= 0.5) && !var1.contains(var5)) {
               sB.put(var5, var4.getHardness());
            }
         }
      } else {
         sB.put(Xh[14], 0.5F);
         sB.put(Xh[15], 0.5F);
         sB.put(Xh[16], 0.5F);
         sB.put(Xh[17], 0.5F);
         sB.put(Xh[18], 0.5F);
         sB.put(Xh[19], 0.5F);
         sB.put(Xh[20], 0.5F);
         sB.put(Xh[21], 0.5F);
         sB.put(Xh[22], 0.5F);
         sB.put(Xh[23], 0.5F);
         sB.put(Xh[24], 3.0F);
         sB.put(Xh[25], 3.0F);
         sB.put(Xh[26], 3.0F);
         sB.put(Xh[27], 3.0F);
         sB.put(Xh[28], 3.0F);
         sB.put(Xh[29], 3.0F);
         sB.put(Xh[30], 3.0F);
         sB.put(Xh[31], 3.0F);
         sB.put(Xh[32], 3.0F);
         sB.put(Xh[33], 3.0F);
         sB.put(Xh[34], 3.0F);
         sB.put(Xh[35], 3.0F);
         sB.put(Xh[36], 3.0F);
         sB.put(Xh[37], 3.0F);
         sB.put(Xh[38], 3.0F);
         sB.put(Xh[39], 3.0F);
         sB.put(Xh[40], 3.0F);
         sB.put(Xh[41], 3.0F);
         sB.put(Xh[42], 3.0F);
         sB.put(Xh[43], 3.0F);
         sB.put(Xh[44], 5.0F);
         sB.put(Xh[45], 5.0F);
         sB.put(Xh[46], 5.0F);
         sB.put(Xh[47], 5.0F);
         sB.put(Xh[48], 5.0F);
         sB.put(Xh[49], 5.0F);
         sB.put(Xh[50], 5.0F);
         sB.put(Xh[51], 5.0F);
         sB.put(Xh[52], 5.0F);
         sB.put(Xh[53], 5.0F);
         sB.put(Xh[54], 5.0F);
         sB.put(Xh[55], 2.0F);
         sB.put(Xh[56], 2.0F);
         sB.put(Xh[57], 2.0F);
         sB.put(Xh[58], 2.0F);
         sB.put(Xh[59], 2.0F);
         sB.put(Xh[60], 2.0F);
         sB.put(Xh[61], 2.0F);
         sB.put(Xh[62], 2.0F);
         sB.put(Xh[63], 2.0F);
         sB.put(Xh[64], 2.0F);
         sB.put(Xh[65], 2.0F);
         sB.put(Xh[66], 2.0F);
         sB.put(Xh[67], 2.0F);
         sB.put(Xh[68], 2.0F);
         sB.put(Xh[69], 2.0F);
         sB.put(Xh[70], 2.0F);
         sB.put(Xh[71], 2.0F);
         sB.put(Xh[72], 2.0F);
         sB.put(Xh[73], 2.0F);
         sB.put(Xh[74], 2.0F);
         sB.put(Xh[75], 2.0F);
         sB.put(Xh[76], 2.0F);
         sB.put(Xh[77], 2.0F);
         sB.put(Xh[78], 2.0F);
         sB.put(Xh[79], 2.0F);
         sB.put(Xh[80], 2.0F);
         sB.put(Xh[81], 2.0F);
         sB.put(Xh[82], 2.0F);
         sB.put(Xh[83], 2.0F);
         sB.put(Xh[84], 2.0F);
         sB.put(Xh[85], 2.0F);
         sB.put(Xh[86], 2.0F);
         sB.put(Xh[87], 2.0F);
         sB.put(Xh[88], 2.0F);
         sB.put(Xh[89], 2.0F);
         sB.put(Xh[90], 2.0F);
         sB.put(Xh[91], 2.0F);
         sB.put(Xh[92], 2.0F);
         sB.put(Xh[93], 2.0F);
         sB.put(Xh[94], 2.0F);
         sB.put(Xh[95], 2.0F);
         sB.put(Xh[96], 2.0F);
         sB.put(Xh[97], 2.0F);
         sB.put(Xh[98], 2.0F);
         sB.put(Xh[99], 2.0F);
         sB.put(Xh[100], 2.0F);
         sB.put(Xh[101], 2.0F);
         sB.put(Xh[102], 2.0F);
         sB.put(Xh[103], 2.0F);
         sB.put(Xh[104], 2.0F);
         sB.put(Xh[105], 2.0F);
         sB.put(Xh[106], 2.0F);
         sB.put(Xh[107], 2.0F);
         sB.put(Xh[108], 2.0F);
         sB.put(Xh[109], 1.5F);
         sB.put(Xh[110], 1.5F);
         sB.put(Xh[111], 1.5F);
         sB.put(Xh[112], 1.5F);
         sB.put(Xh[113], 1.5F);
         sB.put(Xh[114], 0.8F);
         sB.put(Xh[115], 0.8F);
         sB.put(Xh[116], 0.8F);
         sB.put(Xh[117], 0.8F);
         sB.put(Xh[118], 0.8F);
         sB.put(Xh[119], 0.8F);
         sB.put(Xh[120], 0.8F);
         sB.put(Xh[121], 0.8F);
         sB.put(Xh[122], 0.8F);
         sB.put(Xh[123], 0.7F);
         sB.put(Xh[124], 0.7F);
         sB.put(Xh[125], 0.7F);
         sB.put(Xh[126], 0.7F);
         sB.put(Xh[127], 1.0F);
         sB.put(Xh[128], 1.0F);
         sB.put(Xh[129], 1.0F);
         sB.put(Xh[130], 1.0F);
         sB.put(Xh[131], 1.0F);
         sB.put(Xh[132], 1.0F);
         sB.put(Xh[133], 22.5F);
         sB.put(Xh[134], 50.0F);
         sB.put(Xh[135], 3.5F);
         sB.put(Xh[136], 3.5F);
         sB.put(Xh[137], 3.5F);
         sB.put(Xh[138], 0.6F);
         sB.put(Xh[139], 0.6F);
         sB.put(Xh[140], 0.6F);
         sB.put(Xh[141], 0.6F);
         sB.put(Xh[142], 0.6F);
         sB.put(Xh[143], 0.65F);
         sB.put(Xh[144], 1.4F);
         sB.put(Xh[145], 1.4F);
         sB.put(Xh[146], 1.4F);
         sB.put(Xh[147], 1.4F);
         sB.put(Xh[148], 1.4F);
         sB.put(Xh[149], 1.4F);
         sB.put(Xh[150], 1.4F);
         sB.put(Xh[151], 1.4F);
         sB.put(Xh[152], 1.4F);
         sB.put(Xh[153], 1.4F);
         sB.put(Xh[154], 1.4F);
         sB.put(Xh[155], 1.4F);
         sB.put(Xh[156], 1.4F);
         sB.put(Xh[157], 1.4F);
         sB.put(Xh[158], 1.4F);
         sB.put(Xh[159], 1.4F);
         sB.put(Xh[160], 1.8F);
         sB.put(Xh[161], 2.5F);
         sB.put(Xh[162], 2.5F);
         sB.put(Xh[163], 2.5F);
      }
   }

   private static int dx(ItemStack var0) {
      String var2;
      if (!(var2 = var0.getType().toString()).contains(Xh[0]) && !var2.contains(Xh[1])) {
         byte var1 = -1;
         switch (var2.hashCode()) {
            case -2004114189:
               if (Integer.valueOf(-2004114189).equals(var2.hashCode())) {
                  var1 = 12;
               }
               break;
            case -1850063282:
               if (Integer.valueOf(-1850063282).equals(var2.hashCode())) {
                  var1 = 29;
               }
               break;
            case -1474660721:
               if (Integer.valueOf(-1474660721).equals(var2.hashCode())) {
                  var1 = 23;
               }
               break;
            case -1376059913:
               if (Integer.valueOf(-1376059913).equals(var2.hashCode())) {
                  var1 = 28;
               }
               break;
            case -1092987765:
               if (Integer.valueOf(-1092987765).equals(var2.hashCode())) {
                  var1 = 6;
               }
               break;
            case -1048733105:
               if (Integer.valueOf(-1048733105).equals(var2.hashCode())) {
                  var1 = 25;
               }
               break;
            case -955115213:
               if (Integer.valueOf(-955115213).equals(var2.hashCode())) {
                  var1 = 4;
               }
               break;
            case -541889864:
               if (Integer.valueOf(-541889864).equals(var2.hashCode())) {
                  var1 = 22;
               }
               break;
            case -487815164:
               if (Integer.valueOf(-487815164).equals(var2.hashCode())) {
                  var1 = 8;
               }
               break;
            case -374280293:
               if (Integer.valueOf(-374280293).equals(var2.hashCode())) {
                  var1 = 9;
               }
               break;
            case -262974918:
               if (Integer.valueOf(-262974918).equals(var2.hashCode())) {
                  var1 = 21;
               }
               break;
            case -170122909:
               if (Integer.valueOf(-170122909).equals(var2.hashCode())) {
                  var1 = 19;
               }
               break;
            case -95218994:
               if (Integer.valueOf(-95218994).equals(var2.hashCode())) {
                  var1 = 3;
               }
               break;
            case 70353908:
               if (Integer.valueOf(70353908).equals(var2.hashCode())) {
                  var1 = 7;
               }
               break;
            case 122966710:
               if (Integer.valueOf(122966710).equals(var2.hashCode())) {
                  var1 = 0;
               }
               break;
            case 206638182:
               if (Integer.valueOf(206638182).equals(var2.hashCode())) {
                  var1 = 27;
               }
               break;
            case 346690796:
               if (Integer.valueOf(346690796).equals(var2.hashCode())) {
                  var1 = 13;
               }
               break;
            case 427049884:
               if (Integer.valueOf(427049884).equals(var2.hashCode())) {
                  var1 = 14;
               }
               break;
            case 430758414:
               if (Integer.valueOf(430758414).equals(var2.hashCode())) {
                  var1 = 20;
               }
               break;
            case 470163933:
               if (Integer.valueOf(470163933).equals(var2.hashCode())) {
                  var1 = 5;
               }
               break;
            case 473626359:
               if (Integer.valueOf(473626359).equals(var2.hashCode())) {
                  var1 = 1;
               }
               break;
            case 726388316:
               if (Integer.valueOf(726388316).equals(var2.hashCode())) {
                  var1 = 11;
               }
               break;
            case 872992337:
               if (Integer.valueOf(872992337).equals(var2.hashCode())) {
                  var1 = 15;
               }
               break;
            case 1263725840:
               if (Integer.valueOf(1263725840).equals(var2.hashCode())) {
                  var1 = 17;
               }
               break;
            case 1336224762:
               if (Integer.valueOf(1336224762).equals(var2.hashCode())) {
                  var1 = 2;
               }
               break;
            case 1542325061:
               if (Integer.valueOf(1542325061).equals(var2.hashCode())) {
                  var1 = 10;
               }
               break;
            case 1788665440:
               if (Integer.valueOf(1788665440).equals(var2.hashCode())) {
                  var1 = 16;
               }
               break;
            case 1842058393:
               if (Integer.valueOf(1842058393).equals(var2.hashCode())) {
                  var1 = 24;
               }
               break;
            case 2103862626:
               if (Integer.valueOf(2103862626).equals(var2.hashCode())) {
                  var1 = 26;
               }
               break;
            case 2118280994:
               if (Integer.valueOf(2118280994).equals(var2.hashCode())) {
                  var1 = 18;
               }
         }

         int var3;
         switch (var1) {
            case 0:
            case 1:
            case 2:
            case 3:
               var3 = me.nik.alice.Ey.dx.dx(me.nik.alice.Ey.dx.AC);
               break;
            case 4:
            case 5:
            case 6:
            case 7:
               var3 = me.nik.alice.Ey.dx.dx(me.nik.alice.Ey.dx.sB);
               break;
            case 8:
            case 9:
            case 10:
            case 11:
            case 12:
            case 13:
            case 14:
               var3 = me.nik.alice.Ey.dx.dx(me.nik.alice.Ey.dx.dC);
               break;
            case 15:
            case 16:
            case 17:
               var3 = me.nik.alice.Ey.dx.dx(me.nik.alice.Ey.dx.dx);
               break;
            case 18:
            case 19:
            case 20:
            case 21:
               var3 = me.nik.alice.Ey.dx.dx(me.nik.alice.Ey.dx.Aa);
               break;
            case 22:
            case 23:
            case 24:
            case 25:
            case 26:
            case 27:
            case 28:
            case 29:
               var3 = me.nik.alice.Ey.dx.dx(me.nik.alice.Ey.dx.og);
               break;
            default:
               var3 = 1;
         }

         return var3;
      } else {
         return -500;
      }
   }
}
