package me.nik.alice;

import java.util.List;

public enum F {
   dx(Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[30]))),
   Aa(Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[32]))),
   og(Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[34]))),
   AC(Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[36]))),
   sB(Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[38]))),
   dC,
   Zh,
   h5(Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[44]))),
   Fh(Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[46]))),
   b,
   VL,
   UH,
   yM,
   sX,
   WB,
   Vm,
   gz,
   tm,
   x,
   zP,
   hW,
   Ch(Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[72]))),
   iv(dx(Alice.dx().dx().dx().dx().getStringList(F.s2[74]))),
   F7(dx(Alice.dx().dx().dx().dx().getStringList(F.s2[76]))),
   Zp(dx(Alice.dx().dx().dx().dx().getStringList(F.s2[78]))),
   DB(dx(Alice.dx().dx().dx().dx().getStringList(F.s2[80]))),
   jD,
   Qt(dx(Alice.dx().dx().dx().dx().getStringList(F.s2[84])));

   private String message;
   private static final F[] dx;
   private static String[] s2;

   private F(String var3) {
      this.message = var3;
   }

   public String getMessage() {
      return this.message;
   }

   public static void AC() {
      dx.setMessage(Dq.sB(Alice.dx().dx().dx().dx().getString(s2[0])));
      Aa.setMessage(Dq.sB(Alice.dx().dx().dx().dx().getString(s2[1])));
      og.setMessage(Dq.sB(Alice.dx().dx().dx().dx().getString(s2[2])));
      AC.setMessage(Dq.sB(Alice.dx().dx().dx().dx().getString(s2[3])));
      sB.setMessage(Dq.sB(Alice.dx().dx().dx().dx().getString(s2[4])));
      dC.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[5])));
      Zh.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[6])));
      h5.setMessage(Dq.sB(Alice.dx().dx().dx().dx().getString(s2[7])));
      Fh.setMessage(Dq.sB(Alice.dx().dx().dx().dx().getString(s2[8])));
      b.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[9])));
      VL.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[10])));
      UH.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[11])));
      yM.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[12])));
      sX.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[13])));
      WB.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[14])));
      Vm.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[15])));
      gz.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[16])));
      tm.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[17])));
      x.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[18])));
      zP.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[19])));
      hW.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[20])));
      Ch.setMessage(Dq.sB(Alice.dx().dx().dx().dx().getString(s2[21])));
      iv.setMessage(dx(Alice.dx().dx().dx().dx().getStringList(s2[22])));
      F7.setMessage(dx(Alice.dx().dx().dx().dx().getStringList(s2[23])));
      Zp.setMessage(dx(Alice.dx().dx().dx().dx().getStringList(s2[24])));
      DB.setMessage(dx(Alice.dx().dx().dx().dx().getStringList(s2[25])));
      jD.setMessage(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(s2[26])));
      Qt.setMessage(dx(Alice.dx().dx().dx().dx().getStringList(s2[27])));
   }

   private void setMessage(String var1) {
      this.message = var1;
   }

   private static String dx(List var0) {
      StringBuilder var1 = new StringBuilder();
      int var2 = var0.size();

      for (int var3 = 0; var3 < var2; var3++) {
         var1.append((String)var0.get(var3));
         if (var2 - 1 != var3) {
            var1.append(s2[28]);
         }
      }

      return Dq.sB(var1.toString());
   }

   // $VF: Failed to inline enum fields
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   static {
      gt();
      dC = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[40])));
      Zh = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[42])));
      b = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[48])));
      VL = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[50])));
      UH = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[52])));
      yM = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[54])));
      sX = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[56])));
      WB = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[58])));
      Vm = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[60])));
      gz = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[62])));
      tm = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[64])));
      x = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[66])));
      zP = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[68])));
      hW = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[70])));
      jD = new F(dx.getMessage() + Dq.sB(Alice.dx().dx().dx().dx().getString(F.s2[82])));
      dx = new F[]{dx, Aa, og, AC, sB, dC, Zh, h5, Fh, b, VL, UH, yM, sX, WB, Vm, gz, tm, x, zP, hW, Ch, iv, F7, Zp, DB, jD, Qt};
   }

   private static void gt() {
      s2 = new String[]{
         "prefix",
         "bar.opening_bracket",
         "bar.closing_bracket",
         "bar.fill",
         "bar.empty",
         "update_reminder",
         "reconnect",
         "client_alert",
         "client_non_vanilla",
         "client_kick",
         "no_perm",
         "player_not_found",
         "kick_sender",
         "kick_crasher",
         "punish_sender",
         "punish_broadcast",
         "alert_message",
         "punishwave_countdown",
         "punishwave_punished",
         "punishwave_added",
         "punishwave_exists",
         "commands_list",
         "info_command",
         "lag_command",
         "vl_command",
         "alert_hover",
         "debug_message",
         "profiler_result",
         "\n",
         "PREFIX",
         "prefix",
         "BAR_OPENING_BRACKET",
         "bar.opening_bracket",
         "BAR_CLOSING_BRACKET",
         "bar.closing_bracket",
         "BAR_FILL",
         "bar.fill",
         "BAR_EMPTY",
         "bar.empty",
         "UPDATE_REMINDER",
         "update_reminder",
         "RECONNECT",
         "reconnect",
         "CLIENT_ALERT",
         "client_alert",
         "CLIENT_NON_VANILLA",
         "client_non_vanilla",
         "CLIENT_KICK",
         "client_kick",
         "NO_PERMISSION",
         "no_perm",
         "PLAYER_NOT_FOUND",
         "player_not_found",
         "KICK_SENDER",
         "kick_sender",
         "KICK_CRASHER",
         "kick_crasher",
         "PUNISH_SENDER",
         "punish_sender",
         "PUNISH_BROADCAST",
         "punish_broadcast",
         "ALERT_MESSAGE",
         "alert_message",
         "PUNISHWAVE_COUNTDOWN",
         "punishwave_countdown",
         "PUNISHWAVE_PUNISHED",
         "punishwave_punished",
         "PUNISHWAVE_ADDED",
         "punishwave_added",
         "PUNISHWAVE_EXISTS",
         "punishwave_exists",
         "COMMANDS_LIST",
         "commands_list",
         "INFO_COMMAND",
         "info_command",
         "LAG_COMMAND",
         "lag_command",
         "VL_COMMAND",
         "vl_command",
         "ALERT_HOVER",
         "alert_hover",
         "DEBUG_MESSAGE",
         "debug_message",
         "PROFILER_RESULT",
         "profiler_result"
      };
   }
}
