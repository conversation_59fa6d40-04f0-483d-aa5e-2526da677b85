package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class FH extends PacketCheck {
   private static String[] nD;

   public FH(UC var1) {
      super(var1, CheckType.MOTION, nD[0], Category.MOVE, 2.0F);
   }

   @Override
   public String sB() {
      return nD[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && !this.Aa.dx().dx().DP()
         && !this.Aa.dx().tr()
         && !(this.Aa.dx().Aa().getY() < 4.0)
         && this.Aa.dx().a() != 0
         && this.Aa.dx().GE() >= 20
         && this.Aa.dx().dx().og(2000L) <= 0
         && this.Aa.dx().cO() >= 20
         && this.Aa.dx().R2() >= 40
         && this.Aa.dx().yk() >= 20) {
         ES var8;
         int var2 = (var8 = this.Aa.dx()).u();
         double var4 = var8.Qt();
         if (var2 != 1 || !l.yM(var4)) {
            double var6 = var2 >= 2 && var2 <= 14 ? 0.06 : (var2 >= 15 && var2 <= 23 ? 0.05 : 0.009);
            if (var4 < var6) {
               this.Aa(nD[2] + var4 + nD[3] + var6 + nD[4] + var2);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.05);
            }
         }
      }
   }

   static {
      my();
   }

   private static void my() {
      nD = new String[]{"A", "Checks for slow falling", "Accel Y: ", " expected: ", " fly ticks: "};
   }
}
