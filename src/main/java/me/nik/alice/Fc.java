package me.nik.alice;

import com.comphenix.protocol.events.PacketContainer;
import me.nik.alice.wrappers.WrapperPlayClientUseEntity;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.entity.EntityDamageByEntityEvent;

public class Fc {
   private int SP;
   private final M5 dx;
   private int K;
   private int uX;
   private final UC Aa;
   private int iK;
   private Entity og;
   private int hB = -1;
   private int uD;
   private double DB;
   private Entity Aa;
   private int Wx;

   public void AC(Event var1) {
      Player var2;
      Player var10000 = var2 = this.Aa.getPlayer();

      try {
         var10000.hashCode();
      } catch (Exception var4) {
         return;
      }

      int var3 = Ku.iK();
      EntityDamageByEntityEvent var5;
      if (var1 instanceof EntityDamageByEntityEvent && (var5 = (EntityDamageByEntityEvent)var1).getEntity() == var2) {
         this.p6();
         int var6 = this.K - this.SP;
         this.SP = this.K;
         if (var5.getDamager() instanceof Player && var6 < 10) {
            this.uD = var3;
         }
      }
   }

   public int DB() {
      return l.Aa(this.K);
   }

   public void AC(DH var1) {
      Player var2;
      Player var10000 = var2 = this.Aa.getPlayer();

      try {
         var10000.equals(null);
      } catch (Exception var5) {
         return;
      }

      if (var2.isOnline()) {
         PacketContainer var3 = var1.dx();
         if (var1.Tq()) {
            WrapperPlayClientUseEntity var6;
            int var8 = (var6 = new WrapperPlayClientUseEntity(var3)).getTargetID();
            this.uX = this.hB == -1 ? var8 : this.hB;
            this.hB = var8;
            if (var8 != this.uX) {
               this.Wx++;
            }

            this.iK = Ku.iK();
            World var9 = var2.getWorld();
            if (!var6.isNullTarget(var9)) {
               Entity var7 = var6.getTarget(var9);
               Entity var4 = this.Aa == null ? var7 : this.Aa;
               if (var7.getWorld() == var9) {
                  this.DB = var2.getLocation().distanceSquared(var7.getLocation());
               } else {
                  this.DB = 0.0;
               }

               this.Aa = var7;
               this.og = var4;
            }
         }
      }
   }

   public void p6() {
      this.K = Ku.iK();
   }

   public void R2() {
      this.Wx = 0;
      this.DP();
   }

   public int Qt() {
      return l.Aa(this.iK);
   }

   public M5 dx() {
      return this.dx;
   }

   public Entity Aa() {
      return this.Aa;
   }

   public Fc(UC var1) {
      this.uX = -1;
      this.dx = new M5();
      this.Aa = var1;
   }

   public int getTargetID() {
      return this.hB;
   }

   public boolean jD() {
      return l.Aa(this.uD) < 18 + l.dC((long)(this.Aa.dx().jA() + 5));
   }

   public double gz() {
      return this.DB;
   }

   public int rc() {
      return this.Wx;
   }

   public int jD() {
      return this.uX;
   }

   private void DP() {
      if (this.Aa instanceof Player && this.og instanceof Player) {
         if (this.Aa != this.og || this.Aa.getLocation().getWorld() != this.og.getLocation().getWorld() || (long)this.Qt() > 10000L) {
            this.dx.clear();
         }

         this.dx.dx(this.Aa.getLocation());
      } else {
         this.dx.clear();
      }
   }

   public Entity og() {
      return this.og;
   }
}
