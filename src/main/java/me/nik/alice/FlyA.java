package me.nik.alice;

import me.nik.alice.checks.PacketCheck;

public class DC extends PacketCheck {
   private static String[] 1s;

   static {
      uV();
   }

   private static void uV() {
      1s = new String[]{"A", "Checks for fall distance modifications", "Fly ticks: ", " dist: ", " lastDist: "};
   }

   public DC(UC var1) {
      super(var1, VL.b, 1s[0], b.og, 1.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && !(this.Aa.getPlayer().getLocation().getY() < 4.0)
         && !this.Aa.dx().DB()
         && !this.Aa.dx().dx().DP()
         && !this.Aa.dx().tr()) {
         ES var7;
         double var3 = (double)(var7 = this.Aa.dx()).dC();
         double var5 = (double)var7.Zh();
         int var8 = var7.M3();
         if (var3 != 0.0 && var8 > 10 && var3 < var5) {
            this.Aa(1s[2] + var8 + 1s[3] + var3 + 1s[4] + var5);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.125);
         }
      }
   }

   @Override
   public String sB() {
      return 1s[1];
   }
}
