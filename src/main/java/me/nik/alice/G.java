package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class G extends PacketCheck {
   private static String[] 8i;

   private static void lA() {
      8i = new String[]{"D", "Checks for irregular motion changes", "MiniJump, Delta: ", "Invalid motion change, delta Y: ", " expected linear: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && !this.Aa.dx().dx().DP()
         && !this.Aa.dx().cO()
         && !this.Aa.dx().tk()
         && this.Aa.dx().yk() >= 20
         && this.Aa.dx().yV() >= 40
         && this.Aa.dx().cO() >= 40
         && this.Aa.dx().GE() >= 20
         && this.Aa.dx().R2() >= 20) {
         ES var14;
         double var3 = (var14 = this.Aa.dx()).Ch();
         double var5 = var14.iv();
         double var7 = Math.abs(var3 - -var5);
         Qk var15 = this.Aa.dx();
         double var10 = 0.004 - (var15.tr() ? Math.abs(var15.getVelocityY() - -var15.yV()) : 0.0);
         if (var7 < var10) {
            this.Aa(8i[2].concat(String.valueOf(var7)));
            if (this.Aa() > this.dx()) {
               this.sX();
            }
         } else {
            this.Aa(0.025);
         }

         if (var5 == 0.42F && !var15.tr()) {
            double var12 = var5 / 1.261;
            if (var3 < var12) {
               this.og(8i[3] + var3 + 8i[4] + var12);
            }
         }
      }
   }

   public G(UC var1) {
      super(var1, CheckType.MOTION, 8i[0], Category.MOVE, 2.0F);
   }

   @Override
   public String sB() {
      return 8i[1];
   }

   static {
      lA();
   }
}
