package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientWindowClick;
import org.bukkit.Material;
import org.bukkit.inventory.ItemStack;

public class GD extends PacketCheck {
   private static String[] Mv;
   private long Vm;
   private int sX;

   @Override
   public void dx(DH var1) {
      if (var1.Lh()) {
         WrapperPlayClientWindowClick var2;
         int var3 = (var2 = new WrapperPlayClientWindowClick(var1.dx())).getSlot();
         int var4 = this.sX;
         this.sX = var3;
         ItemStack var10;
         ItemStack var10000 = var10 = var2.getClickedItem();

         try {
            var10000.toString();
         } catch (Exception var8) {
            return;
         }

         if (var10.getType() != Material.AIR && var3 != var4) {
            long var6 = l.og(this.Vm);
            this.Vm = var1.getTimeStamp();
            int var9 = this.Aa.dx().jA() / 100;
            if (var6 >= 10L && var6 <= me.nik.alice.Ww.dx.Mt.Aa()) {
               this.Aa(Mv[2] + var6 + Mv[3] + var9);
               if (this.Aa() >= this.dx() + (float)var9) {
                  this.sX();
                  return;
               }
            } else {
               this.og();
            }
         }
      }
   }

   public GD(UC var1) {
      super(var1, CheckType.INVENTORY, Mv[0], Category.WORLD, 2.0F);
   }

   static {
      VX();
   }

   private static void VX() {
      Mv = new String[]{"ChestStealer", "Checks for fast inventory interactions", "Delay: ", " formula: "};
   }

   @Override
   public String sB() {
      return Mv[1];
   }
}
