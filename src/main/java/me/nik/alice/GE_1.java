package me.nik.alice;

import com.comphenix.protocol.wrappers.EnumWrappers.PlayerDigType;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientBlockDig;

public class GE extends PacketCheck {
   private static String[] kc;
   private int count;

   @Override
   public String sB() {
      return kc[1];
   }

   static {
      OH();
   }

   public GE(UC var1) {
      super(var1, CheckType.PACKET, kc[0], Category.WORLD, 1.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.uD()) {
         if (new WrapperPlayClientBlockDig(var1.dx()).getStatus() == PlayerDigType.RELEASE_USE_ITEM) {
            if (this.count++ >= 2) {
               this.Aa(kc[2] + this.count);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.og();
            }
         }
      } else {
         this.count = 0;
      }
   }

   private static void OH() {
      kc = new String[]{"L", "Checks for invalid dig packets", "Count: "};
   }
}
