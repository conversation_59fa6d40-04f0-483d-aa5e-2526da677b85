package me.nik.alice;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;

import me.nik.alice.checks.Check;
import me.nik.alice.checks.EventCheck;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.checks.aim.*;
import me.nik.alice.checks.autofish.AutoFishA;
import me.nik.alice.checks.baritone.BaritoneA;
import me.nik.alice.checks.clicking.*;
import me.nik.alice.checks.elytra.ElytraA;
import me.nik.alice.checks.elytra.ElytraB;
import me.nik.alice.checks.esp.ESPA;
import me.nik.alice.checks.fly.FlyA;
import me.nik.alice.checks.fly.FlyB;
import me.nik.alice.checks.hitbox.HitBoxA;
import me.nik.alice.checks.hitbox.HitBoxB;
import me.nik.alice.checks.packet.BadPacketZ;
import me.nik.alice.checks.velocity.VelocityA;
import me.nik.alice.checks.velocity.VelocityC;
import org.bukkit.event.Event;

public class GK {
   private final List sB = new ArrayList();
   private boolean b;
   private final UC Aa;
   private final List dC = new ArrayList();

   private boolean Aa(Check var1) {
      if (this.b) {
         return true;
      } else {
         if (var1.getClass().isAnnotationPresent(Fh.class)) {
            this.b = true;
         }

         return false;
      }
   }

   private void dx(PacketCheck var1) {
      UC var10000 = this.Aa;

      label25: {
         try {
            var10000.hashCode();
         } catch (Exception var2) {
            break label25;
         }

         if (!var1.isEnabled() || this.Aa(var1)) {
            return;
         }
      }

      this.sB.add(var1);
   }

   public void Aa(Event var1) {
      Iterator var2 = this.dC.iterator();

      while (var2.hasNext()) {
         ((EventCheck)var2.next()).on(var1);
      }
   }

   public List sB() {
      return this.dC;
   }

   private static boolean dx(EventCheck var0) {
      return !var0.getClass().isAnnotationPresent(Fh.class);
   }

   private static boolean dx(PacketCheck var0) {
      return !var0.getClass().isAnnotationPresent(Fh.class);
   }

   public void Aa(DH var1) {
      boolean var4 = VDQi;
      Iterator var2 = this.sB.iterator();

      while (var2.hasNext()) {
         ((PacketCheck)var2.next()).dx(var1);
         if (var4) {
            throw null;
         }
      }
   }

   public void jD() {
      this.sB.clear();
      this.dC.clear();
   }

   public void DB() {
      this.jD();
      this.dx(new AimA(this.Aa));
      this.dx(new AimB(this.Aa));
      this.dx(new AimC(this.Aa));
      this.dx(new AimD(this.Aa));
      this.dx(new AimE(this.Aa));
      this.dx(new AimF(this.Aa));
      this.dx(new AimG(this.Aa));
      this.dx(new AimH(this.Aa));
      this.dx(new AimI(this.Aa));
      this.dx(new AimJ(this.Aa));
      this.dx(new AimK(this.Aa));
      this.dx(new AimL(this.Aa));
      this.dx(new AimM(this.Aa));
      this.dx(new AimN(this.Aa));
      this.dx(new AimO(this.Aa));
      this.dx(new AimP(this.Aa));
      this.dx(new AimQ(this.Aa));
      this.dx(new AimR(this.Aa));
      this.dx(new ClickingA(this.Aa));
      this.dx(new ClickingB(this.Aa));
      this.dx(new ClickingC(this.Aa));
      this.dx(new ClickingD(this.Aa));
      this.dx(new ClickingE(this.Aa));
      this.dx(new ClickingF(this.Aa));
      this.dx(new ClickingG(this.Aa));
      this.dx(new ClickingH(this.Aa));
      this.dx(new o4(this.Aa));
      this.dx(new pO(this.Aa));
      this.dx(new p6(this.Aa));
      this.dx(new Sh(this.Aa));
      this.dx(new yk(this.Aa));
      this.dx(new P(this.Aa));
      this.dx(new R(this.Aa));
      this.dx(new xx(this.Aa));
      this.dx(new M3(this.Aa));
      this.dx(new E(this.Aa));
      this.dx(new tV(this.Aa));
      this.dx(new GE(this.Aa));
      this.dx(new tU(this.Aa));
      this.dx(new Wx(this.Aa));
      this.dx(new hB(this.Aa));
      this.dx(new uX(this.Aa));
      this.dx(new iK(this.Aa));
      this.dx(new K(this.Aa));
      this.dx(new SP(this.Aa));
      this.dx(new uD(this.Aa));
      this.dx(new PU(this.Aa));
      this.dx(new J4(this.Aa));
      this.dx(new Lh(this.Aa));
      this.dx(new Ej(this.Aa));
      this.dx(new CS(this.Aa));
      this.dx(new BadPacketZ(this.Aa));
      this.dx(new tr(this.Aa));
      this.dx(new DP(this.Aa));
      this.dx(new R2(this.Aa));
      this.dx(new pQ(this.Aa));
      this.dx(new ElytraA(this.Aa));
      this.dx(new ElytraB(this.Aa));
      this.dx(new qx(this.Aa));
      this.dx(new Tq(this.Aa));
      this.dx(new qa(this.Aa));
      this.dx(new v(this.Aa));
      this.dx(new N7(this.Aa));
      this.dx(new Sx(this.Aa));
      this.dx(new rr(this.Aa));
      this.dx(new cf(this.Aa));
      this.dx(new HitBoxA(this.Aa));
      this.dx(new HitBoxB(this.Aa));
      this.dx(new nA(this.Aa));
      this.dx(new sp(this.Aa));
      this.dx(new PP(this.Aa));
      this.dx(new GD(this.Aa));
      this.dx(new Zb(this.Aa));
      this.dx(new k0(this.Aa));
      this.dx(new fa(this.Aa));
      this.dx(new Pu(this.Aa));
      this.dx(new f7(this.Aa));
      this.dx(new Zt(this.Aa));
      this.dx(new fq(this.Aa));
      this.dx(new xB(this.Aa));
      this.dx(new EG(this.Aa));
      this.dx(new Tf(this.Aa));
      this.dx(new KJ(this.Aa));
      this.dx(new FH(this.Aa));
      this.dx(new J(this.Aa));
      this.dx(new kV(this.Aa));
      this.dx(new G(this.Aa));
      this.dx(new W(this.Aa));
      this.dx(new z(this.Aa));
      this.dx(new mQ(this.Aa));
      this.dx(new YL(this.Aa));
      this.dx(new iL(this.Aa));
      this.dx(new FlyA(this.Aa));
      this.dx(new D1(this.Aa));
      this.dx(new tN(this.Aa));
      this.dx(new p(this.Aa));
      this.dx(new UZ(this.Aa));
      this.dx(new KB(this.Aa));
      this.dx(new C3(this.Aa));
      this.dx(new vg(this.Aa));
      this.dx(new oN(this.Aa));
      this.dx(new Ac(this.Aa));
      this.dx(new Xm(this.Aa));
      this.dx(new A(this.Aa));
      this.dx(new SU(this.Aa));
      this.dx(new vi(this.Aa));
      this.dx(new pn(this.Aa));
      this.dx(new FlyB(this.Aa));
      this.dx(new Eg(this.Aa));
      this.dx(new Qp(this.Aa));
      this.dx(new Mt(this.Aa));
      this.dx(new VelocityA(this.Aa));
      this.dx(new r(this.Aa));
      this.dx(new VelocityC(this.Aa));
      this.dx(new UH(this.Aa));
      this.dx(new BaritoneA(this.Aa));
      this.dx(new AutoFishA(this.Aa));
      this.dx(new qJ(this.Aa));
      this.dx(new Qf(this.Aa));
      this.dx(new O5(this.Aa));
      this.dx(new yx(this.Aa));
      this.dx(new Hy(this.Aa));
      this.dx(new fT(this.Aa));
      this.dx(new ESPA(this.Aa));
      this.dx(new sX(this.Aa));
      if (this.b) {
         this.sB.removeIf(GK::dx);
         this.dC.removeIf(GK::dx);
      }
   }

   public GK(UC var1) {
      this.Aa = var1;
   }

   public List dC() {
      ArrayList var1;
      (var1 = new ArrayList()).addAll(this.sB);
      var1.addAll(this.dC);
      return var1;
   }

   private void dx(EventCheck var1) {
      UC var10000 = this.Aa;

      label26: {
         try {
            var10000.hashCode();
         } catch (Exception var2) {
            break label26;
         }

         if (!var1.isEnabled() || this.Aa(var1)) {
            return;
         }
      }

      this.dC.add(var1);
   }

   public List AC() {
      return this.sB;
   }
}
