package me.nik.alice;

import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import me.nik.alice.api.events.AliceViolationEvent;
import me.nik.fastmath.FastMath;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;

public class Gy implements Listener {
   private static String[] 4K;
   private final Alice plugin;

   static {
      1A();
   }

   private static void _A/* $VF was: 1A*/() {
      4K = new String[]{
         " (",
         ")",
         " (Experimental)",
         "",
         "%description%",
         "%information%",
         "%version%",
         "%ping%",
         "%tps%",
         "%player%",
         "%check%",
         "%vl%",
         "%percent%",
         "%bar%",
         "/alice spectate ",
         " X: ",
         " Y: ",
         " Z: ",
         "Violation",
         "Server",
         "Check",
         "Description",
         "Location",
         "Client",
         "Version",
         "TPS",
         "Ping",
         "Information"
      };
   }

   @EventHandler(
      priority = EventPriority.HIGHEST,
      ignoreCancelled = true
   )
   public void dx(AliceViolationEvent var1) {
      this.plugin.dx().dx().execute(this::Aa);
   }

   private void Aa(AliceViolationEvent var1) {
      Player var2;
      Player var10000 = var2 = var1.getPlayer();

      try {
         var10000.toString();
      } catch (Exception var15) {
         return;
      }

      if (var2.isOnline()) {
         UC var3;
         UC var27 = var3 = this.plugin.dx().dx(var2);

         try {
            var27.equals(null);
         } catch (Exception var14) {
            return;
         }

         String var4 = String.valueOf(var3.dx().jA());
         String var5 = String.valueOf(Ku.QJ());
         String var6 = var1.getType();
         String var7 = var1.getCheck();
         var6 = (var6.isEmpty() ? var7 : var7 + 4K[0] + var6 + 4K[1]) + (var1.isExperimental() ? 4K[2] : 4K[3]);
         var7 = var1.getDescription();
         String var8 = var1.getInformation();
         String var9 = var2.getName();
         String var10 = var3.dx().toString();
         int var11 = var1.getVl();
         int var16 = var1.getMaxVl();
         this.plugin.dx().Aa(new hL(me.nik.alice.UN.dx.AC.b(), var9, var2.getUniqueId().toString(), var6, var8));
         List var12;
         if (!(var12 = this.plugin.dx().og()).isEmpty()) {
            String var13 = F.DB.getMessage().replace(4K[4], var7).replace(4K[5], var8).replace(4K[6], var10).replace(4K[7], var4).replace(4K[8], var5);
            String var17 = F.gz
               .getMessage()
               .replace(4K[9], var9)
               .replace(4K[10], var6)
               .replace(4K[11], String.valueOf(var11))
               .replace(4K[12], gE.dx((double)var11, (double)var16))
               .replace(4K[13], gE.Aa((double)var11, (double)var16));
            Sl var22 = new Sl(var17).dx(me.nik.alice.Sl.Aa.Aa, var13).dx(me.nik.alice.Sl.dx.og, 4K[14].concat(String.valueOf(var9))).dx();
            Iterator var18 = var12.iterator();

            while (var18.hasNext()) {
               Player var24;
               if ((var24 = Bukkit.getPlayer((UUID)var18.next())) != null) {
                  var22.Zh(var24);
               }
            }
         }

         if (me.nik.alice.UN.dx.h5.dC() && me.nik.alice.UN.dx.dC.dC()) {
            Location var25 = var2.getLocation();
            String var26 = var25.getWorld().getName()
               + 4K[15]
               + FastMath.round(var25.getX())
               + 4K[16]
               + FastMath.round(var25.getY())
               + 4K[17]
               + FastMath.round(var25.getZ());
            String var19 = var3.hW();
            OB var23;
            (var23 = new OB(me.nik.alice.UN.dx.Zh.b()))
               .dx(
                  new me.nik.alice.OB.dx()
                     .dx(4K[18])
                     .og(var9)
                     .dx(Color.YELLOW)
                     .Aa(var9)
                     .dx(4K[19], me.nik.alice.UN.dx.AC.b())
                     .dx(4K[20], var6)
                     .dx(4K[21], var7)
                     .dx(4K[22], var26)
                     .dx(4K[23], var19)
                     .dx(4K[24], var10)
                     .dx(4K[25], var5)
                     .dx(4K[26], var4)
                     .dx(4K[27], var8)
               );
            var23.Zm();
         }
      }
   }

   public Gy(Alice var1) {
      this.plugin = var1;
   }
}
