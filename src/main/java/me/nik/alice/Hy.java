package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import org.bukkit.block.Block;
import org.bukkit.event.Event;
import org.bukkit.event.block.BlockPlaceEvent;

public class Hy extends EventCheck {
   private static String[] dj;
   private int gz;

   @Override
   public String sB() {
      return dj[3];
   }

   private static void Go() {
      dj = new String[]{"F", "Dy: ", " Dp: ", "Checks for snappy rotations when bridging"};
   }

   public Hy(UC var1) {
      super(var1, CheckType.SCAFFOLD, dj[0], Category.WORLD, 2.0F);
   }

   static {
      Go();
   }

   @Override
   public void on(Event var1) {
      if (var1 instanceof BlockPlaceEvent) {
         Block var2;
         BlockPlaceEvent var4;
         if ((var2 = (var4 = (BlockPlaceEvent)var1).getBlockPlaced()).getType().isSolid()) {
            int var7 = var2.getY();
            int var3 = this.gz;
            this.gz = var7;
            if (var7 == var3) {
               if (!var4.getPlayer().getAllowFlight()) {
                  qn var5;
                  float var8 = (var5 = this.Aa.dx()).b();
                  float var6;
                  if (((var6 = var5.UH()) == 0.0F && var8 >= 20.0F || var6 >= 20.0F && var8 == 0.0F) && this.Aa.dx().VL() < 2) {
                     this.Aa(dj[1] + var8 + dj[2] + var6);
                     if (this.Aa() > this.on()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.25);
                  }
               }
            }
         }
      }
   }
}
