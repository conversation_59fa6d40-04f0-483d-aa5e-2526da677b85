package me.nik.alice;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.UUID;
import java.util.logging.Logger;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.PluginManager;
import org.geysermc.floodgate.FloodgateAPI;

public class I {
   private final List Aa = new ArrayList();
   private boolean h5;
   private static String[] Iu;

   public boolean dx(Player var1) {
      boolean var4 = y6ih;
      if (this.Aa.isEmpty()) {
         return false;
      } else {
         Iterator var2 = this.Aa.iterator();

         while (var2.hasNext()) {
            if (((f2)var2.next()).dx(var1)) {
               return true;
            }

            if (var4) {
               throw null;
            }
         }

         return false;
      }
   }

   static {
      1n();
   }

   public void dC() {
      PluginManager var1;
      Plugin var10000 = (var1 = Bukkit.getPluginManager()).getPlugin(Iu[0]);

      label62: {
         try {
            var10000.equals(null);
         } catch (Exception var6) {
            break label62;
         }

         this.Aa.add(new rn());
         var1.registerEvents(new e(), Alice.dx());
      }

      var10000 = var1.getPlugin(Iu[1]);

      label57: {
         try {
            var10000.hashCode();
         } catch (Exception var5) {
            break label57;
         }

         this.Aa.add(new ZO());
      }

      var10000 = var1.getPlugin(Iu[2]);

      label52: {
         try {
            var10000.equals(null);
         } catch (Exception var4) {
            break label52;
         }

         var1.registerEvents(new Od(), Alice.dx());
      }

      var10000 = var1.getPlugin(Iu[3]);

      label47: {
         try {
            var10000.getClass();
         } catch (Exception var3) {
            break label47;
         }

         this.Aa.add(new QF());
         var1.registerEvents(new qg(), Alice.dx());
      }

      var10000 = var1.getPlugin(Iu[4]);

      label42: {
         try {
            var10000.hashCode();
         } catch (Exception var2) {
            break label42;
         }

         this.Aa.add(new C());
      }

      this.h5 = var1.getPlugin(Iu[5]) != null;
      if (!this.Aa.isEmpty()) {
         Logger var7 = Alice.dx().getLogger();
         this.Aa.forEach(I::dx);
      }
   }

   public boolean og(Player var1) {
      if (this.Aa.isEmpty()) {
         return false;
      } else {
         Iterator var2 = this.Aa.iterator();

         while (var2.hasNext()) {
            if (((f2)var2.next()).og(var1)) {
               return true;
            }
         }

         return false;
      }
   }

   public boolean dx(UUID var1) {
      return var1.getMostSignificantBits() == 0L || this.h5 && FloodgateAPI.isBedrockPlayer(var1);
   }

   public void gz() {
      this.Aa.clear();
   }

   private static void _n/* $VF was: 1n*/() {
      Iu = new String[]{"CrazyEnchantments", "mcMMO", "AdvancedEnchantments", "EcoEnchants", "AnvilLot", "floodgate-bukkit", "Hooked into "};
   }

   private static void dx(Logger var0, f2 var1) {
      var0.info(Iu[6].concat(String.valueOf(var1)));
   }

   public boolean Aa(Player var1) {
      if (this.Aa.isEmpty()) {
         return false;
      } else {
         Iterator var2 = this.Aa.iterator();

         while (var2.hasNext()) {
            if (((f2)var2.next()).Aa(var1)) {
               return true;
            }
         }

         return false;
      }
   }
}
