package me.nik.alice;

import me.nik.alice.api.events.AlicePunishEvent;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;

public class Ib implements Listener {
   private static String[] IA;

   @EventHandler(
      priority = EventPriority.HIGHEST
   )
   public void dx(AlicePunishEvent var1) {
      String var2;
      String var10000 = var2 = var1.getPlayer();

      try {
         var10000.toString();
      } catch (Exception var4) {
         return;
      }

      if (me.nik.alice.UN.dx.Qt.dC()) {
         Bukkit.broadcastMessage(F.Vm.getMessage().replace(IA[0], var2));
      }

      if (me.nik.alice.UN.dx.Fh.dC() && me.nik.alice.UN.dx.dC.dC()) {
         OB var3;
         (var3 = new OB(me.nik.alice.UN.dx.Zh.b()))
            .dx(
               new me.nik.alice.OB.dx()
                  .dx(IA[1])
                  .og(var2)
                  .dx(Color.RED)
                  .Aa(var2)
                  .dx(IA[2], me.nik.alice.UN.dx.AC.b())
                  .dx(IA[3], var1.getFrom())
                  .dx(IA[4], var1.getReason())
            );
         var3.Zm();
      }
   }

   private static void VT() {
      IA = new String[]{"%player%", "Punishment", "Server", "From", "Reason"};
   }

   static {
      VT();
   }
}
