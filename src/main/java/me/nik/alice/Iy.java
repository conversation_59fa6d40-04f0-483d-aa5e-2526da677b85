package me.nik.alice;

import alice_libs.hikari.HikariConfig;
import alice_libs.hikari.HikariDataSource;
import java.sql.Connection;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.sql.Statement;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class Iy extends TQ {
   private HikariDataSource dx;
   private static String[] iP;

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   @Override
   public void dx(hL var1) {
      String var2 = var1.gz();
      String var3 = var1.getPlayer();
      String var4 = var1.tm();
      String var5 = var1.getCheck();
      String var6 = var1.getInformation();
      String var20 = var1.x();

      SQLException var10000;
      label87: {
         String var26;
         try {
            var22 = this.getConnection();
            var26 = iP[0];
         } catch (SQLException var19) {
            var10000 = var19;
            boolean var10001 = false;
            break label87;
         }

         PreparedStatement var7;
         try {
            var23 = var7 = var22.prepareStatement(var26);
            var28 = 1;
         } catch (SQLException var18) {
            var10000 = var18;
            boolean var27 = false;
            break label87;
         }

         try {
            var23.setString(var28, var2);
         } catch (SQLException var17) {
            var10000 = var17;
            boolean var29 = false;
            break label87;
         }

         try {
            var7.setString(2, var3);
         } catch (SQLException var16) {
            var10000 = var16;
            boolean var30 = false;
            break label87;
         }

         try {
            var7.setString(3, var4);
         } catch (SQLException var15) {
            var10000 = var15;
            boolean var31 = false;
            break label87;
         }

         try {
            var7.setString(4, var5);
         } catch (SQLException var14) {
            var10000 = var14;
            boolean var32 = false;
            break label87;
         }

         try {
            var7.setString(5, var6);
         } catch (SQLException var13) {
            var10000 = var13;
            boolean var33 = false;
            break label87;
         }

         PreparedStatement var24 = var7;
         byte var34 = 6;
         String var10002 = var20;

         try {
            var24.setString(var34, var10002);
         } catch (SQLException var12) {
            var10000 = var12;
            boolean var35 = false;
            break label87;
         }

         try {
            var7.executeUpdate();
         } catch (SQLException var11) {
            var10000 = var11;
            boolean var36 = false;
            break label87;
         }

         try {
            var7.close();
         } catch (SQLException var10) {
            var10000 = var10;
            boolean var37 = false;
            break label87;
         }

         try {
            var25 = var7.getConnection();
         } catch (SQLException var9) {
            var10000 = var9;
            boolean var38 = false;
            break label87;
         }

         try {
            var25.close();
            return;
         } catch (SQLException var8) {
            var10000 = var8;
            boolean var39 = false;
         }
      }

      SQLException var21 = var10000;
      Dq.GE();
      var21.printStackTrace();
   }

   public Connection getConnection() {
      Iy var10000 = this;

      try {
         return var10000.dx.getConnection();
      } catch (SQLException var2) {
         Dq.GE();
         var2.printStackTrace();
         return null;
      }
   }

   public boolean gz() {
      try {
         this.dx.getConnection();

         try {
            return true;
         } catch (Exception var1) {
         }
      } catch (SQLException var2) {
         return false;
      }

      return false;
   }

   @Override
   public void dC() {
      wf.Aa(this::yV);
   }

   static {
      C0();
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private List Aa(String var1) {
      SQLException var10000;
      label80: {
         ArrayList var2;
         try {
            var2 = new ArrayList();
         } catch (SQLException var13) {
            var10000 = var13;
            boolean var10001 = false;
            break label80;
         }

         try {
            var16 = this.getConnection().prepareStatement(iP[3]);
         } catch (SQLException var12) {
            var10000 = var12;
            boolean var18 = false;
            break label80;
         }

         PreparedStatement var3 = var16;

         try {
            var16.setString(1, var1);
         } catch (SQLException var11) {
            var10000 = var11;
            boolean var19 = false;
            break label80;
         }

         try {
            var14 = var3.executeQuery();
         } catch (SQLException var9) {
            var10000 = var9;
            boolean var20 = false;
            break label80;
         }

         while (true) {
            try {
               if (!var14.next()) {
                  break;
               }
            } catch (SQLException var10) {
               var10000 = var10;
               boolean var21 = false;
               break label80;
            }

            ArrayList var17 = var2;
            hL var22 = new hL;
            hL var10002 = var22;
            ResultSet var10003 = var14;
            String var10004 = iP[4];

            String var10005;
            String var10006;
            try {
               var28 = var10003.getString(var10004);
               var10004 = var14.getString(iP[5]);
               var10005 = var14.getString(iP[6]);
               var10006 = var14.getString(iP[7]);
            } catch (SQLException var8) {
               var10000 = var8;
               boolean var23 = false;
               break label80;
            }

            ResultSet var10007 = var14;

            ResultSet var10008;
            try {
               var30 = var10007.getString(iP[8]);
               var10008 = var14;
            } catch (SQLException var7) {
               var10000 = var7;
               boolean var24 = false;
               break label80;
            }

            String[] var10009 = iP;
            byte var10010 = 9;

            try {
               var10002.<init>(var28, var10004, var10005, var10006, var30, var10008.getString(var10009[var10010]));
               var17.add(var22);
            } catch (SQLException var6) {
               var10000 = var6;
               boolean var25 = false;
               break label80;
            }
         }

         try {
            var14.close();
         } catch (SQLException var5) {
            var10000 = var5;
            boolean var26 = false;
            break label80;
         }

         try {
            var3.getConnection().close();
            return var2;
         } catch (SQLException var4) {
            var10000 = var4;
            boolean var27 = false;
         }
      }

      SQLException var15 = var10000;
      Dq.GE();
      var15.printStackTrace();
      return new ArrayList();
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private List Fh() {
      SQLException var10000;
      label87: {
         try {
            var15 = new ArrayList();
         } catch (SQLException var13) {
            var10000 = var13;
            boolean var10001 = false;
            break label87;
         }

         ArrayList var1 = var15;
         Iy var16 = this;

         PreparedStatement var2;
         ResultSet var3;
         try {
            var3 = (var2 = var16.getConnection().prepareStatement(iP[10])).executeQuery();
         } catch (SQLException var11) {
            var10000 = var11;
            boolean var21 = false;
            break label87;
         }

         while (true) {
            ResultSet var17 = var3;

            try {
               if (!var17.next()) {
                  break;
               }
            } catch (SQLException var12) {
               var10000 = var12;
               boolean var22 = false;
               break label87;
            }

            ArrayList var18 = var1;

            hL var24;
            hL var10002;
            String var10003;
            try {
               var24 = new hL;
               var10002 = var24;
               var10003 = var3.getString(iP[11]);
            } catch (SQLException var10) {
               var10000 = var10;
               boolean var23 = false;
               break label87;
            }

            ResultSet var10004 = var3;

            ResultSet var10005;
            String[] var10006;
            try {
               var31 = var10004.getString(iP[12]);
               var10005 = var3;
               var10006 = iP;
            } catch (SQLException var9) {
               var10000 = var9;
               boolean var25 = false;
               break label87;
            }

            try {
               var32 = var10005.getString(var10006[13]);
               var33 = var3;
            } catch (SQLException var8) {
               var10000 = var8;
               boolean var26 = false;
               break label87;
            }

            String[] var10007 = iP;

            try {
               var10006 = var33.getString(var10007[14]);
               var10007 = var3.getString(iP[15]);
            } catch (SQLException var7) {
               var10000 = var7;
               boolean var27 = false;
               break label87;
            }

            try {
               var10002.<init>(var10003, var31, var32, var10006, var10007, var3.getString(iP[16]));
               var18.add(var24);
            } catch (SQLException var6) {
               var10000 = var6;
               boolean var28 = false;
               break label87;
            }
         }

         ResultSet var19 = var3;

         try {
            var19.close();
         } catch (SQLException var5) {
            var10000 = var5;
            boolean var29 = false;
            break label87;
         }

         PreparedStatement var20 = var2;

         try {
            var20.getConnection().close();
            return var1;
         } catch (SQLException var4) {
            var10000 = var4;
            boolean var30 = false;
         }
      }

      SQLException var14 = var10000;
      Dq.GE();
      var14.printStackTrace();
      return new ArrayList();
   }

   private static void C0() {
      iP = new String[]{
         "INSERT INTO alice_playerdata (id,server,player,uuid,checktype,information,timestamp) VALUES (null,?,?,?,?,?,?)",
         "Took more than 10 seconds to load the player logs!",
         "Took more than 10 seconds to load the player logs!",
         "SELECT * FROM alice_playerdata WHERE player=?",
         "server",
         "player",
         "uuid",
         "checktype",
         "information",
         "timestamp",
         "SELECT * FROM alice_playerdata",
         "server",
         "player",
         "uuid",
         "checktype",
         "information",
         "timestamp",
         "alice-hikari",
         "autoReconnect",
         "cachePrepStmts",
         "prepStmtCacheSize",
         "prepStmtCacheSqlLimit",
         "useServerPrepStmts",
         "useLocalSessionState",
         "rewriteBatchedStatements",
         "cacheResultSetMetadata",
         "cacheServerConfiguration",
         "elideSetAutoCommits",
         "maintainTimeStats",
         "alwaysSendSetIsolation",
         "cacheCallableStmts",
         "com.mysql.jdbc.jdbc2.optional.MysqlDataSource",
         "serverName",
         "port",
         "databaseName",
         "user",
         "password",
         "useSSL",
         "CREATE TABLE IF NOT EXISTS alice_playerdata (id mediumint not null auto_increment,server varchar(50),player varchar(100),uuid varchar(100),checktype varchar(100),information varchar(100),timestamp varchar(100),primary key (id))",
         "SELECT * FROM alice_playerdata",
         "SELECT * FROM alice_playerdata",
         "dd.MM.yyyy HH:mm",
         "timestamp",
         "Couldn't connect to the Database, Make sure the Credentials are correct."
      };
   }

   public Iy(Alice var1) {
      super(var1);
   }

   @Override
   public void gz() {
      if (this.gz()) {
         this.dx.close();
      }
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private void yV() {
      SQLException var10000;
      label329: {
         HikariConfig var1;
         try {
            (var1 = new HikariConfig()).setPoolName(iP[17]);
         } catch (ParseException | SQLException var47) {
            var10000 = var47;
            boolean var10001 = false;
            break label329;
         }

         try {
            var1.setMaximumPoolSize(me.nik.alice.UN.dx.pQ.Aa());
         } catch (ParseException | SQLException var46) {
            var10000 = var46;
            boolean var79 = false;
            break label329;
         }

         HikariConfig var51 = var1;
         me.nik.alice.UN.dx var80 = me.nik.alice.UN.dx.yk;

         try {
            var51.setMinimumIdle(var80.Aa());
         } catch (ParseException | SQLException var45) {
            var10000 = var45;
            boolean var81 = false;
            break label329;
         }

         try {
            var1.setMaxLifetime(me.nik.alice.UN.dx.P.Aa());
         } catch (ParseException | SQLException var44) {
            var10000 = var44;
            boolean var82 = false;
            break label329;
         }

         try {
            var1.setConnectionTimeout(me.nik.alice.UN.dx.R.Aa());
         } catch (ParseException | SQLException var43) {
            var10000 = var43;
            boolean var83 = false;
            break label329;
         }

         HikariConfig var52 = var1;
         var80 = me.nik.alice.UN.dx.xx;

         try {
            var86 = var80.Aa();
         } catch (ParseException | SQLException var42) {
            var10000 = var42;
            boolean var85 = false;
            break label329;
         }

         try {
            var52.setLeakDetectionThreshold(var86);
         } catch (ParseException | SQLException var41) {
            var10000 = var41;
            boolean var87 = false;
            break label329;
         }

         try {
            var1.addDataSourceProperty(iP[18], Boolean.TRUE);
         } catch (ParseException | SQLException var40) {
            var10000 = var40;
            boolean var88 = false;
            break label329;
         }

         HikariConfig var53 = var1;
         String var89 = iP[19];
         Boolean var10002 = Boolean.TRUE;

         try {
            var53.addDataSourceProperty(var89, var10002);
         } catch (ParseException | SQLException var39) {
            var10000 = var39;
            boolean var90 = false;
            break label329;
         }

         HikariConfig var54 = var1;
         String[] var91 = iP;
         byte var139 = 20;

         try {
            var54.addDataSourceProperty(var91[var139], 250);
         } catch (ParseException | SQLException var38) {
            var10000 = var38;
            boolean var92 = false;
            break label329;
         }

         HikariConfig var55 = var1;
         String[] var93 = iP;
         byte var140 = 21;

         try {
            var55.addDataSourceProperty(var93[var140], 2048);
         } catch (ParseException | SQLException var37) {
            var10000 = var37;
            boolean var94 = false;
            break label329;
         }

         HikariConfig var56 = var1;
         String[] var95 = iP;

         try {
            var56.addDataSourceProperty(var95[22], Boolean.TRUE);
         } catch (ParseException | SQLException var36) {
            var10000 = var36;
            boolean var96 = false;
            break label329;
         }

         HikariConfig var57 = var1;

         try {
            var57.addDataSourceProperty(iP[23], Boolean.TRUE);
         } catch (ParseException | SQLException var35) {
            var10000 = var35;
            boolean var97 = false;
            break label329;
         }

         try {
            var1.addDataSourceProperty(iP[24], Boolean.TRUE);
         } catch (ParseException | SQLException var34) {
            var10000 = var34;
            boolean var98 = false;
            break label329;
         }

         HikariConfig var58 = var1;

         try {
            var58.addDataSourceProperty(iP[25], Boolean.TRUE);
         } catch (ParseException | SQLException var33) {
            var10000 = var33;
            boolean var99 = false;
            break label329;
         }

         HikariConfig var59 = var1;
         String var100 = iP[26];

         try {
            var59.addDataSourceProperty(var100, Boolean.TRUE);
         } catch (ParseException | SQLException var32) {
            var10000 = var32;
            boolean var101 = false;
            break label329;
         }

         HikariConfig var60 = var1;
         String[] var102 = iP;

         try {
            var60.addDataSourceProperty(var102[27], Boolean.TRUE);
         } catch (ParseException | SQLException var31) {
            var10000 = var31;
            boolean var103 = false;
            break label329;
         }

         try {
            var1.addDataSourceProperty(iP[28], Boolean.FALSE);
         } catch (ParseException | SQLException var30) {
            var10000 = var30;
            boolean var104 = false;
            break label329;
         }

         try {
            var1.addDataSourceProperty(iP[29], Boolean.FALSE);
         } catch (ParseException | SQLException var29) {
            var10000 = var29;
            boolean var105 = false;
            break label329;
         }

         try {
            var1.addDataSourceProperty(iP[30], Boolean.TRUE);
         } catch (ParseException | SQLException var28) {
            var10000 = var28;
            boolean var106 = false;
            break label329;
         }

         HikariConfig var61 = var1;
         String[] var107 = iP;
         byte var141 = 31;

         try {
            var61.setDataSourceClassName(var107[var141]);
         } catch (ParseException | SQLException var27) {
            var10000 = var27;
            boolean var108 = false;
            break label329;
         }

         try {
            var1.addDataSourceProperty(iP[32], me.nik.alice.UN.dx.o4.b());
         } catch (ParseException | SQLException var26) {
            var10000 = var26;
            boolean var109 = false;
            break label329;
         }

         HikariConfig var62 = var1;

         try {
            var62.addDataSourceProperty(iP[33], me.nik.alice.UN.dx.tr.Aa());
         } catch (ParseException | SQLException var25) {
            var10000 = var25;
            boolean var110 = false;
            break label329;
         }

         HikariConfig var63 = var1;
         String var111 = iP[34];
         me.nik.alice.UN.dx var142 = me.nik.alice.UN.dx.pO;

         try {
            var63.addDataSourceProperty(var111, var142.b());
         } catch (ParseException | SQLException var24) {
            var10000 = var24;
            boolean var112 = false;
            break label329;
         }

         HikariConfig var64 = var1;
         String var113 = iP[35];
         me.nik.alice.UN.dx var143 = me.nik.alice.UN.dx.DP;

         try {
            var64.addDataSourceProperty(var113, var143.b());
         } catch (ParseException | SQLException var23) {
            var10000 = var23;
            boolean var114 = false;
            break label329;
         }

         HikariConfig var65 = var1;
         String var115 = iP[36];

         try {
            var65.addDataSourceProperty(var115, me.nik.alice.UN.dx.p6.b());
         } catch (ParseException | SQLException var22) {
            var10000 = var22;
            boolean var116 = false;
            break label329;
         }

         HikariConfig var66 = var1;
         String var117 = iP[37];
         me.nik.alice.UN.dx var144 = me.nik.alice.UN.dx.R2;

         try {
            var66.addDataSourceProperty(var117, String.valueOf(var144.dC()));
         } catch (ParseException | SQLException var21) {
            var10000 = var21;
            boolean var118 = false;
            break label329;
         }

         Iy var67 = this;
         HikariDataSource var119 = new HikariDataSource;
         HikariDataSource var145 = var119;

         try {
            var145.<init>(var1);
            var67.dx = var119;
         } catch (ParseException | SQLException var20) {
            var10000 = var20;
            boolean var120 = false;
            break label329;
         }

         try {
            var68 = this.dx.getConnection().prepareStatement(iP[38]);
         } catch (ParseException | SQLException var19) {
            var10000 = var19;
            boolean var121 = false;
            break label329;
         }

         try {
            var48 = var68;
            var68.executeUpdate();
         } catch (ParseException | SQLException var18) {
            var10000 = var18;
            boolean var122 = false;
            break label329;
         }

         try {
            var48.close();
         } catch (ParseException | SQLException var17) {
            var10000 = var17;
            boolean var123 = false;
            break label329;
         }

         Iy var69 = this;

         try {
            var70 = var69.getConnection().createStatement(1004, 1008);
         } catch (ParseException | SQLException var16) {
            var10000 = var16;
            boolean var124 = false;
            break label329;
         }

         Statement var49 = var70;
         String var71 = iP[39];
         Statement var72 = var49;
         String[] var125 = iP;
         byte var146 = 40;

         ResultSet var2;
         try {
            var2 = var72.executeQuery(var125[var146]);
         } catch (ParseException | SQLException var15) {
            var10000 = var15;
            boolean var126 = false;
            break label329;
         }

         SimpleDateFormat var73 = new SimpleDateFormat;
         SimpleDateFormat var127 = var73;
         String[] var147 = iP;
         byte var10003 = 41;

         SimpleDateFormat var3;
         try {
            var127.<init>(var147[var10003]);
            var3 = var73;
         } catch (ParseException | SQLException var14) {
            var10000 = var14;
            boolean var128 = false;
            break label329;
         }

         try {
            var74 = new Date;
            var130 = var74;
            var148 = System.currentTimeMillis();
         } catch (ParseException | SQLException var13) {
            var10000 = var13;
            boolean var129 = false;
            break label329;
         }

         try {
            var130.<init>(var148);
         } catch (ParseException | SQLException var12) {
            var10000 = var12;
            boolean var131 = false;
            break label329;
         }

         Date var4 = var74;

         while (true) {
            try {
               if (!var2.next()) {
                  break;
               }
            } catch (ParseException | SQLException var11) {
               var10000 = var11;
               boolean var132 = false;
               break label329;
            }

            try {
               var75 = var3.parse(var2.getString(iP[42]));
            } catch (ParseException | SQLException var10) {
               var10000 = var10;
               boolean var133 = false;
               break label329;
            }

            try {
               var76 = var75.getTime() - var4.getTime();
            } catch (ParseException | SQLException var9) {
               var10000 = var9;
               boolean var134 = false;
               break label329;
            }

            try {
               var77 = Math.abs(var76);
            } catch (ParseException | SQLException var8) {
               var10000 = var8;
               boolean var135 = false;
               break label329;
            }

            if (var77 > this.jD) {
               ResultSet var78 = var2;

               try {
                  var78.deleteRow();
               } catch (ParseException | SQLException var7) {
                  var10000 = var7;
                  boolean var136 = false;
                  break label329;
               }
            }
         }

         try {
            var2.close();
         } catch (ParseException | SQLException var6) {
            var10000 = var6;
            boolean var137 = false;
            break label329;
         }

         try {
            var49.getConnection().close();
            return;
         } catch (ParseException | SQLException var5) {
            var10000 = var5;
            boolean var138 = false;
         }
      }

      SQLException var50 = var10000;
      this.plugin.getLogger().info(iP[43]);
      var50.printStackTrace();
      this.dx.close();
   }

   @Override
   public List dx(String var1) {
      CompletableFuture var3 = CompletableFuture.supplyAsync(this::Aa);
      CompletableFuture var10000 = var3;
      long var10001 = 10L;
      TimeUnit var10002 = TimeUnit.SECONDS;

      try {
         var4 = var10000.get(var10001, var10002);
      } catch (ExecutionException | TimeoutException | InterruptedException var2) {
         this.plugin.getLogger().severe(iP[2]);
         var2.printStackTrace();
         return new ArrayList();
      }

      return (List)var4;
   }

   @Override
   public List h5() {
      CompletableFuture var1 = CompletableFuture.supplyAsync(this::Fh);

      try {
         return (List)var1.get(10L, TimeUnit.SECONDS);
      } catch (ExecutionException | TimeoutException | InterruptedException var2) {
         this.plugin.getLogger().severe(iP[1]);
         var2.printStackTrace();
         return new ArrayList();
      }
   }
}
