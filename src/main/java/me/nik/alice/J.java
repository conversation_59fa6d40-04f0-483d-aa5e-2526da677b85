package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class J extends PacketCheck {
   private static String[] Ia;

   @Override
   public String sB() {
      return Ia[1];
   }

   static {
      KD();
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().Q() && !this.Aa.dx().jA()) {
         ES var7;
         double var3 = (var7 = this.Aa.dx()).Ch();
         double var5 = 0.42F;
         int var2;
         if ((var2 = var7.dx().og(2000L)) > 0) {
            var5 = 0.42F + (double)((float)var2 * 0.11F);
         }

         Qk var8;
         if ((var8 = this.Aa.dx()).tr()) {
            var5 += Math.abs(var8.getVelocityY() * 1.25);
         }

         if (var7.R2() < 60) {
            var5 += 1.15 * Math.abs(var3);
         }

         if (var7.bI() && var3 > var5) {
            this.og(Ia[2] + var3 + Ia[3] + var5);
         }
      }
   }

   public J(UC var1) {
      super(var1, CheckType.MOTION, Ia[0], Category.MOVE);
   }

   private static void KD() {
      Ia = new String[]{"B", "Checks for high jump", "High Jump, Delta Y: ", " expected: "};
   }
}
