package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class J4 extends PacketCheck {
   private static String[] bH;

   static {
      1x();
   }

   private static void _x/* $VF was: 1x*/() {
      bH = new String[]{"V", "Checks for invalid keep alive packets"};
   }

   @Override
   public void dx(DH var1) {
   }

   public J4(UC var1) {
      super(var1, CheckType.PACKET, bH[0], Category.WORLD);
   }

   @Override
   public String sB() {
      return bH[1];
   }
}
