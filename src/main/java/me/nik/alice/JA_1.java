package me.nik.alice;

import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import me.nik.fastmath.FastMath;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;
import org.bukkit.util.Vector;

public final class JA {
   private static final double iK = 16.0;

   public static int dx(Player var0, PotionEffectType var1) {
      String var4 = var1.getName();
      Iterator var3 = var0.getActivePotionEffects().iterator();

      while (var3.hasNext()) {
         PotionEffect var2;
         if (Integer.valueOf(var4.hashCode()).equals((var2 = (PotionEffect)var3.next()).getType().getName().hashCode())) {
            return var2.getAmplifier() + 1;
         }
      }

      return 0;
   }

   public static boolean dx(Player var0, String var1) {
      ItemStack[] var6;
      if ((var6 = var0.getInventory().getArmorContents()).length == 0) {
         return false;
      } else {
         int var2 = var6.length;

         for (int var3 = 0; var3 < var2; var3++) {
            ItemStack var4;
            ItemStack var10000 = var4 = var6[var3];

            try {
               var10000.getClass();
            } catch (Exception var5) {
               continue;
            }

            if (var4.getType().toString().contains(var1)) {
               return true;
            }
         }

         return false;
      }
   }

   public static ItemStack dx(Player var0) {
      return w.v() ? var0.getItemInHand() : var0.getInventory().getItemInMainHand();
   }

   public static int dx(Player var0) {
      Player var10000 = var0;

      try {
         var10000.getClass();
      } catch (Exception var4) {
         return 0;
      }

      Location var6;
      Vector var1 = (var6 = var0.getEyeLocation()).getDirection().normalize();

      for (int var2 = 1; var2 <= 5; var2++) {
         var6.add(var1);
         Block var3;
         Block var7 = var3 = fD.dx(var6);

         try {
            var7.getClass();
         } catch (Exception var5) {
            break;
         }

         if (!var3.isEmpty()) {
            return var2;
         }
      }

      return 6;
   }

   public static float dx(UC var0) {
      float var1 = FastMath.max(0.2F, var0.getPlayer().getWalkSpeed()) - 0.2F;
      int var2 = var0.dx().dx().sB(2500L);
      return 0.36F + (float)var2 * 0.062F + var1 * 1.6F;
   }

   public static boolean sB(Player var0) {
      if (!w.v()) {
         ItemStack var10000 = var0.getInventory().getBoots();

         try {
            var10000.equals(null);
         } catch (Exception var1) {
            return false;
         }

         if (var0.getInventory().getBoots().containsEnchantment(Enchantment.FROST_WALKER)) {
            return true;
         }
      }

      return false;
   }

   public static int Aa(Player var0) {
      ItemStack var10000 = var0.getInventory().getBoots();

      try {
         var10000.toString();
      } catch (Exception var1) {
         return 0;
      }

      return dx(var0.getInventory().getBoots(), Enchantment.DEPTH_STRIDER) > 0
         ? (Integer)var0.getInventory().getBoots().getEnchantments().get(Enchantment.DEPTH_STRIDER)
         : 0;
   }

   private JA() {
   }

   public static float Aa(UC var0) {
      float var1 = FastMath.max(0.2F, var0.getPlayer().getWalkSpeed()) - 0.2F;
      int var2 = var0.dx().dx().sB(2500L);
      return 0.2867F + (float)var2 * 0.079F + var1 * 1.6F;
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   public static List dx(Player var0, double var1) {
      LinkedList var3 = new LinkedList();
      Player var10000 = var0;

      World var4;
      try {
         var4 = var10000.getWorld();
      } catch (Exception var27) {
         boolean var10001 = false;
         return var3;
      }

      Location var5;
      double var6;
      try {
         var6 = (var5 = var0.getLocation()).getX();
      } catch (Exception var26) {
         boolean var40 = false;
         return var3;
      }

      double var8;
      try {
         var8 = var5.getZ();
      } catch (Exception var25) {
         boolean var41 = false;
         return var3;
      }

      double var31 = var6 - var1;
      double var42 = 16.0;

      try {
         var32 = FastMath.floorInt(var31 / var42);
      } catch (Exception var24) {
         boolean var43 = false;
         return var3;
      }

      int var10 = var32;

      try {
         var28 = FastMath.floorInt((var6 + var1) / 16.0);
      } catch (Exception var23) {
         boolean var44 = false;
         return var3;
      }

      double var33 = var8 - var1;
      var42 = 16.0;

      int var7;
      try {
         var7 = FastMath.floorInt(var33 / var42);
      } catch (Exception var22) {
         boolean var46 = false;
         return var3;
      }

      double var34 = (var8 + var1) / 16.0;

      try {
         var29 = FastMath.floorInt(var34);
      } catch (Exception var21) {
         boolean var47 = false;
         return var3;
      }

      for (int var9 = var10; var9 <= var28; var9++) {
         for (int var30 = var7; var30 <= var29; var30++) {
            World var35 = var4;
            int var48 = var9;
            int var10002 = var30;

            try {
               var36 = var35.isChunkLoaded(var48, var10002);
            } catch (Exception var18) {
               boolean var49 = false;
               return var3;
            }

            if (var36) {
               World var37 = var4;
               int var50 = var9;

               Entity[] var11;
               int var12;
               try {
                  var12 = (var11 = var37.getChunkAt(var50, var30).getEntities()).length;
               } catch (Exception var17) {
                  boolean var51 = false;
                  return var3;
               }

               for (int var13 = 0; var13 < var12; var13++) {
                  Entity var14;
                  try {
                     Entity var38 = var14 = var11[var13];

                     try {
                        var38.getClass();
                     } catch (Exception var19) {
                        break;
                     }
                  } catch (Exception var20) {
                     boolean var52 = false;
                     return var3;
                  }

                  if (var14 != var0) {
                     try {
                        var39 = var14.getLocation().distanceSquared(var5);
                        var42 = var1 * var1;
                     } catch (Exception var16) {
                        boolean var53 = false;
                        return var3;
                     }

                     if (!(var39 > var42)) {
                        try {
                           var3.add(var14);
                        } catch (Exception var15) {
                           boolean var55 = false;
                           return var3;
                        }
                     }
                  }
               }
            }
         }
      }

      return var3;
   }

   public static int dx(ItemStack var0, Enchantment var1) {
      ItemStack var10000 = var0;

      try {
         var10000.hashCode();
      } catch (Exception var2) {
         return 0;
      }

      return var0.getEnchantmentLevel(var1);
   }

   public static float dx(Player var0, Entity var1) {
      Player var10000 = var0;

      try {
         var10000.hashCode();
      } catch (Exception var4) {
         return 0.0F;
      }

      Entity var7 = var1;

      try {
         var7.getClass();
      } catch (Exception var3) {
         return 0.0F;
      }

      Vector var2 = var0.getEyeLocation().getDirection();
      Vector var5 = var0.getEyeLocation().toVector();
      Vector var6 = var1.getLocation().toVector().subtract(var5);
      return var2.angle(var6);
   }

   public static int Aa(double var0) {
      float var2 = Math.abs((float)var0);
      int var1 = 0;

      do {
         var2 = (var2 - 0.02F) * 0.91F;
         var1++;
      } while (var2 > -0.05F);

      return var1;
   }
}
