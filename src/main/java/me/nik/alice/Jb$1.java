package me.nik.alice;

import me.nik.alice.api.events.AlicePunishWaveEvent;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitRunnable;

class Jb$1 extends BukkitRunnable {
   final Jb dx;
   private static String[] xK;

   Jb$1(Jb var1) {
      this.dx = var1;
   }

   private static void ef() {
      xK = new String[]{"%countdown%"};
   }

   static {
      ef();
   }

   public void run() {
      boolean var4 = HHQz;
      if (Jb.dx(this.dx) > 1) {
         Bukkit.broadcastMessage(F.tm.getMessage().replace(xK[0], String.valueOf(Jb.Aa(this.dx))));
      } else {
         String var1 = me.nik.alice.UN.dx.cO.b();

         for (String var3 : Jb.dx(this.dx).dx().dx()) {
            Alice.getAPI().punish(var3, var1);
            if (var4) {
               throw null;
            }
         }

         AlicePunishWaveEvent var5 = new AlicePunishWaveEvent(Jb.dx(this.dx).dx().dx());
         Bukkit.getPluginManager().callEvent(var5);
         Jb.dx(this.dx).dx().Zp();
         Jb.dx(this.dx).dx().Aa(false);
         Jb.dx(this.dx);
         this.cancel();
      }
   }
}
