package me.nik.alice;

import me.nik.alice.Jb.1;
import org.bukkit.scheduler.BukkitRunnable;

public class Jb extends BukkitRunnable {
   private final Alice plugin;
   private int z;

   static void dx(Jb var0) {
      var0.tV();
   }

   static int dx(Jb var0) {
      return var0.z--;
   }

   public void run() {
      if (!this.plugin.dx().dx().isEmpty() && dC.og()) {
         this.plugin.dx().Aa(true);
         new 1(this).runTaskTimer(this.plugin, 0L, 20L);
      }
   }

   static int Aa(Jb var0) {
      return var0.z;
   }

   private void tV() {
      this.z = me.nik.alice.UN.dx.a.Aa();
   }

   public Jb(Alice var1) {
      this.plugin = var1;
      this.tV();
   }

   static Alice dx(Jb var0) {
      return var0.plugin;
   }
}
