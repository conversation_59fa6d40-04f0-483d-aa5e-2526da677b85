package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import org.bukkit.GameMode;

public class K extends PacketCheck {
   private static String[] 3J;

   public K(UC var1) {
      super(var1, CheckType.PACKET, 3J[0], Category.WORLD);
   }

   private static void gW() {
      3J = new String[]{"R", "Checks for invalid creative slot packets", "Sent creative slot packet without creative"};
   }

   static {
      gW();
   }

   @Override
   public String sB() {
      return 3J[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.iK() && this.Aa.getPlayer().getGameMode() != GameMode.CREATIVE) {
         this.og(3J[2]);
      }
   }
}
