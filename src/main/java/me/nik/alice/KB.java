package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import org.bukkit.block.Block;

public class KB extends PacketCheck {
   private static String[] Ph;

   private static void w3() {
      Ph = new String[]{"C", "Checks for impossible accelerations when bridging", "Dy: ", " accel: "};
   }

   @Override
   public String sB() {
      return Ph[1];
   }

   public KB(UC var1) {
      super(var1, CheckType.SCAFFOLD, Ph[0], Category.WORLD, 2.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().Fh() && this.Aa.dx().DB()) {
         Block var7;
         Block var10000 = var7 = this.Aa.dx().Aa();

         try {
            var10000.toString();
         } catch (Exception var6) {
            return;
         }

         if (var7.getType().isSolid()) {
            double var4 = this.Aa.dx().DB();
            float var8;
            if ((var8 = this.Aa.dx().b()) >= 10.0F && var4 >= 0.1 && this.Aa.dx().VL() < 2) {
               this.Aa(Ph[2] + var8 + Ph[3] + var4);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.25);
            }
         }
      }
   }

   static {
      w3();
   }
}
