package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientUseEntity;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

@h5
public class KJ extends PacketCheck {
   private static String[] hs;

   private static void fS() {
      hs = new String[]{"F", "Checks for attacks without line of sight", "Attack without line of sight"};
   }

   static {
      fS();
   }

   public KJ(UC var1) {
      super(var1, CheckType.KILLAURA, hs[0], Category.COMBAT, 5.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.Tq() && this.Aa.dx().cO() >= 20 && this.Aa.dx().yk() >= 20 && !this.Aa.dx().dx().DP()) {
         Player var2;
         Player var10000 = var2 = this.Aa.getPlayer();

         try {
            var10000.equals(null);
         } catch (Exception var3) {
            return;
         }

         Entity var4;
         if ((var4 = new WrapperPlayClientUseEntity(var1.dx()).getTarget(var2.getWorld())) instanceof LivingEntity) {
            if (!var2.hasLineOfSight(var4)) {
               this.Aa(hs[2]);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.75);
            }
         }
      }
   }

   @Override
   public String sB() {
      return hs[1];
   }
}
