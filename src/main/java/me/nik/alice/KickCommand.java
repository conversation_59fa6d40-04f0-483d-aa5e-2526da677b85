package me.nik.alice;

import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class Al extends Zc {
   private final Alice plugin;
   private static String[] g7;

   @Override
   protected int dx() {
      return 3;
   }

   private static void _l/* $VF was: 1l*/() {
      g7 = new String[]{
         "kick",
         "Kick a player from the server",
         "/alice kick <player> <reason>",
         "%player%",
         " ",
         "%nl%",
         "\n",
         "%prefix%",
         "Kicked from the Server!",
         "%player%"
      };
   }

   @Override
   protected List dx(CommandSender var1, String[] var2) {
      return null;
   }

   @Override
   protected String h5() {
      return ZM.gz.h5();
   }

   @Override
   protected String Zh() {
      return g7[2];
   }

   @Override
   protected void dx(CommandSender var1, String[] var2) {
      Player var3;
      Player var10000 = var3 = Bukkit.getPlayer(var2[1]);

      try {
         var10000.equals(null);
      } catch (Exception var6) {
         var1.sendMessage(F.UH.getMessage().replace(g7[3], var2[1]));
         return;
      }

      StringBuilder var4 = new StringBuilder();

      for (int var5 = 2; var5 < var2.length; var5++) {
         var4.append(var2[var5]).append(g7[4]);
      }

      String var7;
      if ((var7 = Dq.sB(var4.toString().trim().replaceAll(g7[5], g7[6]).replace(g7[7], F.dx.getMessage()))).isEmpty()) {
         var7 = g7[8];
      }

      var3.kickPlayer(var7);
      var1.sendMessage(F.yM.getMessage().replace(g7[9], var2[1]));
   }

   @Override
   protected String getDescription() {
      return g7[1];
   }

   public Al(Alice var1) {
      this.plugin = var1;
   }

   @Override
   protected String getName() {
      return g7[0];
   }

   static {
      1l();
   }

   @Override
   protected boolean AC() {
      return true;
   }
}
