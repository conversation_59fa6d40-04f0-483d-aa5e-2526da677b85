package me.nik.alice;

import me.nik.fastmath.FastMath;
import org.bukkit.scheduler.BukkitRunnable;

public class Ku extends BukkitRunnable {
   private static double P = 20.0;
   private final Alice plugin;
   private long xx;
   private static int Fh;
   private long og = System.currentTimeMillis();
   private int mQ;
   private static long R;
   private static long P;

   public static long Vm() {
      return l.og(R);
   }

   public static double QJ() {
      return P;
   }

   public Ku(Alice var1) {
      this.plugin = var1;
   }

   public static long WB() {
      return P;
   }

   public void run() {
      if (++Fh >= 20) {
         long var1;
         P = (var1 = System.currentTimeMillis()) - this.og;
         this.og = var1;
         long var3 = var1 / 1000L;
         if (this.xx == var3) {
            this.mQ++;
         } else {
            this.xx = var3;
            P = FastMath.min(l.Aa((P + (double)this.mQ) / 2.0, 2), 20.0);
            this.mQ = 1;
         }

         if (P >= me.nik.alice.UN.dx.yM.Aa() || P <= me.nik.alice.UN.dx.UH.dx()) {
            R = var1;
         }
      }

      this.plugin.dx().dx().values().forEach(UC::u);
   }

   public static int iK() {
      return Math.abs(Fh);
   }
}
