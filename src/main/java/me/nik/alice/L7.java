package me.nik.alice;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.ProtocolLibrary;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.ListenerPriority;
import com.comphenix.protocol.events.PacketAdapter;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.events.PacketEvent;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import me.nik.alice.wrappers.WrapperPlayServerEntityVelocity;
import org.bukkit.entity.Player;

public class L7 extends PacketAdapter {
   private static final List yM = new ArrayList();
   private final Alice plugin;

   static {
      Client.getInstance().values().stream().filter(L7::dx).forEach(yM::add);
      yM.removeAll(
         Arrays.asList(
            Client.DIFFICULTY_CHANGE,
            Client.AUTO_RECIPE,
            Client.ITEM_NAME,
            Client.TAB_COMPLETE,
            Client.BOAT_MOVE,
            Client.ADVANCEMENTS,
            Client.B_EDIT,
            Client.BEACON,
            Client.DIFFICULTY_LOCK,
            Client.UPDATE_SIGN,
            Client.TR_SEL,
            Client.TILE_NBT_QUERY,
            Client.TELEPORT_ACCEPT,
            Client.STRUCT,
            Client.SET_JIGSAW,
            Client.SET_COMMAND_MINECART,
            Client.SET_COMMAND_BLOCK,
            Client.PICK_ITEM,
            Client.ENTITY_NBT_QUERY,
            Client.ENCHANT_ITEM
         )
      );
      yM.add(Server.ENTITY_VELOCITY);
      yM.add(Server.TRANSACTION);
      yM.add(Server.POSITION);
      yM.add(Server.KEEP_ALIVE);
      yM.add(Server.EXPLOSION);
      yM.add(Server.ABILITIES);
   }

   private static void dx(UC var0, PacketContainer var1) {
      var0.og(new DH(var1));
   }

   public L7(Alice var1) {
      super(var1, ListenerPriority.MONITOR, yM);
      this.plugin = var1;
      ProtocolLibrary.getProtocolManager().addPacketListener(this);
   }

   private static boolean dx(PacketType var0) {
      return var0.isSupported() && !var0.isDeprecated();
   }

   private static void dx(UC var0, PacketEvent var1) {
      var0.og(new DH(var1.getPacket()));
   }

   public void onPacketReceiving(PacketEvent var1) {
      if (!var1.isPlayerTemporary()) {
         Player var10000 = var1.getPlayer();

         try {
            var10000.hashCode();
         } catch (Exception var4) {
            return;
         }

         UC var2;
         UC var5 = var2 = this.plugin.dx().dx(var1.getPlayer());

         try {
            var5.hashCode();
         } catch (Exception var3) {
            return;
         }

         var2.og().execute(L7::dx);
      }
   }

   public void onPacketSending(PacketEvent var1) {
      if (!var1.isPlayerTemporary()) {
         Player var10000 = var1.getPlayer();

         try {
            var10000.equals(null);
         } catch (Exception var6) {
            return;
         }

         Player var2 = var1.getPlayer();
         UC var3;
         UC var8 = var3 = this.plugin.dx().dx(var2);

         try {
            var8.toString();
         } catch (Exception var5) {
            return;
         }

         int var7 = var2.getEntityId();
         PacketContainer var4 = var1.getPacket();
         if (var1.getPacketType() != Server.ENTITY_VELOCITY || new WrapperPlayServerEntityVelocity(var4).getEntityID() == var7) {
            var3.og().execute(L7::dx);
         }
      }
   }
}
