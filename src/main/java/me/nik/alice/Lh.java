package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientWindowClick;

public class Lh extends PacketCheck {
   private static String[] Xr;

   public Lh(UC var1) {
      super(var1, CheckType.PACKET, Xr[0], Category.WORLD);
   }

   private static void Gf() {
      Xr = new String[]{"W", "Checks for invalid window click packets", "Invalid window click packet, slot: "};
   }

   @Override
   public String sB() {
      return Xr[1];
   }

   static {
      Gf();
   }

   @Override
   public void dx(DH var1) {
      if (var1.Lh()) {
         int var2;
         if ((var2 = new WrapperPlayClientWindowClick(var1.dx()).getSlot()) < 0 && var2 != -999 && var2 != -1) {
            this.og(Xr[2].concat(String.valueOf(var2)));
         }
      }
   }
}
