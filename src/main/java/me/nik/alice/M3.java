package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientSettings;

public class M3 extends PacketCheck {
   private int UH = -1;
   private long VL;
   private static String[] w7;

   public M3(UC var1) {
      super(var1, CheckType.PACKET, w7[0], Category.WORLD, 3.0F);
   }

   private static void C6() {
      w7 = new String[]{"I", "Checks for skin blink", "Delta: ", " delta XZ: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.K()) {
         int var2 = new WrapperPlayClientSettings(var1.dx()).getDisplayedSkinParts();
         int var3 = this.UH;
         this.UH = var2;
         if (var3 != var2) {
            long var5 = this.VL;
            this.VL = var1.getTimeStamp();
            long var7 = l.og(var5);
            double var9 = this.Aa.dx().F7();
            if (var7 <= 100L && var9 > 0.1) {
               this.Aa(w7[2] + var7 + w7[3] + var9);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.75);
            }
         }
      }
   }

   static {
      C6();
   }

   @Override
   public String sB() {
      return w7[1];
   }
}
