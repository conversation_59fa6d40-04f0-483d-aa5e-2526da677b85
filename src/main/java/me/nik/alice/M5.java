package me.nik.alice;

import java.util.Comparator;
import java.util.LinkedList;
import java.util.List;
import org.bukkit.Location;

public class M5 {
   private static String[] ZX;
   private final Ne dx = new Ne(20, true);

   public void dx(Location var1) {
      this.dx.add(new bV(var1));
   }

   public List dx(long var1, long var3) {
      if (var3 <= 200L && var3 >= 100L) {
         if (var1 > 1000L) {
            var1 = 1000L;
         }

         LinkedList var5 = new LinkedList();
         long var6 = System.currentTimeMillis();
         this.dx.stream().sorted(Comparator.comparingLong(M5::dx)).filter(M5::dx).forEach(var5::add);
         return var5;
      } else {
         throw new xt(ZX[0].concat(String.valueOf(var3)));
      }
   }

   static {
      Dk();
   }

   private static long dx(long var0, long var2, bV var4) {
      return Math.abs(var4.getTimeStamp() - (var0 - var2));
   }

   private static boolean dx(long var0, long var2, long var4, bV var6) {
      return Math.abs(var6.getTimeStamp() - (var0 - var2)) < var4;
   }

   private static void Dk() {
      ZX = new String[]{"Delta cannot be higher or lower than "};
   }

   public boolean h5() {
      return this.dx.h5();
   }

   public void clear() {
      this.dx.clear();
   }
}
