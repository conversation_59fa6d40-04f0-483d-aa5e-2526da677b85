package me.nik.alice;

import com.sun.management.OperatingSystemMXBean;
import java.lang.management.ManagementFactory;
import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;
import javax.management.JMX;
import javax.management.MBeanServer;
import javax.management.ObjectName;
import me.nik.fastmath.FastMath;
import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

public class MB implements Runnable {
   private final Queue h5;
   private final OperatingSystemMXBean dx;
   private final Queue AC;
   private static BukkitTask dx;
   private final Queue dC;
   private final Queue sB;
   private static String[] 2I;
   private final Queue Zh;
   private final Queue Fh;
   private final Queue og = new ConcurrentLinkedQueue();

   static {
      AS();
   }

   public void og(long var1) {
      if (this.isRunning()) {
         this.Zh.add(var1);
      }
   }

   public ZU dx() {
      if (this.isRunning()) {
         return ZU.dx;
      } else {
         dx = Bukkit.getScheduler().runTaskTimerAsynchronously(Alice.dx(), this, 20L, 20L);
         return ZU.og;
      }
   }

   public void AC(long var1) {
      if (this.isRunning()) {
         this.AC.add(var1);
      }
   }

   public ZU Aa() {
      if (!this.isRunning()) {
         return ZU.Aa;
      } else {
         dx.cancel();
         dx = null;
         return ZU.AC;
      }
   }

   public void sB(long var1) {
      if (this.isRunning()) {
         this.sB.add(var1);
      }
   }

   public MB() {
      this.AC = new ConcurrentLinkedQueue();
      this.sB = new ConcurrentLinkedQueue();
      this.dC = new ConcurrentLinkedQueue();
      this.Zh = new ConcurrentLinkedQueue();
      this.h5 = new ConcurrentLinkedQueue();
      this.Fh = new ConcurrentLinkedQueue();

      try {
         MBeanServer var1 = ManagementFactory.getPlatformMBeanServer();
         ObjectName var2 = ObjectName.getInstance(2I[0]);
         this.dx = (OperatingSystemMXBean)JMX.newMXBeanProxy(var1, var2, OperatingSystemMXBean.class);
      } catch (Exception var3) {
         throw new xt(2I[1]);
      }
   }

   public void Aa(long var1) {
      if (this.isRunning()) {
         this.dC.add(var1);
      }
   }

   public boolean isRunning() {
      BukkitTask var10000 = dx;

      try {
         var10000.equals(null);
         return true;
      } catch (Exception var1) {
         return false;
      }
   }

   public void run() {
      OperatingSystemMXBean var10000 = this.dx;

      label13: {
         try {
            var10000.hashCode();
         } catch (Exception var1) {
            break label13;
         }

         this.og.add(this.dx.getSystemCpuLoad());
      }

      this.h5.add(Ku.QJ());
      this.Fh.add(Ku.WB());
   }

   public mz dx() {
      double var1 = this.og.isEmpty() ? 0.0 : l.Aa(this.og.stream().mapToDouble(Double::doubleValue).average().orElse(0.0) * 100.0, 2);
      long var3 = this.sB.isEmpty() ? 0L : (long)this.sB.stream().mapToLong(Long::longValue).average().orElse(0.0);
      long var5 = this.AC.isEmpty() ? 0L : (long)this.AC.stream().mapToLong(Long::longValue).average().orElse(0.0);
      long var7 = this.Zh.isEmpty() ? 0L : (long)this.Zh.stream().mapToLong(Long::longValue).average().orElse(0.0);
      long var9 = this.dC.isEmpty() ? 0L : (long)this.dC.stream().mapToLong(Long::longValue).average().orElse(0.0);
      double var11 = (double)FastMath.round(this.h5.stream().mapToDouble(Double::doubleValue).average().orElse(0.0));
      long var13 = (long)this.Fh.stream().mapToLong(Long::longValue).average().orElse(0.0);
      long var15 = var3 + var5 + var7 + var9;
      long var17 = (long)(this.sB.size() + this.AC.size() + this.Zh.size() + this.dC.size() + this.h5.size() + this.Fh.size());
      this.sB.clear();
      this.AC.clear();
      this.Zh.clear();
      this.dC.clear();
      this.h5.clear();
      this.Fh.clear();
      return new mz(var17, var1, var11, var13, var3, var7, var5, var9, var15);
   }

   private static void AS() {
      2I = new String[]{"java.lang:type=OperatingSystem", "OperatingSystemMXBean is not supported, Alice will be unable to get the CPU Usage"};
   }
}
