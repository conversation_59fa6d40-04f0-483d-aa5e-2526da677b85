package me.nik.alice;

import com.comphenix.protocol.events.PacketContainer;
import java.util.ArrayDeque;
import java.util.Deque;
import java.util.Iterator;
import me.nik.alice.wrappers.WrapperPlayClientPositionLook;
import me.nik.alice.wrappers.WrapperPlayServerPosition;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.event.player.PlayerTeleportEvent.TeleportCause;

public class MN {
   private static String[] 8L;
   private boolean nZ;
   private long pO;
   private long R2;
   private final UC Aa;
   private long DP;
   private float x;
   private long tr;
   private int f7;
   private double Eu;
   private double nZ;
   private int Pu;
   private final Deque Aa = new ArrayDeque();
   private long p6;

   private static void NA() {
      8L = new String[]{"BadPackets", "DD", "Y: ", " last void tp: ", "Timed out"};
   }

   public void AC(Event var1) {
      if (var1 instanceof PlayerTeleportEvent) {
         PlayerTeleportEvent var23 = (PlayerTeleportEvent)var1;
         long var3 = System.currentTimeMillis();
         if (var23.getCause() == TeleportCause.PLUGIN) {
            this.R2 = var3;
         }

         if (me.nik.alice.UN.dx.GE.dC() && !Alice.dx().dx().dx(this.Aa.dx())) {
            long var5 = l.og(this.p6);
            this.p6 = var3;
            if (var5 <= 3000L && (long)this.Aa.dx().dx().uX() >= 5L) {
               if (this.f7++ >= 50 && this.Aa.dx().Aa(3)) {
                  this.f7 = 0;
                  this.Aa.sB(8L[4]);
               }
            } else {
               this.f7 = 0;
            }
         }

         if (var23.getCause() == TeleportCause.UNKNOWN) {
            Location var27 = var23.getFrom();
            Location var6;
            Location var28 = var6 = var23.getTo();

            try {
               var28.equals(null);
            } catch (Exception var22) {
               return;
            }

            double var7 = Math.abs(var27.getX() - var6.getX());
            double var9 = Math.abs(var27.getY() - var6.getY());
            double var11 = Math.abs(var27.getZ() - var6.getZ());
            ES var24;
            double var14 = Math.abs((var24 = this.Aa.dx()).Ch());
            double var16 = Math.abs(var24.F7());
            double var18 = Math.abs(var24.iv());
            double var20 = Math.abs(var24.Zp());
            boolean var25;
            boolean var2 = !(var25 = var7 == 0.0 && var9 == 0.0 && var11 == 0.0) && var9 == var14 && var14 >= 5.0;
            boolean var26 = !var25 && (var14 > 1.0 && var14 == var18 || var16 > 1.0 && var16 == var20);
            if (var2 || var26) {
               this.tr = System.currentTimeMillis();
            }
         }
      }
   }

   public boolean o4() {
      return this.nZ;
   }

   public MN(UC var1) {
      this.Aa = var1;
   }

   public double Zm() {
      return this.Eu;
   }

   public int GE() {
      return this.Pu;
   }

   private void dx(double var1) {
      long var3 = l.og(this.DP);
      this.DP = System.currentTimeMillis();
      if (var1 < 1.0 && var3 < 5000L && this.VL() > 5000L) {
         if (this.x++ > 2.0F) {
            Alice.getAPI().flag(this.Aa.getPlayer(), 8L[0], 8L[1], 8L[2] + var1 + 8L[3] + var3);
            return;
         }
      } else {
         this.x = Math.max(0.0F, this.x - 1.0F);
      }
   }

   public double jA() {
      return this.nZ;
   }

   public void AC(DH var1) {
      Player var10000 = this.Aa.getPlayer();

      try {
         var10000.hashCode();
      } catch (Exception var20) {
         return;
      }

      PacketContainer var2 = var1.dx();
      if (var1.qa()) {
         WrapperPlayServerPosition var23;
         double var25 = (var23 = new WrapperPlayServerPosition(var2)).getX();
         double var7 = var23.getY();
         double var9 = var23.getZ();
         long var11 = var1.getTimeStamp();
         this.Aa.add(new Sm(var25, var7, var9, var11));
         Location var22;
         double var14 = (var22 = this.Aa.dx().Aa()).getY();
         this.dx(var14);
         double var16 = l.dx(var22.getX(), var25, var22.getZ(), var9);
         double var18 = l.AC(var14, var7);
         if (this.b() < 1000L) {
            if (var16 < this.Eu) {
               this.Eu = var16;
            }

            if (var18 < this.nZ) {
               this.nZ = var18;
            }
         } else {
            this.Eu = var16;
            this.nZ = var18;
         }

         this.pO = var11;
      } else {
         if (var1.M3()) {
            int var3 = this.Aa.dx().Zm();
            if (var1.PU() && !this.Aa.isEmpty()) {
               WrapperPlayClientPositionLook var5;
               double var6 = (var5 = new WrapperPlayClientPositionLook(var2)).getX();
               double var8 = var5.getY();
               double var10 = var5.getZ();
               Iterator var12 = this.Aa.iterator();

               while (var12.hasNext()) {
                  Sm var21;
                  if ((var21 = (Sm)var12.next()).Aa(var6, var8, var10)) {
                     if (var21.getTimeStamp() - this.tr >= (long)var3 + 15L) {
                        this.Pu = 0;
                        this.Aa.remove(var21);
                        break;
                     }

                     this.Aa.remove(var21);
                  }
               }
            }

            this.Pu++;
            int var24 = l.dC((long)var3) + 3;
            this.nZ = this.Pu <= var24;
         }
      }
   }

   public long b() {
      return l.og(this.pO);
   }

   public long VL() {
      return l.og(this.R2);
   }

   static {
      NA();
   }
}
