package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientVehicleMove;
import org.bukkit.entity.Entity;

public class Mt extends PacketCheck {
   private int F7;
   private double VL;
   private static String[] nu;

   public Mt(UC var1) {
      super(var1, CheckType.VEHICLE, nu[0], Category.MOVE, 4.0F);
   }

   static {
      8o();
   }

   private static void _o/* $VF was: 8o*/() {
      nu = new String[]{"C", "Fly ticks: ", " delta Y: ", "Checks for invalid vehicle motions"};
   }

   @Override
   public void dx(DH var1) {
      if (var1.Sh() && !this.Aa.dx().WB()) {
         Entity var2;
         Entity var10000 = var2 = this.Aa.getPlayer().getVehicle();

         try {
            var10000.getClass();
         } catch (Exception var10) {
            return;
         }

         double var4 = new WrapperPlayClientVehicleMove(var1.dx()).getY();
         double var6 = this.VL;
         this.VL = var4;
         if (!(var4 < var6)) {
            double var8 = var4 - var6;
            this.F7 = !var2.isOnGround() && var2.getLocation().clone().subtract(0.0, 1.0, 0.0).getBlock().isEmpty() ? this.F7 + 1 : 0;
            if (this.F7 >= 20 && var8 >= 0.0) {
               this.Aa(nu[1] + this.F7 + nu[2] + var8);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.075);
            }
         }
      }
   }

   @Override
   public String sB() {
      return nu[3];
   }
}
