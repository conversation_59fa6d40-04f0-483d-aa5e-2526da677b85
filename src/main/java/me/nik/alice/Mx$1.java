package me.nik.alice;

import org.bukkit.Material;

class Mx$1 {
   static final int[] Aa = new int[Material.values().length];

   static {
      try {
         Aa[Material.BARRIER.ordinal()] = 1;
      } catch (NoSuchFieldError var1) {
      }

      int[] var10000 = Aa;

      int var10001;
      byte var10002;
      try {
         var10001 = Material.BOOK.ordinal();
         var10002 = 2;
      } catch (NoSuchFieldError var0) {
         return;
      }

      var10000[var10001] = var10002;
   }
}
