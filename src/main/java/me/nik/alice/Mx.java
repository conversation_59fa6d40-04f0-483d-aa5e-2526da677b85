package me.nik.alice;

import java.util.ArrayList;
import me.nik.alice.Mx.1;
import org.apache.commons.lang.WordUtils;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

public class Mx extends EJ {
   private static String[] Jx;

   private static void _Q/* $VF was: 3Q*/() {
      Jx = new String[]{
         "&cPlayer Logs", "", "&8\u00bb &7Server: &f", "", "&8\u00bb &7Check: &f", "", "&8\u00bb &7Timestamp: &f", "", "&8\u00bb &7Information:", "&f", "&6"
      };
   }

   // $VF: Unable to simplify switch on enum
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   @Override
   public void dx(InventoryClickEvent var1) {
      boolean var3 = wRCL;
      Player var2 = (Player)var1.getWhoClicked();
      ItemStack var4;
      if ((var4 = var1.getCurrentItem()).getItemMeta().hasDisplayName()) {
         switch (1.Aa[var4.getType().ordinal()]) {
            case 1:
               var2.closeInventory();
               new pl(this.dx, this.plugin).iv();
               return;
            case 2:
               String var5 = ChatColor.stripColor(var4.getItemMeta().getDisplayName());
               byte var6 = -1;
               switch (var5.hashCode()) {
                  case -1133036644:
                     if (Integer.valueOf(-1133036644).equals(var5.hashCode())) {
                        var6 = 1;
                     }
                     break;
                  case 473267736:
                     if (Integer.valueOf(473267736).equals(var5.hashCode())) {
                        var6 = 0;
                        if (var3) {
                           throw null;
                        }
                     }
               }

               switch (var6) {
                  case 0:
                     if (this.DB != 0) {
                        this.DB--;
                        super.iv();
                        return;
                     }
                     break;
                  case 1:
                     this.DB++;
                     super.iv();
               }
         }
      }
   }

   public Mx(QZ var1, Alice var2) {
      super(var1, var2);
   }

   @Override
   protected int og() {
      return 54;
   }

   @Override
   protected String yM() {
      return Dq.sB(Jx[0]);
   }

   @Override
   protected void Ch() {
      this.F7();
      this.dx.setMaxStackSize(1);
      ArrayList var1 = new ArrayList();

      for (hL var3 : this.plugin.dx().dx().h5()) {
         ArrayList var4;
         (var4 = new ArrayList()).add(Jx[1]);
         var4.add(Jx[2] + var3.gz());
         var4.add(Jx[3]);
         var4.add(Jx[4] + var3.getCheck());
         var4.add(Jx[5]);
         var4.add(Jx[6] + var3.x());
         var4.add(Jx[7]);
         var4.add(Jx[8]);

         String[] var5;
         for (String var8 : var5 = WordUtils.wrap(var3.getInformation(), 35).split(System.lineSeparator())) {
            var4.add(Jx[9].concat(String.valueOf(var8)));
         }

         var1.add(this.dx(Material.PAPER, 1, Dq.sB(Jx[10] + var3.getPlayer()), var4));
      }

      if (!var1.isEmpty()) {
         for (int var10 = 0; var10 < super.jD; var10++) {
            this.index = super.jD * this.DB + var10;
            if (this.index >= var1.size()) {
               break;
            }

            Object var10000 = var1.get(this.index);

            try {
               var10000.hashCode();
            } catch (Exception var9) {
               continue;
            }

            this.dx.addItem(new ItemStack[]{(ItemStack)var1.get(this.index)});
         }
      }
   }

   static {
      3Q();
   }
}
