package me.nik.alice;

import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.event.player.PlayerQuitEvent;

public class N implements Listener {
   private final Alice plugin;

   @EventHandler(
      priority = EventPriority.NORMAL,
      ignoreCancelled = true
   )
   public void dx(PlayerJoinEvent var1) {
      Player var2 = var1.getPlayer();
      wf.Aa(this::og);
      if (me.nik.alice.UN.dx.iv.dC() && var2.hasPermission(ZM.dC.h5())) {
         this.plugin.dx().dx(var2.getUniqueId());
      }
   }

   @EventHandler(
      priority = EventPriority.NORMAL,
      ignoreCancelled = true
   )
   public void Aa(PlayerQuitEvent var1) {
      this.plugin.dx().dC(var1.getPlayer());
   }

   public N(Alice var1) {
      this.plugin = var1;
   }

   private void og(Player var1) {
      this.plugin.dx().sB(var1);
   }
}
