package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class N7 extends PacketCheck {
   private static String[] e7;

   private static void MH() {
      e7 = new String[]{"E", "Checks for invalid acceleration", "Acceleration: ", " fly ticks: "};
   }

   static {
      MH();
   }

   public N7(UC var1) {
      super(var1, CheckType.FLY, e7[0], Category.MOVE, 1.0F);
   }

   @Override
   public String sB() {
      return e7[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().jD() && !this.Aa.dx().dx().DP() && this.Aa.dx().GE() >= 20 && this.Aa.dx().yk() >= 20) {
         ES var13;
         double var3 = (var13 = this.Aa.dx()).Ch();
         double var5 = var13.iv();
         if (var3 != 0.0 && var5 != 0.0) {
            double var7 = Math.abs(var13.Ch());
            double var9 = Math.abs(var13.iv());
            double var11 = var7 - var9;
            int var14 = var13.a();
            if (var11 == 0.0 && var14 > 1) {
               this.Aa(e7[2] + var11 + e7[3] + var14);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.05);
            }
         }
      }
   }
}
