package me.nik.alice;

import alice_libs.apachecommons.FileUtils;
import java.io.File;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Iterator;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

public class NA {
   private final List sX = new ArrayList();
   private final Alice plugin;
   private static String[] Bv;
   private nz dx;

   private void dx(nz var1, AtomicBoolean var2, String var3) {
      this.dx.dx().addDefault(var3, var1.dx().get(var3));
      if (!var2.get()) {
         var2.set(true);
      }
   }

   public NA(Alice var1) {
      this.plugin = var1;
   }

   public List VL() {
      return this.sX;
   }

   private void h5(File var1) {
      this.sX.add(new nz(var1));
   }

   static {
      BK();
   }

   public nz dx(String var1) {
      Iterator var2 = this.sX.iterator();

      while (var2.hasNext()) {
         nz var3;
         if (Integer.valueOf(var1.toUpperCase().hashCode()).equals((var3 = (nz)var2.next()).iv().toUpperCase().hashCode())) {
            return var3;
         }
      }

      return null;
   }

   public nz dx() {
      return this.dx;
   }

   private boolean Aa(String var1) {
      return !this.dx.dx().getKeys(false).contains(var1);
   }

   public void gz() {
      this.sX.clear();
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   public void dC() {
      File var1;
      if (!(var1 = new File(this.plugin.getDataFolder(), Bv[0])).exists()) {
         var1.mkdirs();
      }

      String var2 = this.plugin.getDataFolder() + Bv[1];
      File var3 = new File(var2, Bv[2]);
      File var4 = new File(var2, Bv[3]);
      File var18 = new File(var2, Bv[4]);

      label111: {
         IOException var10000;
         label117: {
            label109: {
               try {
                  if (!var3.createNewFile()) {
                     break label109;
                  }
               } catch (IOException var16) {
                  var10000 = var16;
                  boolean var10001 = false;
                  break label117;
               }

               try {
                  FileUtils.copyInputStreamToFile(this.plugin.getResource(Bv[5]), var3);
               } catch (IOException var15) {
                  var10000 = var15;
                  boolean var31 = false;
                  break label117;
               }
            }

            try {
               var21 = var4.createNewFile();
            } catch (IOException var14) {
               var10000 = var14;
               boolean var32 = false;
               break label117;
            }

            if (var21) {
               Alice var22 = this.plugin;
               String var33 = Bv[6];

               try {
                  var23 = var22.getResource(var33);
                  var35 = var4;
               } catch (IOException var13) {
                  var10000 = var13;
                  boolean var34 = false;
                  break label117;
               }

               try {
                  FileUtils.copyInputStreamToFile(var23, var35);
               } catch (IOException var12) {
                  var10000 = var12;
                  boolean var36 = false;
                  break label117;
               }
            }

            File var24 = var18;

            try {
               if (!var24.createNewFile()) {
                  break label111;
               }
            } catch (IOException var11) {
               var10000 = var11;
               boolean var37 = false;
               break label117;
            }

            Alice var25 = this.plugin;
            String var38 = Bv[7];

            try {
               var26 = var25.getResource(var38);
               var40 = var18;
            } catch (IOException var10) {
               var10000 = var10;
               boolean var39 = false;
               break label117;
            }

            try {
               FileUtils.copyInputStreamToFile(var26, var40);
               break label111;
            } catch (IOException var9) {
               var10000 = var9;
               boolean var41 = false;
            }
         }

         IOException var19 = var10000;
         this.plugin.getLogger().severe(Bv[8]);
         var19.printStackTrace();
      }

      Arrays.stream(var1.listFiles()).filter(File::isFile).forEach(this::h5);
      this.sX.forEach(nz::AC);
      this.Zh(me.nik.alice.UN.dx.og.b());
      nz var17 = this.dx(Bv[9]);
      nz var27 = this.dx;

      try {
         var27.equals(null);
      } catch (Exception var5) {
         this.dx = var17;
      }

      if (this.dx != var17) {
         AtomicBoolean var20 = new AtomicBoolean(false);
         var17.dx().getKeys(false).stream().filter(this::Aa).forEach(this::dx);
         if (var20.get()) {
            NA var28 = this;

            label120: {
               try {
                  var30 = var28.dx.dx();
               } catch (IOException var8) {
                  var29 = var8;
                  boolean var42 = false;
                  break label120;
               }

               try {
                  var30.save(this.dx.getFile());
               } catch (IOException var7) {
                  var29 = var7;
                  boolean var43 = false;
                  break label120;
               }

               try {
                  this.dx.AC();
                  return;
               } catch (IOException var6) {
                  var29 = var6;
                  boolean var44 = false;
               }
            }

            var29.printStackTrace();
         }
      }
   }

   private static void BK() {
      Bv = new String[]{
         "themes",
         "/themes",
         "default.yml",
         "cardinal.yml",
         "luckypouches.yml",
         "themes/default.yml",
         "themes/cardinal.yml",
         "themes/luckypouches.yml",
         "Couldn't load default themes",
         "default"
      };
   }

   public void AC() {
      this.gz();
      this.dC();
   }

   public void Zh(String var1) {
      this.dx = this.dx(var1);
   }
}
