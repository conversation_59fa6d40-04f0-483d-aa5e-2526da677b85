package me.nik.alice;

import java.util.ArrayList;
import java.util.List;
import java.util.UUID;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerQuitEvent;

public class NE implements Listener {
   private final List AC;
   private final ExecutorService dx = Executors.newSingleThreadExecutor();

   public NE(Alice var1) {
      this.AC = new ArrayList();
      var1.getServer().getPluginManager().registerEvents(this, var1);
   }

   public List og() {
      return this.AC;
   }

   public void gz() {
      this.AC.clear();
   }

   public ExecutorService dx() {
      return this.dx;
   }

   public void dx(UUID var1) {
      this.AC.add(var1);
   }

   public boolean Aa(UUID var1) {
      return this.AC.contains(var1);
   }

   public void Aa(UUID var1) {
      this.AC.remove(var1);
   }

   @EventHandler(
      priority = EventPriority.HIGH
   )
   public void og(PlayerQuitEvent var1) {
      UUID var2 = var1.getPlayer().getUniqueId();
      this.Aa(var2);
   }
}
