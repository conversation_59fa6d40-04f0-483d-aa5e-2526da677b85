package me.nik.alice;

import java.util.concurrent.ConcurrentLinkedDeque;

public final class Ne extends ConcurrentLinkedDeque {
   private final int Zm;
   private final boolean VL;

   public Ne(int var1) {
      this.Zm = var1;
      this.VL = false;
   }

   public boolean h5() {
      return super.size() >= this.Zm;
   }

   public boolean add(Object var1) {
      boolean var2 = Hc44;
      if (this.h5()) {
         if (this.VL) {
            super.removeFirst();
            if (var2) {
               throw null;
            }
         } else {
            super.clear();
         }
      }

      return super.add(var1);
   }

   public Ne(int var1, boolean var2) {
      this.Zm = var1;
      this.VL = var2;
   }
}
