package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import org.bukkit.block.BlockFace;
import org.bukkit.event.Event;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.util.Vector;

public class O5 extends EventCheck {
   private static String[] ux;

   static {
      PH();
   }

   @Override
   public void on(Event var1) {
      if (var1 instanceof BlockPlaceEvent) {
         BlockPlaceEvent var3;
         if ((var3 = (BlockPlaceEvent)var1).getBlockPlaced().getType().isSolid() && !var3.getPlayer().getAllowFlight()) {
            BlockFace var2 = var3.getBlock().getFace(var3.getBlockAgainst());
            Vector var5 = new Vector(var2.getModX(), var2.getModY(), var2.getModZ());
            float var4;
            if ((var4 = var3.getPlayer().getLocation().getDirection().angle(var5)) > 1.57F) {
               this.Aa(ux[2].concat(String.valueOf(var4)));
               if (this.Aa() > this.on()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.25);
            }
         }
      }
   }

   @Override
   public String sB() {
      return ux[1];
   }

   private static void PH() {
      ux = new String[]{"B", "Checks for irregular angles when placing blocks", "Invalid angle: "};
   }

   public O5(UC var1) {
      super(var1, CheckType.SCAFFOLD, ux[0], Category.WORLD, 4.0F);
   }
}
