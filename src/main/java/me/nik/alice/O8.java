package me.nik.alice;

import org.bukkit.Material;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.InventoryHolder;
import org.bukkit.inventory.ItemStack;

public class O8 implements Listener {
   @EventHandler
   public void Aa(InventoryClickEvent var1) {
      InventoryHolder var2;
      if ((var2 = var1.getInventory().getHolder()) instanceof lk) {
         ItemStack var10000 = var1.getCurrentItem();

         try {
            var10000.equals(null);
         } catch (Exception var3) {
            return;
         }

         if (var1.getCurrentItem().getType() != Material.AIR) {
            var1.setCancelled(true);
            ((lk)var2).dx(var1);
         }
      }
   }
}
