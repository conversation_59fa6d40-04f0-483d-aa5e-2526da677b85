package me.nik.alice;

import java.lang.reflect.Array;
import java.util.HashMap;
import java.util.Iterator;
import java.util.Set;
import java.util.Map.Entry;
import me.nik.alice.OB.1;

class OB$Aa {
   private final HashMap dx = new HashMap();
   private static String[] HV;

   static {
      x2();
   }

   OB$Aa(1 var1) {
      this();
   }

   void dx(String var1, Object var2) {
      Object var10000 = var2;

      try {
         var10000.getClass();
      } catch (Exception var3) {
         return;
      }

      this.dx.put(var1, var2);
   }

   public String toString() {
      StringBuilder var1 = new StringBuilder();
      Set var2 = this.dx.entrySet();
      var1.append(HV[0]);
      int var3 = 0;
      Iterator var4 = var2.iterator();

      while (var4.hasNext()) {
         Entry var5;
         Object var6 = (var5 = (Entry)var4.next()).getValue();
         var1.append(this.AC((String)var5.getKey())).append(HV[1]);
         if (var6 instanceof String) {
            var1.append(this.AC(String.valueOf(var6)));
         } else if (var6 instanceof Integer) {
            var1.append(Integer.valueOf(String.valueOf(var6)));
         } else if (var6 instanceof Boolean) {
            var1.append(var6);
         } else if (var6 instanceof OB$Aa) {
            var1.append(var6.toString());
         } else if (var6.getClass().isArray()) {
            var1.append(HV[2]);
            int var8 = Array.getLength(var6);

            for (int var7 = 0; var7 < var8; var7++) {
               var1.append(Array.get(var6, var7).toString()).append(var7 != var8 - 1 ? HV[3] : HV[4]);
            }

            var1.append(HV[5]);
         }

         var3++;
         var1.append(var3 == var2.size() ? HV[6] : HV[7]);
      }

      return var1.toString();
   }

   private String AC(String var1) {
      return HV[8] + var1 + HV[9];
   }

   private static void x2() {
      HV = new String[]{"{", ":", "[", ",", "", "]", "}", ",", "\"", "\""};
   }

   private OB$Aa() {
   }
}
