package me.nik.alice;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import org.bukkit.Color;

public class OB$dx {
   private String description;
   private final List Fh = new ArrayList();
   private Color dx;
   private String Vm;
   private String gz;
   private static String[] JB;

   public String WB() {
      return this.gz;
   }

   static {
      eq();
   }

   public OB$dx dx(Collection var1, Collection var2) {
      for (String var3 : var1) {
         for (String var5 : var2) {
            this.Fh.add(new me.nik.alice.OB.dx.dx(var3, var5, null));
         }
      }

      return this;
   }

   public List Zh() {
      return this.Fh;
   }

   public OB$dx dx(String var1) {
      this.Vm = var1;
      return this;
   }

   private static void eq() {
      JB = new String[]{"https://mc-heads.net/avatar/"};
   }

   public String getDescription() {
      return this.description;
   }

   public OB$dx Aa(String var1) {
      this.gz = JB[0].concat(String.valueOf(var1));
      return this;
   }

   public OB$dx dx(Color var1) {
      this.dx = var1;
      return this;
   }

   public OB$dx dx(String var1, String var2) {
      this.Fh.add(new me.nik.alice.OB.dx.dx(var1, var2, null));
      return this;
   }

   public OB$dx og(String var1) {
      this.description = var1;
      return this;
   }

   public String sX() {
      return this.Vm;
   }

   public Color dx() {
      return this.dx;
   }
}
