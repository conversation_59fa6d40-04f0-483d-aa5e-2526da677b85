package me.nik.alice;

import java.io.IOException;
import java.io.OutputStream;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import javax.net.ssl.HttpsURLConnection;
import org.bukkit.Color;

public class OB {
   private static final String WB = "Alice";
   private static String[] U9;
   private final String sX;
   private static final ExecutorService Aa = Executors.newSingleThreadExecutor();
   private static long DB = System.currentTimeMillis();
   private static final String UH = "https://mc-heads.net/avatar/";
   private static final String yM = "https://raw.githubusercontent.com/NikV2/AliceAPI/master/img/ICON.png";
   private final List h5 = new ArrayList();

   private static void cK() {
      U9 = new String[]{
         "avatar_url",
         "https://raw.githubusercontent.com/NikV2/AliceAPI/master/img/ICON.png",
         "username",
         "Alice",
         "title",
         "description",
         "url",
         "thumbnail",
         "color",
         "name",
         "value",
         "fields",
         "embeds",
         "Content-Type",
         "application/json",
         "User-Agent",
         "Java-DiscordWebhook-BY-Gelox_",
         "POST"
      };
   }

   public void dx(me.nik.alice.OB.dx var1) {
      this.h5.add(var1);
   }

   static {
      cK();
   }

   public OB(String var1) {
      this.sX = var1;
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private void jA() {
      me.nik.alice.OB.Aa var1;
      (var1 = new me.nik.alice.OB.Aa(null)).dx(U9[0], U9[1]);
      var1.dx(U9[2], U9[3]);
      if (!this.h5.isEmpty()) {
         ArrayList var2 = new ArrayList();

         for (me.nik.alice.OB.dx var4 : this.h5) {
            me.nik.alice.OB.Aa var5;
            (var5 = new me.nik.alice.OB.Aa(null)).dx(U9[4], var4.sX());
            String var10000 = var4.getDescription();

            label117: {
               try {
                  var10000.equals(null);
               } catch (Exception var21) {
                  break label117;
               }

               var5.dx(U9[5], var4.getDescription());
            }

            var10000 = var4.WB();

            label122: {
               try {
                  var10000.toString();
               } catch (Exception var22) {
                  break label122;
               }

               me.nik.alice.OB.Aa var6;
               (var6 = new me.nik.alice.OB.Aa(null)).dx(U9[6], var4.WB());
               var5.dx(U9[7], var6);
            }

            Color var32 = var4.dx();

            label127: {
               try {
                  var32.equals(null);
               } catch (Exception var23) {
                  break label127;
               }

               Color var27;
               int var7 = (((var27 = var4.dx()).getRed() << 8) + var27.getGreen() << 8) + var27.getBlue();
               var5.dx(U9[8], var7);
            }

            List var28 = var4.Zh();
            ArrayList var30 = new ArrayList();

            for (me.nik.alice.OB.dx.dx var29 : var28) {
               me.nik.alice.OB.Aa var8;
               (var8 = new me.nik.alice.OB.Aa(null)).dx(U9[9], me.nik.alice.OB.dx.dx.dx(var29));
               var8.dx(U9[10], me.nik.alice.OB.dx.dx.Aa(var29));
               var30.add(var8);
            }

            var5.dx(U9[11], var30.toArray());
            var2.add(var5);
         }

         var1.dx(U9[12], var2.toArray());
      }

      URL var33 = new URL;
      URL var10001 = var33;

      label144: {
         try {
            var10001.<init>(this.sX);
         } catch (IOException var20) {
            var34 = var20;
            boolean var40 = false;
            break label144;
         }

         HttpsURLConnection var24;
         try {
            var35 = var24 = (HttpsURLConnection)var33.openConnection();
         } catch (IOException var19) {
            var34 = var19;
            boolean var41 = false;
            break label144;
         }

         try {
            var35.addRequestProperty(U9[13], U9[14]);
         } catch (IOException var18) {
            var34 = var18;
            boolean var42 = false;
            break label144;
         }

         try {
            var24.addRequestProperty(U9[15], U9[16]);
         } catch (IOException var17) {
            var34 = var17;
            boolean var43 = false;
            break label144;
         }

         HttpsURLConnection var36 = var24;

         try {
            var36.setDoOutput(true);
         } catch (IOException var16) {
            var34 = var16;
            boolean var44 = false;
            break label144;
         }

         HttpsURLConnection var37 = var24;
         String[] var45 = U9;
         byte var10002 = 17;

         try {
            var37.setRequestMethod(var45[var10002]);
         } catch (IOException var15) {
            var34 = var15;
            boolean var46 = false;
            break label144;
         }

         HttpsURLConnection var38 = var24;

         try {
            var39 = var38.getOutputStream();
         } catch (IOException var14) {
            var34 = var14;
            boolean var47 = false;
            break label144;
         }

         OutputStream var26;
         try {
            var26 = var39;
            var39.write(var1.toString().getBytes());
         } catch (IOException var13) {
            var34 = var13;
            boolean var48 = false;
            break label144;
         }

         try {
            var26.flush();
         } catch (IOException var12) {
            var34 = var12;
            boolean var49 = false;
            break label144;
         }

         try {
            var26.close();
         } catch (IOException var11) {
            var34 = var11;
            boolean var50 = false;
            break label144;
         }

         try {
            var24.getInputStream().close();
         } catch (IOException var10) {
            var34 = var10;
            boolean var51 = false;
            break label144;
         }

         try {
            var24.disconnect();
            return;
         } catch (IOException var9) {
            var34 = var9;
            boolean var52 = false;
         }
      }

      var34.printStackTrace();
   }

   public void Zm() {
      String var10000 = this.sX;

      try {
         var10000.equals(null);
      } catch (Exception var1) {
         return;
      }

      if (!this.sX.isEmpty() && System.currentTimeMillis() - DB >= 1500L) {
         DB = System.currentTimeMillis();
         Aa.execute(this::jA);
      }
   }
}
