package me.nik.alice;

import n3kas.ae.api.EnchantActivateEvent;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;

public class Od implements Listener {
   private static void dx(Player var0) {
      Alice.getAPI().setTempBypass(var0, 5L);
   }

   @EventHandler
   public void dx(EnchantActivateEvent var1) {
      if (var1.getFirstEntity() instanceof Player) {
         Alice.getAPI().setTempBypass((Player)var1.getFirstEntity(), 5L);
      }

      if (var1.getSecondEntity() instanceof Player) {
         Alice.getAPI().setTempBypass((Player)var1.getSecondEntity(), 5L);
      }

      var1.getAdditionalPlayers().forEach(Od::dx);
   }
}
