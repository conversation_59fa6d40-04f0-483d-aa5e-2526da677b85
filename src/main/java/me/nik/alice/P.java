package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientUseEntity;

public class P extends PacketCheck {
   private static String[] HL;

   @Override
   public void dx(DH var1) {
      if (var1.Tq()) {
         int var3 = new WrapperPlayClientUseEntity(var1.dx()).getTargetID();
         int var2 = this.Aa.getPlayer().getEntityId();
         if (var3 == var2) {
            this.og(HL[2]);
         }
      }
   }

   @Override
   public String sB() {
      return HL[1];
   }

   private static void vU() {
      HL = new String[]{"F", "Checks for self damage", "Self damage"};
   }

   public P(UC var1) {
      super(var1, CheckType.PACKET, HL[0], Category.WORLD);
   }

   static {
      vU();
   }
}
