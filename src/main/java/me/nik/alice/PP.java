package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class PP extends PacketCheck {
   private long og;
   private static String[] ra;
   private long WB;
   private final V0 sB = new V0(10);

   static {
      P9();
   }

   @Override
   public String sB() {
      return ra[1];
   }

   public PP(UC var1) {
      super(var1, CheckType.INTERACT, ra[0], Category.WORLD);
   }

   private static void P9() {
      ra = new String[]{"FastPlace", "Checks for irregular block placing speed", "Dev: ", " average: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.CS() || var1.d()) {
         long var2 = var1.getTimeStamp();
         long var4 = this.og;
         this.og = var2;
         long var6 = var2 - var4;
         long var8 = this.WB;
         this.WB = var6;
         if (var6 > 5L && var6 != var8) {
            this.sB.add(var6);
         }

         if (!this.sB.h5()) {
            return;
         }

         double var10 = l.sB(this.sB);
         double var12 = l.Zh(this.sB);
         if (var10 > 0.0 && var10 <= 2.0 && var12 < 65.0) {
            this.og(ra[2] + var10 + ra[3] + var12);
         }
      }
   }
}
