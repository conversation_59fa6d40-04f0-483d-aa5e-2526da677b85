package me.nik.alice;

import com.comphenix.protocol.wrappers.EnumWrappers.ClientCommand;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientClientCommand;

public class PU extends PacketCheck {
   private static String[] 92;
   private long og;

   @Override
   public void dx(DH var1) {
      boolean var12 = twm2;
      if (var1.E()) {
         WrapperPlayClientClientCommand var2 = new WrapperPlayClientClientCommand(var1.dx());
         double var3 = this.Aa.getPlayer().getHealth();
         boolean var10000;
         if (var2.getAction() == ClientCommand.PERFORM_RESPAWN && var3 != 0.0) {
            var10000 = true;
            if (var12) {
               throw null;
            }
         } else {
            var10000 = false;
         }

         boolean var13 = var10000;
         long var6 = var1.getTimeStamp();
         long var8 = this.og;
         this.og = var6;
         long var14 = var6 - var8;
         long var10001 = var6 - var8;
         if (var14 > 15000L) {
            this.Vm();
         }

         if (var13) {
            this.Aa(92[2].concat(String.valueOf(var3)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.25);
         }
      }
   }

   @Override
   public String sB() {
      return 92[1];
   }

   static {
      Av();
   }

   public PU(UC var1) {
      super(var1, CheckType.PACKET, 92[0], Category.WORLD, 3.0F);
   }

   private static void Av() {
      92 = new String[]{"U", "Checks for invalid respawn packets used by disablers", "Invalid respawn packet, health: "};
   }
}
