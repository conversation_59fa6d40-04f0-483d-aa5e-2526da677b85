package me.nik.alice;

import me.nik.alice.checks.Check;

public abstract class PacketCheck extends Check {
   private static String[] 9m;

   public PacketCheck(UC var1, VL var2, String var3, b var4, float var5) {
      super(var1, var2, var3, var4, var5);
   }

   static {
      t4();
   }

   public PacketCheck(UC var1, VL var2, b var3) {
      super(var1, var2, 9m[1], var3, 0.0F);
   }

   public PacketCheck(UC var1, VL var2, String var3, b var4) {
      super(var1, var2, var3, var4, 0.0F);
   }

   public PacketCheck(UC var1, VL var2, b var3, float var4) {
      super(var1, var2, 9m[0], var3, var4);
   }

   public abstract void dx(DH var1);

   private static void t4() {
      9m = new String[]{"", ""};
   }
}
