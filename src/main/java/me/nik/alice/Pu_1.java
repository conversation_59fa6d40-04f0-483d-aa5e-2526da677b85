package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Pu extends PacketCheck {
   private static String[] 4C;

   static {
      LS();
   }

   public Pu(UC var1) {
      super(var1, CheckType.JESUS, 4C[0], Category.MOVE, 5.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().VL()) {
         ES var7;
         if ((var7 = this.Aa.dx()).M3() >= 5 && !var7.Qt() && var7.QJ() >= 5) {
            double var3 = var7.Ch();
            double var5 = this.Aa.dx().Aa(RH.sX) ? 0.45 : 0.25;
            if (var3 > var5) {
               this.Aa(4C[2].concat(String.valueOf(var3)));
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.25);
            }
         }
      }
   }

   private static void LS() {
      4C = new String[]{"C", "Checks for irregular swimming accelerations", "Delta Y: "};
   }

   @Override
   public String sB() {
      return 4C[1];
   }
}
