package me.nik.alice;

public class Q extends PacketCheck {
   private final V0 dC = new V0(20);
   private long og;
   private static String[] QQ;

   @Override
   public void dx(DH var1) {
      if (var1.i2() && !this.Aa.dx().UH()) {
         long var2;
         long var4 = (var2 = var1.getTimeStamp()) - this.og;
         this.og = var2;
         if (var4 > 0L) {
            if (var4 > 10000L) {
               this.dC.clear();
            }

            this.dC.add(var4);
            if (this.dC.h5()) {
               double var6 = l.Aa(this.dC);
               long var8;
               if ((var8 = this.Aa.dx().sB()) > 7L && var6 <= 0.1 && var6 >= 0.0) {
                  this.Aa(QQ[2] + var8 + QQ[3] + var6);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.5);
               }
            }
         }
      }
   }

   private static void _B/* $VF was: 3B*/() {
      QQ = new String[]{"H", "Checks for irregular click patterns", "CPS: ", " kurtosis: "};
   }

   @Override
   public String sB() {
      return QQ[1];
   }

   public Q(UC var1) {
      super(var1, VL.Aa, QQ[0], b.dx, 1.0F);
   }

   static {
      3B();
   }
}
