package me.nik.alice;

class Q7$1 {
   static final int[] dx = new int[ZU.values().length];

   static {
      try {
         dx[ZU.dx.ordinal()] = 1;
      } catch (NoSuchFieldError var3) {
      }

      try {
         dx[ZU.og.ordinal()] = 2;
      } catch (NoSuchFieldError var2) {
      }

      int[] var10000 = dx;
      ZU var10001 = ZU.Aa;

      try {
         var10000[var10001.ordinal()] = 3;
      } catch (NoSuchFieldError var1) {
      }

      var10000 = dx;
      var10001 = ZU.AC;

      try {
         var10000[var10001.ordinal()] = 4;
      } catch (NoSuchFieldError var0) {
      }
   }
}
