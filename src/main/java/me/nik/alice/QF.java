package me.nik.alice;

import com.willfp.ecoenchants.enchantments.EcoEnchant;
import com.willfp.ecoenchants.enchantments.EcoEnchants;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class QF extends f2 {
   private static String[] fJ;

   static {
      cF();
   }

   private static void cF() {
      fJ = new String[]{"EcoEnchants"};
   }

   @Override
   public boolean Aa(Player var1) {
      return this.AC(var1);
   }

   @Override
   public boolean og(Player var1) {
      return false;
   }

   public QF() {
      super(fJ[0]);
   }

   @Override
   public boolean dx(Player var1) {
      return this.AC(var1);
   }

   private boolean AC(Player var1) {
      ItemStack var5;
      ItemStack var10000 = var5 = JA.dx(var1);

      try {
         var10000.hashCode();
      } catch (Exception var4) {
         return false;
      }

      if (var5.getType() != Material.AIR) {
         for (EcoEnchant var3 : EcoEnchants.values()) {
            if (EcoEnchants.hasAnyOfType(var5, var3.getType())) {
               return true;
            }
         }

         return false;
      } else {
         return false;
      }
   }
}
