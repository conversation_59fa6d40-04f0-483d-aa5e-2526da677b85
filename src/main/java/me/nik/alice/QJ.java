package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import org.bukkit.event.Event;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.event.player.PlayerFishEvent.State;

public class QJ extends EventCheck {
   private long h5;
   private static String[] A7;
   private long Zh;

   static {
      8V();
   }

   @Override
   public String sB() {
      return A7[1];
   }

   @Override
   public void on(Event var1) {
      if (!w.v() && var1 instanceof PlayerFishEvent) {
         State var10;
         if ((var10 = ((PlayerFishEvent)var1).getState()) == State.BITE) {
            this.Zh = this.on();
         } else {
            if (var10 == State.CAUGHT_FISH) {
               long var4 = l.og(this.Zh);
               long var6 = this.h5;
               this.h5 = var4;
               long var8 = Math.abs(var4 - var6);
               if (var4 < 25L || var8 < 3L) {
                  this.Aa(A7[2] + var4 + A7[3] + var8);
                  if (this.Aa() > this.on()) {
                     this.sX();
                  }
               }
            }
         }
      }
   }

   public QJ(UC var1) {
      super(var1, CheckType.AUTOFISH, A7[0], Category.WORLD, 2.0F);
   }

   private static void _V/* $VF was: 8V*/() {
      A7 = new String[]{"A", "Checks for impossible fishing reactions", "Delay: ", " delta: "};
   }
}
