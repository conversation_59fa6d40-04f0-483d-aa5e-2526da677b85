package me.nik.alice;

import java.util.Iterator;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffect;
import org.bukkit.potion.PotionEffectType;

public class QU {
   private long Ch;
   private long F7;
   private long zP;
   private int yV;
   private int cO;
   private int a;
   private int jA;
   private long hW;
   private int bI;
   private long iv;

   public int Aa(long var1) {
      return l.og(this.Ch) < var1 ? this.yV : 0;
   }

   public int dx(long var1) {
      return l.og(this.iv) < var1 ? this.a : 0;
   }

   public int sB(long var1) {
      return l.og(this.zP) < var1 ? this.jA : 0;
   }

   public void AC(Player var1) {
      long var2 = System.currentTimeMillis();
      Iterator var6 = var1.getActivePotionEffects().iterator();

      while (var6.hasNext()) {
         PotionEffect var4;
         PotionEffectType var5 = (var4 = (PotionEffect)var6.next()).getType();
         int var7 = var4.getAmplifier() + 1;
         if (var5.equals(PotionEffectType.SPEED)) {
            this.zP = var2;
            this.jA = var7;
         }

         if (var5.equals(PotionEffectType.JUMP)) {
            this.hW = var2;
            this.bI = var7;
         }

         if (!w.v() && var5.equals(PotionEffectType.SLOW_FALLING)) {
            this.Ch = var2;
            this.yV = var7;
         }

         if (w.Sx() && var5.equals(PotionEffectType.LEVITATION)) {
            this.iv = var2;
            this.a = var7;
         }

         if (!w.v() && var5.equals(PotionEffectType.DOLPHINS_GRACE)) {
            this.F7 = var2;
            this.cO = var7;
         }
      }
   }

   public int AC(long var1) {
      return l.og(this.F7) < var1 ? this.cO : 0;
   }

   public int og(long var1) {
      return l.og(this.hW) < var1 ? this.bI : 0;
   }
}
