package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import org.bukkit.block.Block;
import org.bukkit.event.Event;
import org.bukkit.event.block.BlockPlaceEvent;

public class Qf extends EventCheck {
   private double h5;
   private int gz;
   private double Fh;
   private static String[] 0g;

   private static void D5() {
      0g = new String[]{"A", "Checks for safewalk", "Delta: ", " Accel: ", " Last: ", " L last: ", " XZ: "};
   }

   public Qf(UC var1) {
      super(var1, CheckType.SCAFFOLD, 0g[0], Category.WORLD, 3.0F);
   }

   @Override
   public String sB() {
      return 0g[1];
   }

   @Override
   public void on(Event var1) {
      if (var1 instanceof BlockPlaceEvent) {
         Block var2;
         BlockPlaceEvent var18;
         if ((var2 = (var18 = (BlockPlaceEvent)var1).getBlockPlaced()).getType().isSolid()) {
            int var21 = var2.getY();
            int var3 = this.gz;
            this.gz = var21;
            if (var21 == var3) {
               if (!var18.getPlayer().getAllowFlight()) {
                  ES var19;
                  double var8 = (var19 = this.Aa.dx()).DB() - var19.jD();
                  double var10 = this.h5;
                  this.h5 = var8;
                  double var12 = this.Fh;
                  this.Fh = var10;
                  double var14 = Math.abs(var8 - var10);
                  double var16 = var19.F7();
                  boolean var20 = var8 <= 1.0E-4 && var10 >= 0.1 && var12 <= 1.0E-4;
                  boolean var22 = var14 < 1.0E-4 && var8 > var10 && var16 >= 0.095 && var16 < 0.2;
                  if (this.Aa.dx().VL() < 2 && (var20 || var22)) {
                     this.Aa(0g[2] + var14 + 0g[3] + var8 + 0g[4] + var10 + 0g[5] + var12 + 0g[6] + var16);
                     if (this.Aa() > this.on()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.25);
                  }
               }
            }
         }
      }
   }

   static {
      D5();
   }
}
