package me.nik.alice;

import com.comphenix.protocol.events.PacketContainer;
import java.util.Map;
import java.util.WeakHashMap;
import me.nik.alice.wrappers.WrapperPlayClientTransaction;
import me.nik.alice.wrappers.WrapperPlayServerEntityVelocity;
import me.nik.alice.wrappers.WrapperPlayServerExplosion;
import me.nik.alice.wrappers.WrapperPlayServerTransaction;
import me.nik.fastmath.FastMath;

public class Qk {
   private double Q;
   private double u;
   private final UC Aa;
   private int xB;
   private double o4;
   private double DP;
   private final Map AC = new WeakHashMap();
   private double pO;
   private int Zt;
   private double tr;
   private int EG;
   private int fq;
   private double QJ;
   private int Tf;
   private double p6;

   public Qk(UC var1) {
      this.Aa = var1;
   }

   public int tU() {
      return this.fq;
   }

   public double getVelocityX() {
      return this.u;
   }

   public int Wx() {
      return l.Aa(this.Tf);
   }

   public boolean tr() {
      Sk var1 = this.Aa.dx();
      int var2 = this.xB + l.dC((long)var1.jA()) + (var1.Fh() < 25L ? 10 : 5);
      return Math.abs(this.EG - this.Zt) < var2;
   }

   private void dx(double var1, double var3, double var5, long var7) {
      short var9 = s8.dx();
      this.AC.put(var9, new me.nik.alice.Qk.dx(var1, var3, var5, var7));
      WrapperPlayServerTransaction var10;
      (var10 = new WrapperPlayServerTransaction()).setWindowId(0);
      var10.setActionNumber(var9);
      var10.setAccepted(false);
      var10.sendPacket(this.Aa.getPlayer());
   }

   public double cO() {
      return this.o4;
   }

   public double getVelocityZ() {
      return this.QJ;
   }

   public double a() {
      return this.DP;
   }

   public double getVelocityY() {
      return this.Q;
   }

   public void AC(DH var1) {
      PacketContainer var2 = var1.dx();
      long var3 = var1.getTimeStamp();
      if (var1.SP()) {
         WrapperPlayServerEntityVelocity var28 = new WrapperPlayServerEntityVelocity(var2);
         this.dx(var28.getVelocityX(), var28.getVelocityY(), var28.getVelocityZ(), var3);
      } else if (var1.R2()) {
         WrapperPlayServerExplosion var27 = new WrapperPlayServerExplosion(var2);
         this.dx((double)var27.getPlayerVelocityX(), (double)var27.getPlayerVelocityY(), (double)var27.getPlayerVelocityZ(), var3);
         this.Tf = Ku.iK();
      } else if (var1.tU()) {
         WrapperPlayClientTransaction var24;
         if ((var24 = new WrapperPlayClientTransaction(var2)).getWindowId() == 0) {
            short var25 = var24.getActionNumber();
            if (this.AC.containsKey(var25)) {
               me.nik.alice.Qk.dx var26;
               double var8 = (var26 = (me.nik.alice.Qk.dx)this.AC.get(var25)).getVelocityX();
               double var10 = this.u;
               this.u = var8;
               this.tr = var10;
               double var12 = var26.getVelocityY();
               double var14 = this.Q;
               this.Q = var12;
               this.pO = var14;
               double var16 = var26.getVelocityZ();
               double var18 = this.QJ;
               this.QJ = var16;
               this.DP = var18;
               double var20 = FastMath.hypot(var8, var16);
               double var22 = this.o4;
               this.o4 = var20;
               this.p6 = var22;
               this.Zt = this.EG;
               this.fq = 0;
               this.xB = JA.Aa(var20);
            }
         }
      } else {
         if (var1.pQ()) {
            this.EG++;
            this.fq = this.tr() ? this.fq + 1 : 0;
         }
      }
   }

   public double yV() {
      return this.pO;
   }

   public double bI() {
      return this.tr;
   }

   public double tk() {
      return this.p6;
   }
}
