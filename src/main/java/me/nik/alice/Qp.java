package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientVehicleMove;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Minecart;

public class Qp extends PacketCheck {
   private static String[] 3E;
   private double yM;
   private double UH;
   private double VL;

   @Override
   public void dx(DH var1) {
      if (var1.Sh() && !this.Aa.dx().WB()) {
         Entity var2;
         Entity var10000 = var2 = this.Aa.getPlayer().getVehicle();

         try {
            var10000.hashCode();
         } catch (Exception var24) {
            return;
         }

         if (!(var2 instanceof Minecart)) {
            WrapperPlayClientVehicleMove var25;
            double var4 = (var25 = new WrapperPlayClientVehicleMove(var1.dx())).getX();
            double var6 = var25.getY();
            double var8 = var25.getZ();
            double var10 = this.UH;
            double var12 = this.VL;
            double var14 = this.yM;
            this.UH = var4;
            this.VL = var6;
            this.yM = var8;
            double var16 = var4 - var10;
            double var18 = var6 - var12;
            double var20 = var8 - var14;
            double var22;
            if ((var22 = var16 * var16 + var20 * var20) > 5.0 || var18 > 7.5) {
               this.Aa(3E[2].concat(String.valueOf(var22)));
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.075);
            }
         }
      }
   }

   private static void Y0() {
      3E = new String[]{"B", "Checks for impossible vehicle speed", "Dist XZ: "};
   }

   @Override
   public String sB() {
      return 3E[1];
   }

   public Qp(UC var1) {
      super(var1, CheckType.VEHICLE, 3E[0], Category.MOVE, 3.0F);
   }

   static {
      Y0();
   }
}
