package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class R extends PacketCheck {
   private long b;
   private static String[] 6V;

   @Override
   public String sB() {
      return 6V[1];
   }

   @Override
   public void dx(DH var1) {
      if (!var1.Tq()) {
         if (var1.tV()) {
            this.b = var1.getTimeStamp();
         }
      } else {
         long var2 = l.og(this.b);
         long var4;
         boolean var6 = (var4 = this.Aa.dx().Fh()) < 40L || var4 > 100L;
         if (var2 <= 0L && !var6) {
            this.Aa(6V[2].concat(String.valueOf(var2)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.25);
         }
      }
   }

   static {
      yU();
   }

   public R(UC var1) {
      super(var1, CheckType.PACKET, 6V[0], Category.WORLD, 2.0F);
   }

   private static void yU() {
      6V = new String[]{"G", "Checks for wtap", "Delta: "};
   }
}
