package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

@h5
public class R2 extends PacketCheck {
   private static String[] Cp;

   @Override
   public void dx(DH var1) {
      if (var1.M3()) {
         MN var12;
         double var3 = (var12 = this.Aa.dx()).Zm();
         double var5 = var12.jA();
         ES var2;
         double var8 = (var2 = this.Aa.dx()).F7();
         double var10 = var2.Ch();
         boolean var13 = var3 > 10.0 && var8 != var3 && var8 > 0.0;
         boolean var7 = var5 > 10.0 && var10 != var5 && var10 >= 0.0 && var8 > 0.0;
         if (var12.GE() < 5 && var12.b() < 1000L && var12.VL() > 5000L && (var13 || var7)) {
            this.Aa(Cp[2] + var3 + Cp[3] + var8 + Cp[4] + var5 + Cp[5] + var10);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.125);
         }
      }
   }

   public R2(UC var1) {
      super(var1, CheckType.PACKET, Cp[0], Category.WORLD, 10.0F);
   }

   @Override
   public String sB() {
      return Cp[1];
   }

   private static void cI() {
      Cp = new String[]{"CC", "Checks for teleport exploits", "Teleport XZ: ", " XZ: ", " teleport Y: ", " Y: "};
   }

   static {
      cI();
   }
}
