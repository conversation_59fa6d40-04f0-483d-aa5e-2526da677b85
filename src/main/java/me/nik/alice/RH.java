package me.nik.alice;

public enum RH {
   Aa(5),
   og(47),
   AC(107),
   sB(108),
   dC(109),
   Zh(110),
   h5(210),
   Fh(315),
   b(316),
   VL(335),
   UH(338),
   yM(340),
   sX(393),
   WB(401),
   Vm(404),
   gz(477),
   tm(480),
   x(485),
   zP(490),
   hW(498),
   Ch(573),
   iv(575),
   F7(578),
   Zp(735),
   DB(736),
   jD(751),
   Qt(753),
   rc(754),
   Zm(999999999);

   private final int D1;
   private static final RH[] dx = new RH[]{Aa, og, AC, sB, dC, Zh, h5, Fh, b, VL, UH, yM, sX, WB, Vm, gz, tm, x, zP, hW, Ch, iv, F7, Zp, DB, jD, Qt, rc, Zm};
   private static String[] xf;

   private RH(int var3) {
      this.D1 = var3;
   }

   public static RH dx(int var0) {
      boolean var5 = mY9I;
      RH[] var1;
      int var2 = (var1 = values()).length;
      int var3 = 0;

      while (var3 < var2) {
         RH var4 = var1[var3];
         if (var0 == var4.D1) {
            return var4;
         }

         var3++;
         if (var5) {
            throw null;
         }
      }

      return Zm;
   }

   public int SP() {
      return this.D1;
   }

   public boolean dx(RH var1) {
      return this.D1 > var1.D1;
   }

   public boolean Aa(RH var1) {
      return this.D1 >= var1.D1;
   }

   public boolean og(RH var1) {
      return this.D1 < var1.D1;
   }

   public boolean AC(RH var1) {
      return this.D1 <= var1.D1;
   }

   public boolean sB(RH var1) {
      return this.D1 == var1.D1;
   }

   public String toString() {
      return this.name().substring(2).replace(xf[0], xf[1]);
   }

   static {
      pZ();
   }

   private static void pZ() {
      xf = new String[]{
         "_",
         ".",
         "v_1_7_10",
         "v_1_8",
         "v_1_9",
         "v_1_9_1",
         "v_1_9_2",
         "v_1_9_3",
         "v_1_10",
         "v_1_11",
         "v_1_11_1",
         "v_1_12",
         "v_1_12_1",
         "v_1_12_2",
         "v_1_13",
         "v_1_13_1",
         "v_1_13_2",
         "v_1_14",
         "v_1_14_1",
         "v_1_14_2",
         "v_1_14_3",
         "v_1_14_4",
         "v_1_15",
         "v_1_15_1",
         "v_1_15_2",
         "v_1_16",
         "v_1_16_1",
         "v_1_16_2",
         "v_1_16_3",
         "v_1_16_4",
         "NEWEST"
      };
   }
}
