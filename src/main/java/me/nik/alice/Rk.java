package me.nik.alice;

import java.util.HashMap;
import java.util.Map;

public final class Rk {
   private static final Map VL;
   private static final Map UH;
   private static final Map b;

   public static Map Aa() {
      return b;
   }

   public static Map og() {
      return VL;
   }

   static {
      (b = new HashMap()).put(0L, 0.0F);
      b.put(1L, 0.0070422534F);
      b.put(2L, 0.014084507F);
      b.put(3L, 0.0F);
      b.put(4L, 0.02112676F);
      b.put(5L, 0.028169014F);
      b.put(6L, 0.028169017F);
      b.put(7L, 0.03521127F);
      b.put(8L, 0.04225352F);
      b.put(9L, 0.049295776F);
      b.put(10L, 0.049295772F);
      b.put(11L, 0.056338027F);
      b.put(12L, 0.06338028F);
      b.put(13L, 0.0F);
      b.put(14L, 0.07042254F);
      b.put(15L, 0.07746479F);
      b.put(16L, 0.08450704F);
      b.put(17L, 0.0F);
      b.put(18L, 0.09154929F);
      b.put(19L, 0.09859155F);
      b.put(20L, 0.0F);
      b.put(21L, 0.1056338F);
      b.put(22L, 0.112676054F);
      b.put(23L, 0.11971831F);
      b.put(24L, 0.0F);
      b.put(25L, 0.12676056F);
      b.put(26L, 0.13380282F);
      b.put(27L, 0.0F);
      b.put(28L, 0.14084508F);
      b.put(29L, 0.14788732F);
      b.put(30L, 0.15492958F);
      b.put(31L, 0.0F);
      b.put(32L, 0.16197184F);
      b.put(33L, 0.16901408F);
      b.put(34L, 0.0F);
      b.put(35L, 0.17605634F);
      b.put(36L, 0.18309858F);
      b.put(37L, 0.0F);
      b.put(38L, 0.19014084F);
      b.put(39L, 0.1971831F);
      b.put(40L, 0.20422535F);
      b.put(41L, 0.0F);
      b.put(42L, 0.2112676F);
      b.put(43L, 0.21830986F);
      b.put(44L, 0.0F);
      b.put(45L, 0.22535211F);
      b.put(46L, 0.23239437F);
      b.put(47L, 0.23943663F);
      b.put(48L, 0.0F);
      b.put(49L, 0.24647887F);
      b.put(50L, 0.2535211F);
      b.put(51L, 0.0F);
      b.put(52L, 0.26056337F);
      b.put(53L, 0.26760563F);
      b.put(54L, 0.2746479F);
      b.put(55L, 0.0F);
      b.put(56L, 0.28169015F);
      b.put(57L, 0.28873238F);
      b.put(58L, 0.0F);
      b.put(59L, 0.29577464F);
      b.put(60L, 0.3028169F);
      b.put(61L, 0.30985916F);
      b.put(62L, 0.0F);
      b.put(63L, 0.31690142F);
      b.put(64L, 0.32394367F);
      b.put(65L, 0.0F);
      b.put(66L, 0.3309859F);
      b.put(67L, 0.33802816F);
      b.put(68L, 0.34507042F);
      b.put(69L, 0.0F);
      b.put(70L, 0.35211268F);
      b.put(71L, 0.35915494F);
      b.put(72L, 0.0F);
      b.put(73L, 0.36619717F);
      b.put(74L, 0.37323943F);
      b.put(75L, 0.0F);
      b.put(76L, 0.3802817F);
      b.put(77L, 0.38732395F);
      b.put(78L, 0.3943662F);
      b.put(79L, 0.0F);
      b.put(80L, 0.40140846F);
      b.put(81L, 0.4084507F);
      b.put(82L, 0.0F);
      b.put(83L, 0.41549295F);
      b.put(84L, 0.4225352F);
      b.put(85L, 0.42957747F);
      b.put(86L, 0.0F);
      b.put(87L, 0.43661973F);
      b.put(88L, 0.44366196F);
      b.put(89L, 0.0F);
      b.put(90L, 0.45070422F);
      b.put(91L, 0.45774648F);
      b.put(92L, 0.46478873F);
      b.put(93L, 0.0F);
      b.put(94L, 0.471831F);
      b.put(95L, 0.47887325F);
      b.put(96L, 0.0F);
      b.put(97L, 0.48591548F);
      b.put(98L, 0.49295774F);
      b.put(99L, 0.5F);
      b.put(100L, 0.5F);
      b.put(101L, 0.5070422F);
      b.put(102L, 0.5140845F);
      b.put(103L, 0.0F);
      b.put(104L, 0.52112675F);
      b.put(105L, 0.52816904F);
      b.put(106L, 0.0F);
      b.put(107L, 0.53521127F);
      b.put(108L, 0.542253F);
      b.put(109L, 0.5492958F);
      b.put(110L, 0.0F);
      b.put(111L, 0.556338F);
      b.put(112L, 0.5633803F);
      b.put(113L, 0.0F);
      b.put(114L, 0.57042253F);
      b.put(115L, 0.57746476F);
      b.put(116L, 0.58450705F);
      b.put(117L, 0.0F);
      b.put(118L, 0.5915493F);
      b.put(119L, 0.59859157F);
      b.put(120L, 0.0F);
      b.put(121L, 0.6056338F);
      b.put(122L, 0.6126761F);
      b.put(123L, 0.6197183F);
      b.put(124L, 0.0F);
      b.put(125L, 0.62676054F);
      b.put(126L, 0.63380283F);
      b.put(127L, 0.0F);
      b.put(128L, 0.64084506F);
      b.put(129L, 0.64788735F);
      b.put(130L, 0.6549296F);
      b.put(131L, 0.0F);
      b.put(132L, 0.6619718F);
      b.put(133L, 0.6690141F);
      b.put(134L, 0.0F);
      b.put(135L, 0.6760563F);
      b.put(136L, 0.6830986F);
      b.put(137L, 0.0F);
      b.put(138L, 0.69014084F);
      b.put(139L, 0.6971831F);
      b.put(140L, 0.70422536F);
      b.put(141L, 0.0F);
      b.put(142L, 0.7112676F);
      b.put(143L, 0.7183099F);
      b.put(144L, 0.7253521F);
      b.put(145L, 0.7253521F);
      b.put(146L, 0.73239434F);
      b.put(147L, 0.7394366F);
      b.put(148L, 0.0F);
      b.put(149L, 0.74647886F);
      b.put(150L, 0.75352114F);
      b.put(151L, 0.0F);
      b.put(152L, 0.7605634F);
      b.put(153L, 0.76760566F);
      b.put(154L, 0.7746479F);
      b.put(155L, 0.0F);
      b.put(156L, 0.7816901F);
      b.put(157L, 0.7887324F);
      b.put(158L, 0.0F);
      b.put(159L, 0.79577464F);
      b.put(160L, 0.8028169F);
      b.put(161L, 0.80985916F);
      b.put(162L, 0.0F);
      b.put(163L, 0.8169014F);
      b.put(164L, 0.8239437F);
      b.put(165L, 0.0F);
      b.put(166L, 0.8309859F);
      b.put(167L, 0.8380282F);
      b.put(168L, 0.0F);
      b.put(169L, 0.8450704F);
      b.put(170L, 0.85211265F);
      b.put(171L, 0.85915494F);
      b.put(172L, 0.0F);
      b.put(173L, 0.86619717F);
      b.put(174L, 0.87323946F);
      b.put(175L, 0.0F);
      b.put(176L, 0.8802817F);
      b.put(177L, 0.8873239F);
      b.put(178L, 0.8943662F);
      b.put(179L, 0.0F);
      b.put(180L, 0.90140843F);
      b.put(181L, 0.9084507F);
      b.put(182L, 0.0F);
      b.put(183L, 0.91549295F);
      b.put(184L, 0.92253524F);
      b.put(185L, 0.92957747F);
      b.put(186L, 0.0F);
      b.put(187L, 0.9366197F);
      b.put(188L, 0.943662F);
      b.put(189L, 0.0F);
      b.put(190L, 0.9507042F);
      b.put(191L, 0.9577465F);
      b.put(192L, 0.96478873F);
      b.put(193L, 0.0F);
      b.put(194L, 0.97183096F);
      b.put(195L, 0.97887325F);
      b.put(196L, 0.0F);
      b.put(197L, 0.9859155F);
      b.put(198L, 0.9929578F);
      b.put(199L, 1.0F);
      b.put(200L, 1.0F);
      (VL = new HashMap()).put(1, 0.42F);
      VL.put(2, 0.3332F);
      VL.put(3, 0.248136F);
      VL.put(4, 0.16477329F);
      VL.put(5, 0.08307782F);
      VL.put(7, -0.0784F);
      VL.put(8, -0.155232F);
      VL.put(9, -0.23052737F);
      VL.put(10, -0.30431682F);
      (UH = new HashMap()).put(200L, 0.61);
      UH.put(199L, 0.6);
      UH.put(198L, 0.6);
      UH.put(197L, 0.59);
      UH.put(196L, 0.58);
      UH.put(195L, 0.58);
      UH.put(194L, 0.57);
      UH.put(193L, 0.56);
      UH.put(192L, 0.56);
      UH.put(191L, 0.55);
      UH.put(190L, 0.54);
      UH.put(189L, 0.53);
      UH.put(188L, 0.53);
      UH.put(187L, 0.53);
      UH.put(186L, 0.52);
      UH.put(185L, 0.52);
      UH.put(184L, 0.51);
      UH.put(183L, 0.5);
      UH.put(182L, 0.49);
      UH.put(181L, 0.49);
      UH.put(180L, 0.48);
      UH.put(179L, 0.47);
      UH.put(178L, 0.47);
      UH.put(177L, 0.47);
      UH.put(176L, 0.46);
      UH.put(175L, 0.45);
      UH.put(174L, 0.45);
      UH.put(173L, 0.44);
      UH.put(172L, 0.43);
      UH.put(171L, 0.43);
      UH.put(170L, 0.43);
      UH.put(169L, 0.42);
      UH.put(168L, 0.41);
      UH.put(167L, 0.41);
      UH.put(166L, 0.4);
      UH.put(165L, 0.4);
      UH.put(164L, 0.4);
      UH.put(163L, 0.39);
      UH.put(162L, 0.38);
      UH.put(161L, 0.38);
      UH.put(160L, 0.38);
      UH.put(159L, 0.37);
      UH.put(158L, 0.36);
      UH.put(157L, 0.36);
      UH.put(156L, 0.35);
      UH.put(155L, 0.35);
      UH.put(154L, 0.35);
      UH.put(153L, 0.34);
      UH.put(152L, 0.33);
      UH.put(151L, 0.33);
      UH.put(150L, 0.33);
      UH.put(149L, 0.32);
      UH.put(148L, 0.31);
      UH.put(147L, 0.31);
      UH.put(146L, 0.31);
      UH.put(145L, 0.3);
      UH.put(144L, 0.3);
      UH.put(143L, 0.3);
      UH.put(142L, 0.29);
      UH.put(141L, 0.28);
      UH.put(140L, 0.28);
      UH.put(139L, 0.28);
      UH.put(138L, 0.27);
      UH.put(137L, 0.27);
      UH.put(136L, 0.27);
      UH.put(135L, 0.26);
      UH.put(134L, 0.26);
      UH.put(133L, 0.26);
      UH.put(132L, 0.25);
      UH.put(131L, 0.25);
      UH.put(130L, 0.25);
      UH.put(129L, 0.24);
      UH.put(128L, 0.23);
      UH.put(127L, 0.23);
      UH.put(126L, 0.23);
      UH.put(125L, 0.22);
      UH.put(124L, 0.22);
      UH.put(123L, 0.22);
      UH.put(122L, 0.21);
      UH.put(121L, 0.21);
      UH.put(120L, 0.2);
      UH.put(119L, 0.2);
      UH.put(118L, 0.2);
      UH.put(117L, 0.2);
      UH.put(116L, 0.2);
      UH.put(115L, 0.19);
      UH.put(114L, 0.19);
      UH.put(113L, 0.18);
      UH.put(112L, 0.18);
      UH.put(111L, 0.18);
      UH.put(110L, 0.17);
      UH.put(109L, 0.17);
      UH.put(108L, 0.17);
      UH.put(107L, 0.16);
      UH.put(106L, 0.16);
      UH.put(105L, 0.16);
      UH.put(104L, 0.16);
      UH.put(103L, 0.15);
      UH.put(102L, 0.15);
      UH.put(101L, 0.15);
      UH.put(100L, 0.14);
      UH.put(99L, 0.14);
      UH.put(98L, 0.14);
      UH.put(97L, 0.14);
      UH.put(96L, 0.13);
      UH.put(95L, 0.13);
      UH.put(94L, 0.13);
      UH.put(93L, 0.13);
      UH.put(92L, 0.13);
      UH.put(91L, 0.12);
      UH.put(90L, 0.12);
      UH.put(89L, 0.12);
      UH.put(88L, 0.12);
      UH.put(87L, 0.11);
      UH.put(86L, 0.11);
      UH.put(85L, 0.11);
      UH.put(84L, 0.11);
      UH.put(83L, 0.1);
      UH.put(82L, 0.1);
      UH.put(81L, 0.1);
      UH.put(80L, 0.1);
      UH.put(79L, 0.09);
      UH.put(78L, 0.09);
      UH.put(77L, 0.09);
      UH.put(76L, 0.09);
      UH.put(75L, 0.09);
      UH.put(74L, 0.09);
      UH.put(73L, 0.08);
      UH.put(72L, 0.08);
      UH.put(71L, 0.08);
      UH.put(70L, 0.08);
      UH.put(69L, 0.08);
      UH.put(68L, 0.07);
      UH.put(67L, 0.07);
      UH.put(66L, 0.07);
      UH.put(65L, 0.07);
      UH.put(64L, 0.07);
      UH.put(63L, 0.07);
      UH.put(62L, 0.06);
      UH.put(61L, 0.06);
      UH.put(60L, 0.06);
      UH.put(59L, 0.06);
      UH.put(58L, 0.06);
      UH.put(57L, 0.06);
      UH.put(56L, 0.06);
      UH.put(55L, 0.05);
      UH.put(54L, 0.05);
      UH.put(53L, 0.05);
      UH.put(52L, 0.05);
      UH.put(51L, 0.05);
      UH.put(50L, 0.05);
      UH.put(49L, 0.05);
      UH.put(48L, 0.04);
      UH.put(47L, 0.04);
      UH.put(46L, 0.04);
      UH.put(45L, 0.04);
      UH.put(44L, 0.04);
      UH.put(43L, 0.04);
      UH.put(42L, 0.04);
      UH.put(41L, 0.04);
      UH.put(40L, 0.03);
      UH.put(39L, 0.03);
      UH.put(38L, 0.03);
      UH.put(37L, 0.03);
      UH.put(36L, 0.03);
      UH.put(35L, 0.03);
      UH.put(34L, 0.03);
      UH.put(33L, 0.03);
      UH.put(32L, 0.03);
      UH.put(31L, 0.02);
      UH.put(30L, 0.02);
      UH.put(29L, 0.02);
      UH.put(28L, 0.02);
      UH.put(27L, 0.02);
      UH.put(26L, 0.02);
      UH.put(25L, 0.02);
      UH.put(24L, 0.02);
      UH.put(23L, 0.02);
      UH.put(22L, 0.02);
      UH.put(21L, 0.02);
      UH.put(20L, 0.02);
      UH.put(19L, 0.02);
      UH.put(18L, 0.01);
      UH.put(17L, 0.01);
      UH.put(16L, 0.01);
      UH.put(15L, 0.01);
      UH.put(14L, 0.01);
      UH.put(13L, 0.01);
      UH.put(12L, 0.01);
      UH.put(11L, 0.01);
      UH.put(10L, 0.01);
      UH.put(9L, 0.01);
      UH.put(8L, 0.01);
      UH.put(7L, 0.01);
      UH.put(6L, 0.01);
      UH.put(5L, 0.01);
      UH.put(4L, 0.01);
      UH.put(3L, 0.01);
      UH.put(2L, 0.01);
      UH.put(1L, 0.009);
      UH.put(0L, 0.009);
   }

   private Rk() {
   }

   public static Map AC() {
      return UH;
   }
}
