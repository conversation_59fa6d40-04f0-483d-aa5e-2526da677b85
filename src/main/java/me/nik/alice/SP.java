package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientSteerVehicle;

public class SP extends PacketCheck {
   private static String[] nF;

   static {
      vM();
   }

   public SP(UC var1) {
      super(var1, CheckType.PACKET, nF[0], Category.WORLD);
   }

   @Override
   public String sB() {
      return nF[1];
   }

   private static void vM() {
      nF = new String[]{"S", "Checks for impossible steer vehicle packets", "Side: ", " forward: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.Ej()) {
         WrapperPlayClientSteerVehicle var3;
         float var2 = Math.abs((var3 = new WrapperPlayClientSteerVehicle(var1.dx())).getSideways());
         float var4 = Math.abs(var3.getForward());
         if (var2 > 0.98F || var4 > 0.98F) {
            this.og(nF[2] + var2 + nF[3] + var4);
         }
      }
   }
}
