package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;

public class SU extends PacketCheck {
   private double b;
   private static String[] jM;

   static {
      OV();
   }

   @Override
   public String sB() {
      return jM[1];
   }

   private static void OV() {
      jM = new String[]{"Static", "Checks for static movement speed", "Delta: ", " delta accel: "};
   }

   public SU(UC var1) {
      super(var1, CheckType.SPEED, jM[0], Category.MOVE, 1.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().tr() && this.Aa.dx().GE() >= 10 && this.Aa.dx().bI() >= 20) {
         ES var20;
         double var3 = (var20 = this.Aa.dx()).DB();
         double var5 = var20.jD();
         double var7 = var3 - var5;
         int var2 = var20.dx().sB(1500L);
         double var10 = 0.3;
         if (var2 > 0) {
            var10 = 0.3 + (double)var2 * 0.072;
         }

         if ((double)(FastMath.max(0.2F, this.Aa.getPlayer().getWalkSpeed()) - 0.2F) > 0.0) {
            var10 += (double)(FastMath.max(0.2F, this.Aa.getPlayer().getWalkSpeed()) - 0.2F);
         }

         if (var20.yV() < 20) {
            var10 += 0.25;
         }

         if (!(var7 <= var10)) {
            double var14 = var20.F7();
            double var16 = this.b;
            this.b = var14;
            double var18;
            if ((var18 = Math.abs(var14 - var16)) < 1.0E-5) {
               this.Aa(jM[2] + var18 + jM[3] + var7);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.075);
            }
         }
      }
   }
}
