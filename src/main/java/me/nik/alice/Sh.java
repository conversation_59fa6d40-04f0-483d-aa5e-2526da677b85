package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Sh extends PacketCheck {
   private static String[] Hx;

   @Override
   public void dx(DH var1) {
      if (var1.Sh()) {
         if (this.Aa.dx().Vm() > 100 && !this.Aa.dx().Eu()) {
            this.Aa(Hx[2]);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Vm();
         }
      }
   }

   static {
      xH();
   }

   @Override
   public String sB() {
      return Hx[1];
   }

   public Sh(UC var1) {
      super(var1, CheckType.PACKET, Hx[0], Category.WORLD, 10.0F);
   }

   private static void xH() {
      Hx = new String[]{"D", "Checks for a rare disabler", "Null vehicle"};
   }
}
