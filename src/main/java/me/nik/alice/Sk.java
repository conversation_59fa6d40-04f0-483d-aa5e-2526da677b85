package me.nik.alice;

import com.comphenix.protocol.events.PacketContainer;
import java.util.Map;
import java.util.WeakHashMap;
import me.nik.alice.wrappers.WrapperPlayClientKeepAlive;
import me.nik.alice.wrappers.WrapperPlayClientTransaction;
import me.nik.alice.wrappers.WrapperPlayServerTransaction;

public class Sk {
   private long QJ;
   private long o4;
   private int Ej;
   private long Fh;
   private int Lh;
   private int J4;
   private long Q;
   private final UC Aa;
   private int PU;
   private long u;
   private final Map og = new WeakHashMap();
   private static String[] 7K;

   public void AC(DH var1) {
      long var2 = var1.getTimeStamp();
      PacketContainer var4 = var1.dx();
      if (var1.uX()) {
         this.u = var2;
         int var5;
         if ((var5 = Ku.iK()) > 1) {
            short var6 = (short)(32767 % var5);
            WrapperPlayServerTransaction var7;
            (var7 = new WrapperPlayServerTransaction()).setAccepted(false);
            var7.setActionNumber(var6);
            var7.setWindowId(0);
            this.og.put(var6, var2);
            var7.sendPacket(this.Aa.getPlayer());
            return;
         }
      } else {
         if (var1.hB()) {
            this.QJ = var2;
            WrapperPlayClientKeepAlive var11 = new WrapperPlayClientKeepAlive(var4);
            this.J4 = (int)Math.abs(this.u - var2);
            int var13 = (int)var11.getKeepAliveId();
            int var8 = this.PU;
            this.PU = var13;
            if (var13 == var8 && var8 != -1 && this.Aa.AC() > 10000L) {
               Alice.getAPI().flag(this.Aa.getPlayer(), 7K[0], 7K[1], 7K[2] + var13 + 7K[3] + var8);
            }

            return;
         }

         if (var1.tU()) {
            WrapperPlayClientTransaction var10;
            if ((var10 = new WrapperPlayClientTransaction(var4)).getWindowId() != 0) {
               return;
            }

            short var12 = var10.getActionNumber();
            if (this.og.containsKey(var12)) {
               this.Q = var2;
               this.Lh = (int)Math.abs((Long)this.og.remove(var12) - var2);
            }

            return;
         }

         if (var1.M3()) {
            long var9 = this.Fh;
            this.Fh = var2;
            if (var2 - var9 < 25L) {
               this.Ej++;
            } else if (this.Ej > 0) {
               this.Ej--;
            }

            if (this.Ej > 50) {
               this.Ej = 5;
            }

            if (this.Ej >= 5) {
               this.o4 = var2;
            }

            if (me.nik.alice.UN.dx.tU.dC() && this.Aa.AC() >= 30000L && (this.Zh() > 60000L || this.dC() > 60000L)) {
               this.Aa.sB(7K[4]);
            }
         }
      }
   }

   private static void Pe() {
      7K = new String[]{"BadPackets", "V", "ID: ", " Last: ", "Timed out"};
   }

   public long dC() {
      return l.og(this.Q);
   }

   static {
      Pe();
   }

   public int Zm() {
      return this.Lh;
   }

   public Sk(UC var1) {
      this.PU = -1;
      this.Aa = var1;
   }

   public long Zh() {
      return l.og(this.QJ);
   }

   public long h5() {
      return l.og(this.o4);
   }

   public int jA() {
      return this.J4;
   }

   public boolean Aa(int var1) {
      return this.Ej >= var1;
   }

   public long Fh() {
      return l.og(this.Fh);
   }
}
