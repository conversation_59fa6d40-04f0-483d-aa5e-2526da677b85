package me.nik.alice;

public enum Sl$Aa {
   Aa(Sl$Aa.4B[1]),
   og(Sl$Aa.4B[3]),
   AC(Sl$Aa.4B[5]),
   sB(Sl$Aa.4B[7]);

   private final String rc;
   private static final Sl$Aa[] dx = new Sl$Aa[]{Aa, og, AC, sB};
   private static String[] 4B;

   private Sl$Aa(String var3) {
      this.rc = var3;
   }

   public String DB() {
      return this.rc;
   }

   static {
      pS();
   }

   private static void pS() {
      4B = new String[]{"SHOW_TEXT", "show_text", "SHOW_ITEM", "show_item", "SHOW_ACHIEVEMENT", "show_achievement", "SHOW_STATISTIC", "show_achievement"};
   }
}
