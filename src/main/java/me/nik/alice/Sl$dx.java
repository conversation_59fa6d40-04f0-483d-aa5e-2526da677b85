package me.nik.alice;

public enum Sl$dx {
   Aa(Sl$dx.u1[1]),
   og(Sl$dx.u1[3]),
   AC(Sl$dx.u1[5]);

   private final String rc;
   private static final Sl$dx[] dx = new Sl$dx[]{Aa, og, AC};
   private static String[] u1;

   private Sl$dx(String var3) {
      this.rc = var3;
   }

   public String DB() {
      return this.rc;
   }

   static {
      10();
   }

   private static void _0/* $VF was: 10*/() {
      u1 = new String[]{"OPEN_URL", "open_url", "RUN_COMMAND", "run_command", "SUGGEST_TEXT", "suggest_command"};
   }
}
