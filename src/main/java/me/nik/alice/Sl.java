package me.nik.alice;

import com.comphenix.protocol.wrappers.WrappedChatComponent;
import com.comphenix.protocol.wrappers.EnumWrappers.ChatType;
import java.util.Collection;
import java.util.Optional;
import me.nik.alice.wrappers.WrapperPlayServerChat;
import org.bukkit.entity.Player;

public class Sl {
   private me.nik.alice.Sl.dx dx;
   private String Qt;
   private static String[] GY;
   private static String Zp;
   private String jD;
   private static String DB = GY[24] + Zp + GY[25];
   private me.nik.alice.Sl.Aa dx;

   public String Zp() {
      return DB;
   }

   private Optional Aa() {
      return Optional.of(this.Qt);
   }

   public Sl dx(me.nik.alice.Sl.dx var1, String var2) {
      this.Qt = var2;
      this.dx = var1;
      return this;
   }

   public Sl(String var1) {
      Zp = Dq.sB(var1);
   }

   static {
      8d();
   }

   public String F7() {
      return Zp;
   }

   public Sl dx(me.nik.alice.Sl.Aa var1, String var2) {
      this.jD = Dq.sB(var2);
      this.dx = var1;
      return this;
   }

   public void dx(Collection var1) {
      WrapperPlayServerChat var2;
      (var2 = new WrapperPlayServerChat()).setChatType(ChatType.CHAT);
      var2.setMessage(WrappedChatComponent.fromJson(DB));

      for (Player var3 : var1) {
         var2.sendPacket(var3);
      }
   }

   public void Zh(Player var1) {
      WrapperPlayServerChat var2;
      (var2 = new WrapperPlayServerChat()).setChatType(ChatType.CHAT);
      var2.setMessage(WrappedChatComponent.fromJson(DB));
      var2.sendPacket(var1);
   }

   public Sl dx() {
      if (!this.Aa().isPresent() && !this.dx().isPresent()) {
         DB = GY[0] + Zp + GY[1];
      }

      if (!this.Aa().isPresent() && this.dx().isPresent()) {
         if (this.dx == me.nik.alice.Sl.Aa.AC) {
            DB = GY[2] + Zp + GY[3] + this.dx.DB() + GY[4] + this.jD + GY[5];
         } else if (this.dx == me.nik.alice.Sl.Aa.sB) {
            DB = GY[6] + Zp + GY[7] + this.dx.DB() + GY[8] + this.jD + GY[9];
         } else {
            DB = GY[10] + Zp + GY[11] + this.dx.DB() + GY[12] + this.jD + GY[13];
         }
      }

      if (this.Aa().isPresent() && this.dx().isPresent()) {
         DB = GY[14] + Zp + GY[15] + this.dx.DB() + GY[16] + this.Qt + GY[17] + this.dx.DB() + GY[18] + this.jD + GY[19];
      }

      if (this.Aa().isPresent() && !this.dx().isPresent()) {
         DB = GY[20] + Zp + GY[21] + this.dx.DB() + GY[22] + this.Qt + GY[23];
      }

      return this;
   }

   private Optional dx() {
      return Optional.of(this.jD);
   }

   private static void _d/* $VF was: 8d*/() {
      GY = new String[]{
         "{\"text\":\"",
         "\"}",
         "{\"text\":\"",
         "\",\"hoverEvent\":{\"action\":\"",
         "\",\"value\":\"achievement.",
         "\"}}",
         "{\"text\":\"",
         "\",\"hoverEvent\":{\"action\":\"",
         "\",\"value\":\"stat.",
         "\"}}",
         "{\"text\":\"",
         "\",\"hoverEvent\":{\"action\":\"",
         "\",\"value\":\"",
         "\"}}",
         "{\"text\":\"",
         "\",\"clickEvent\":{\"action\":\"",
         "\",\"value\":\"",
         "\"},\"hoverEvent\":{\"action\":\"",
         "\",\"value\":\"",
         "\"}}",
         "{\"text\":\"",
         "\",\"clickEvent\":{\"action\":\"",
         "\",\"value\":\"",
         "\"}}",
         "{\"text\":\"",
         "\"}"
      };
   }
}
