package me.nik.alice;

public class Sm {
   private final double Vm;
   private final double sX;
   private final double WB;
   private final long timeStamp;

   public Sm(double var1, double var3, double var5, long var7) {
      this.sX = var1;
      this.WB = var3;
      this.Vm = var5;
      this.timeStamp = var7;
   }

   public double getY() {
      return this.WB;
   }

   public long getTimeStamp() {
      return this.timeStamp;
   }

   public double getX() {
      return this.sX;
   }

   public double getZ() {
      return this.Vm;
   }

   public boolean Aa(double var1, double var3, double var5) {
      return Math.abs(this.sX - var1) < 0.01 && Math.abs(this.WB - var3) < 0.01 && Math.abs(this.Vm - var5) < 0.01;
   }
}
