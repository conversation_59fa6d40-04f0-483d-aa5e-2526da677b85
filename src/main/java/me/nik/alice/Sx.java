package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Sx extends PacketCheck {
   private static String[] 2h;

   @Override
   public String sB() {
      return 2h[1];
   }

   @Override
   public void dx(DH var1) {
      boolean var12 = KAFc;
      if (var1.pQ() && !this.Aa.dx().Fh() && this.Aa.dx().Wx() >= 40) {
         ES var13 = this.Aa.dx();
         Qk var2 = this.Aa.dx();
         double var4 = var13.Qt();
         double var6 = 0.42F;
         int var3;
         if ((var3 = var13.dx().og(2000L)) > 0) {
            var6 = 0.42F + (double)var3 * 0.11;
         }

         double var10001;
         if (var2.tr()) {
            var10001 = Math.abs(var2.getVelocityY());
            if (var12) {
               throw null;
            }
         } else {
            var10001 = 0.0;
         }

         var6 += var10001;
         if (var13.R2() < 60) {
            var6 += 1.15 * Math.abs(var13.Ch());
         }

         MN var14;
         if ((var14 = this.Aa.dx()).GE() < 10) {
            var6 += var14.jA();
         }

         double var10 = Math.abs(var4 - var13.Ch());
         boolean var10000;
         if (var4 > var6 && var10 >= 1.0 && var13.a() > 0) {
            var10000 = true;
            if (var12) {
               throw null;
            }
         } else {
            var10000 = false;
         }

         if (var10000) {
            this.Aa(2h[2] + var4 + 2h[3] + var6 + 2h[4] + var10);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.05);
         }
      }
   }

   private static void sF() {
      2h = new String[]{"F", "Checks for abnormal accelerations", "Acceleration: ", " predicted: ", " delta diff: "};
   }

   static {
      sF();
   }

   public Sx(UC var1) {
      super(var1, CheckType.FLY, 2h[0], Category.MOVE, 1.0F);
   }
}
