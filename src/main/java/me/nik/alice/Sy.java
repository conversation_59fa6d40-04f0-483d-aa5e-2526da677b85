package me.nik.alice;

import java.util.Collection;
import me.nik.alice.api.events.AlicePunishWaveEvent;
import org.bukkit.Bukkit;
import org.bukkit.Color;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;

public class Sy implements Listener {
   private static String[] ZQ;

   private static void vl() {
      ZQ = new String[]{"%count%", "Punish Wave", "Server", "Players Punished"};
   }

   @EventHandler
   public void dx(AlicePunishWaveEvent var1) {
      Collection var3 = var1.getPunishedPlayers();
      if (me.nik.alice.UN.dx.bI.dC()) {
         Bukkit.broadcastMessage(F.x.getMessage().replace(ZQ[0], String.valueOf(var3.size())));
      }

      if (me.nik.alice.UN.dx.b.dC()) {
         OB var2;
         (var2 = new OB(me.nik.alice.UN.dx.Zh.b()))
            .dx(new me.nik.alice.OB.dx().dx(ZQ[1]).dx(ZQ[2], me.nik.alice.UN.dx.AC.b()).dx(ZQ[3], var3.toString()).dx(Color.ORANGE));
         var2.Zm();
      }
   }

   static {
      vl();
   }
}
