package me.nik.alice;

import java.util.List;
import java.util.concurrent.TimeUnit;

public abstract class TQ {
   protected final Alice plugin;
   protected final long jD;

   public TQ(Alice var1) {
      this.plugin = var1;
      this.jD = TimeUnit.DAYS.toMillis((long)me.nik.alice.UN.dx.Q.Aa());
   }

   public abstract List h5();

   public abstract List dx(String var1);

   public abstract void gz();

   public abstract void dx(hL var1);

   public abstract void dC();
}
