package me.nik.alice;

import java.lang.reflect.Constructor;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import org.bukkit.Bukkit;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public final class TY {
   private static String Zm;
   private static final Map Fh = new HashMap();
   private static String[] gu;
   private static final Map Zh = new HashMap();
   private static final Map dC = new HashMap();
   private static final Map h5 = new HashMap();

   public static Class dx(String var0) {
      boolean var2 = zJ2P;
      if (dC.containsKey(var0)) {
         return (Class)dC.get(var0);
      } else {
         String var1 = gu[1] + getVersion() + var0;

         try {
            var4 = Class.forName(var1);
         } catch (Throwable var3) {
            var3.printStackTrace();
            return (Class)dC.put(var0, null);
         }

         if (var2) {
            throw null;
         } else {
            dC.put(var0, var4);
            return var4;
         }
      }
   }

   public static Method dx(Class var0, String var1, Class[] var2) {
      if (!h5.containsKey(var0)) {
         h5.put(var0, new HashMap());
      }

      Map var3;
      if ((var3 = (Map)h5.get(var0)).containsKey(var1)) {
         return (Method)var3.get(var1);
      } else {
         try {
            var5 = var0.getMethod(var1, var2);
            var3.put(var1, var5);
            h5.put(var0, var3);
         } catch (Exception var4) {
            var4.printStackTrace();
            var3.put(var1, null);
            h5.put(var0, var3);
            throw new xt(gu[23] + var0.getSimpleName());
         }

         return var5;
      }
   }

   public static Object dx(Entity var0) {
      String[] var10000 = gu;
      byte var10001 = 7;

      try {
         return dx(Aa(var10000[var10001]), gu[8], new Class[0]).invoke(var0);
      } catch (InvocationTargetException | IllegalAccessException var1) {
         var1.printStackTrace();
         return null;
      }
   }

   public static Object dx(Player var0) {
      Method var1;
      Method var10000 = var1 = dx(var0.getClass(), gu[3], new Class[0]);

      try {
         var10000.getClass();
      } catch (Exception var3) {
         return null;
      }

      var10000 = var1;

      try {
         Object var4;
         return dx((var4 = var10000.invoke(var0)).getClass(), gu[4]).get(var4);
      } catch (Exception var2) {
         var2.printStackTrace();
         return null;
      }
   }

   public static Class Aa(String var0) {
      if (Zh.containsKey(var0)) {
         return (Class)Zh.get(var0);
      } else {
         String var1 = gu[2] + getVersion() + var0;

         try {
            var3 = Class.forName(var1);
         } catch (Throwable var2) {
            var2.printStackTrace();
            Zh.put(var0, null);
            return null;
         }

         Zh.put(var0, var3);
         return var3;
      }
   }

   public static void dx(Player var0, Object var1) {
      Method var2;
      Object var5;
      Method var10000 = var2 = dx((var5 = dx(var0)).getClass(), gu[5], new Class[]{dx(gu[6])});

      try {
         var10000.getClass();
      } catch (Exception var4) {
         return;
      }

      var10000 = var2;
      Object var10001 = var5;
      Object[] var10002 = new Object[1];
      Object[] var10003 = var10002;
      byte var10004 = 0;
      Object var10005 = var1;

      try {
         var10003[var10004] = var10005;
         var10000.invoke(var10001, var10002);
      } catch (InvocationTargetException | IllegalAccessException var3) {
         var3.printStackTrace();
      }
   }

   static {
      hZ();
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private static Vector dx(Object var0) {
      double var1 = 0.0;
      double var3 = 0.0;
      double var5 = 0.0;
      Class var7 = var0.getClass();

      IllegalAccessException var10000;
      label107: {
         try {
            var19 = w.v();
         } catch (IllegalAccessException var18) {
            var10000 = var18;
            boolean var10001 = false;
            break label107;
         }

         if (var19) {
            Class var20 = var7;

            try {
               var21 = (Double)dx(var20, gu[11]).get(var0);
            } catch (IllegalAccessException var13) {
               var10000 = var13;
               boolean var33 = false;
               break label107;
            }

            try {
               var22 = var21;
            } catch (IllegalAccessException var12) {
               var10000 = var12;
               boolean var34 = false;
               break label107;
            }

            var1 = var22;
            Class var23 = var7;
            String[] var35 = gu;
            byte var10002 = 12;

            try {
               var3 = (Double)dx(var23, var35[var10002]).get(var0);
            } catch (IllegalAccessException var11) {
               var10000 = var11;
               boolean var36 = false;
               break label107;
            }

            Class var24 = var7;
            var35 = gu;
            var10002 = 13;

            try {
               var25 = dx(var24, var35[var10002]);
               var35 = (String[])var0;
            } catch (IllegalAccessException var10) {
               var10000 = var10;
               boolean var38 = false;
               break label107;
            }

            try {
               var26 = (Double)var25.get(var35);
            } catch (IllegalAccessException var9) {
               var10000 = var9;
               boolean var40 = false;
               break label107;
            }

            try {
               var27 = var26;
            } catch (IllegalAccessException var8) {
               var10000 = var8;
               boolean var41 = false;
               break label107;
            }

            var5 = var27;
         } else {
            Class var28 = var7;
            String var42 = gu[14];

            try {
               var29 = dx(var28, var42);
            } catch (IllegalAccessException var17) {
               var10000 = var17;
               boolean var43 = false;
               break label107;
            }

            try {
               var1 = (Double)var29.get(var0);
            } catch (IllegalAccessException var16) {
               var10000 = var16;
               boolean var44 = false;
               break label107;
            }

            Class var30 = var7;
            var42 = gu[15];

            try {
               var3 = (Double)dx(var30, var42).get(var0);
            } catch (IllegalAccessException var15) {
               var10000 = var15;
               boolean var46 = false;
               break label107;
            }

            Class var31 = var7;

            try {
               var32 = (Double)dx(var31, gu[16]).get(var0);
            } catch (IllegalAccessException var14) {
               var10000 = var14;
               boolean var47 = false;
               break label107;
            }

            var5 = var32;
         }

         return new Vector(var1, var3, var5);
      }

      var10000.printStackTrace();
      return new Vector(var1, var3, var5);
   }

   public static Constructor dx(Class var0, Class[] var1) {
      Class var10000 = var0;
      Class[] var10001 = var1;

      try {
         return var10000.getConstructor(var10001);
      } catch (NoSuchMethodException var2) {
         return null;
      }
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private static Vector Aa(Object var0) {
      double var1 = 0.0;
      double var3 = 0.0;
      double var5 = 0.0;
      Class var7 = var0.getClass();

      IllegalAccessException var10000;
      label111: {
         label118: {
            try {
               if (w.v()) {
                  break label118;
               }
            } catch (IllegalAccessException var21) {
               var10000 = var21;
               boolean var10001 = false;
               break label111;
            }

            try {
               var22 = (Double)dx(var7, gu[20]).get(var0);
            } catch (IllegalAccessException var20) {
               var10000 = var20;
               boolean var33 = false;
               break label111;
            }

            try {
               var1 = var22;
            } catch (IllegalAccessException var19) {
               var10000 = var19;
               boolean var34 = false;
               break label111;
            }

            try {
               var23 = dx(var7, gu[21]);
            } catch (IllegalAccessException var18) {
               var10000 = var18;
               boolean var35 = false;
               break label111;
            }

            try {
               var24 = var23.get(var0);
            } catch (IllegalAccessException var17) {
               var10000 = var17;
               boolean var36 = false;
               break label111;
            }

            try {
               var3 = (Double)var24;
            } catch (IllegalAccessException var16) {
               var10000 = var16;
               boolean var37 = false;
               break label111;
            }

            Class var25 = var7;
            String var38 = gu[22];

            try {
               var26 = (Double)dx(var25, var38).get(var0);
            } catch (IllegalAccessException var15) {
               var10000 = var15;
               boolean var39 = false;
               break label111;
            }

            var5 = var26;
            return new Vector(var1, var3, var5);
         }

         Object var41;
         try {
            var27 = dx(var7, gu[17]);
            var41 = var0;
         } catch (IllegalAccessException var14) {
            var10000 = var14;
            boolean var40 = false;
            break label111;
         }

         try {
            var28 = var27.get(var41);
         } catch (IllegalAccessException var13) {
            var10000 = var13;
            boolean var42 = false;
            break label111;
         }

         try {
            var1 = (Double)var28;
         } catch (IllegalAccessException var12) {
            var10000 = var12;
            boolean var43 = false;
            break label111;
         }

         Class var29 = var7;
         var41 = gu[18];

         try {
            var30 = dx(var29, (String)var41);
         } catch (IllegalAccessException var11) {
            var10000 = var11;
            boolean var45 = false;
            break label111;
         }

         try {
            var31 = (Double)var30.get(var0);
         } catch (IllegalAccessException var10) {
            var10000 = var10;
            boolean var46 = false;
            break label111;
         }

         try {
            var32 = var31;
         } catch (IllegalAccessException var9) {
            var10000 = var9;
            boolean var47 = false;
            break label111;
         }

         var3 = var32;

         try {
            var5 = (Double)dx(var7, gu[19]).get(var0);
            return new Vector(var1, var3, var5);
         } catch (IllegalAccessException var8) {
            var10000 = var8;
            boolean var48 = false;
         }
      }

      var10000.printStackTrace();
      return new Vector(var1, var3, var5);
   }

   private TY() {
   }

   public static gW dx(Object var0) {
      Vector var1 = dx(var0);
      var0 = Aa(var0);
      return gW.dx(var1, var0);
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   public static Field dx(Class var0, String var1) {
      if (!Fh.containsKey(var0)) {
         Fh.put(var0, new HashMap());
      }

      Map var2;
      if ((var2 = (Map)Fh.get(var0)).containsKey(var1)) {
         return (Field)var2.get(var1);
      } else {
         Exception var10000;
         label39: {
            Field var3;
            try {
               var3 = var0.getField(var1);
            } catch (Exception var6) {
               var10000 = var6;
               boolean var10001 = false;
               break label39;
            }

            try {
               var2.put(var1, var3);
            } catch (Exception var5) {
               var10000 = var5;
               boolean var7 = false;
               break label39;
            }

            try {
               Fh.put(var0, var2);
               return var3;
            } catch (Exception var4) {
               var10000 = var4;
               boolean var8 = false;
            }
         }

         var10000.printStackTrace();
         var2.put(var1, null);
         Fh.put(var0, var2);
         throw new xt(gu[24] + var0.getSimpleName());
      }
   }

   public static void clear() {
      Zm = null;
      dC.clear();
      Zh.clear();
      h5.clear();
      Fh.clear();
   }

   private static void hZ() {
      gu = new String[]{
         ".",
         "net.minecraft.server.",
         "org.bukkit.craftbukkit.",
         "getHandle",
         "playerConnection",
         "sendPacket",
         "Packet",
         "entity.CraftEntity",
         "getHandle",
         "Entity",
         "getBoundingBox",
         "a",
         "b",
         "c",
         "minX",
         "minY",
         "minZ",
         "d",
         "e",
         "f",
         "maxX",
         "maxY",
         "maxZ",
         "Couldn't find method at class ",
         "Couldn't find field at class ",
         "Couldn't find method at class "
      };
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   public static gW Aa(Entity var0) {
      if (w.v()) {
         String[] var10000 = gu;
         byte var10001 = 9;

         label39: {
            byte var10002;
            try {
               var5 = dx(var10000[var10001]);
               var8 = gu;
               var10002 = 10;
            } catch (InvocationTargetException | IllegalAccessException var3) {
               var4 = var3;
               boolean var7 = false;
               break label39;
            }

            String var9 = var8[var10002];
            var10002 = 0;

            try {
               var6 = dx(var5, var9, new Class[var10002]);
               var11 = dx(var0);
            } catch (InvocationTargetException | IllegalAccessException var2) {
               var4 = var2;
               boolean var10 = false;
               break label39;
            }

            Object[] var14 = new Object[0];

            try {
               return dx(var6.invoke(var11, var14));
            } catch (InvocationTargetException | IllegalAccessException var1) {
               var4 = var1;
               boolean var12 = false;
            }
         }

         var4.printStackTrace();
         return null;
      } else {
         return gW.dx(var0);
      }
   }

   public static Method dx(Class var0, int var1, int var2, Class var3, Class[] var4) {
      int var5 = var4.length;
      Method[] var6;
      int var7 = (var6 = var0.getMethods()).length;

      for (int var8 = 0; var8 < var7; var8++) {
         Method var9;
         int var10;
         if ((var9 = var6[var8]).getParameterCount() == var5
            && var9.getReturnType() == var3
            && ((var10 = var9.getModifiers()) & var1) == var1
            && (var10 & var2) == 0
            && Arrays.equals(var9.getParameterTypes(), var4)) {
            return var9;
         }
      }

      throw new xt(gu[25] + var0.getSimpleName());
   }

   public static String getVersion() {
      String var10000 = Zm;

      try {
         var10000.getClass();
      } catch (Exception var1) {
         String var0 = Bukkit.getServer().getClass().getPackage().getName();
         Zm = var0.substring(var0.lastIndexOf(46) + 1) + gu[0];
      }

      return Zm;
   }
}
