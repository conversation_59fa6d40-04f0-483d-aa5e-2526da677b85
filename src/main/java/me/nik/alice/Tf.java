package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Tf extends PacketCheck {
   private long Fh;
   private boolean dC;
   private static String[] c7;

   static {
      KW();
   }

   @Override
   public void dx(DH var1) {
      if (!var1.M3()) {
         long var8;
         if (var1.Tq() && (var8 = var1.getTimeStamp() - this.Fh) > 0L && var8 < 10L) {
            this.dC = true;
         }
      } else {
         long var2 = var1.getTimeStamp();
         long var4 = this.Fh;
         this.Fh = var2;
         if (this.dC) {
            long var6;
            if ((var6 = var2 - var4) > 40L && var6 < 100L) {
               this.Aa(c7[2].concat(String.valueOf(var6)));
               if (this.Aa() > this.dx()) {
                  this.sX();
               }
            } else {
               this.Aa(0.75);
            }
         }

         this.dC = false;
      }
   }

   @Override
   public String sB() {
      return c7[1];
   }

   public Tf(UC var1) {
      super(var1, CheckType.KILLAURA, c7[0], Category.COMBAT, 4.0F);
   }

   private static void KW() {
      c7 = new String[]{"E", "Checks for post aura", "Delta: "};
   }
}
