package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Tq extends PacketCheck {
   private static String[] yi;

   @Override
   public String sB() {
      return yi[1];
   }

   private static void _o/* $VF was: 0o*/() {
      yi = new String[]{"B", "Checks for flight", "Delta Y: ", " last delta Y: ", " fly ticks: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().jD() && this.Aa.dx().M3() != 0 && !this.Aa.dx().DB() && !this.Aa.dx().dx().DP()) {
         ES var10 = this.Aa.dx();
         Qk var2;
         boolean var3 = (var2 = this.Aa.dx()).tr();
         double var5 = var10.Ch() - (var3 ? Math.abs(var2.getVelocityY() * 5.0) : 0.0);
         int var12;
         if ((var12 = var10.dx().og(2000L)) > 0) {
            var5 -= (double)((float)var12 * 0.11F);
         }

         if (var10.jA()) {
            var5 -= 0.5;
         }

         double var8 = var10.iv();
         int var11;
         if ((var11 = var10.a()) > 2 && var5 >= var8) {
            this.Aa(yi[2] + var5 + yi[3] + var8 + yi[4] + var11);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.05);
         }
      }
   }

   static {
      0o();
   }

   public Tq(UC var1) {
      super(var1, CheckType.FLY, yi[0], Category.MOVE, 1.0F);
   }
}
