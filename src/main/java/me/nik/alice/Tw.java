package me.nik.alice;

import java.io.File;
import java.io.IOException;
import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class Tw extends TQ {
   private static String[] XS;
   private Connection connection;

   public boolean gz() {
      Connection var10000 = this.connection;

      try {
         var10000.equals(null);
         return true;
      } catch (Exception var1) {
         return false;
      }
   }

   @Override
   public void dC() {
      wf.Aa(this::yV);
   }

   @Override
   public List h5() {
      CompletableFuture var1 = CompletableFuture.supplyAsync(this::Fh);
      CompletableFuture var10000 = var1;
      long var10001 = 10L;
      TimeUnit var10002 = TimeUnit.SECONDS;

      try {
         var3 = var10000.get(var10001, var10002);
      } catch (ExecutionException | TimeoutException | InterruptedException var2) {
         this.plugin.getLogger().severe(XS[1]);
         var2.printStackTrace();
         return new ArrayList();
      }

      return (List)var3;
   }

   @Override
   public void gz() {
      if (this.gz()) {
         try {
            this.connection.close();
         } catch (SQLException var2) {
            Dq.GE();
            var2.printStackTrace();
         }
      }
   }

   @Override
   public List dx(String var1) {
      CompletableFuture var3 = CompletableFuture.supplyAsync(this::Aa);

      try {
         return (List)var3.get(10L, TimeUnit.SECONDS);
      } catch (ExecutionException | TimeoutException | InterruptedException var2) {
         this.plugin.getLogger().severe(XS[2]);
         var2.printStackTrace();
         return new ArrayList();
      }
   }

   public Tw(Alice var1) {
      super(var1);
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   @Override
   public void dx(hL var1) {
      String var2 = var1.gz();
      String var3 = var1.getPlayer();
      String var4 = var1.tm();
      String var5 = var1.getCheck();
      String var6 = var1.getInformation();
      String var18 = var1.x();

      SQLException var10000;
      label77: {
         try {
            var20 = this.getConnection();
         } catch (SQLException var17) {
            var10000 = var17;
            boolean var10001 = false;
            break label77;
         }

         PreparedStatement var7;
         try {
            var21 = var7 = var20.prepareStatement(XS[0]);
         } catch (SQLException var16) {
            var10000 = var16;
            boolean var24 = false;
            break label77;
         }

         try {
            var21.setString(1, var2);
         } catch (SQLException var15) {
            var10000 = var15;
            boolean var25 = false;
            break label77;
         }

         PreparedStatement var22 = var7;
         byte var26 = 2;

         try {
            var22.setString(var26, var3);
         } catch (SQLException var14) {
            var10000 = var14;
            boolean var27 = false;
            break label77;
         }

         try {
            var7.setString(3, var4);
         } catch (SQLException var13) {
            var10000 = var13;
            boolean var28 = false;
            break label77;
         }

         try {
            var7.setString(4, var5);
         } catch (SQLException var12) {
            var10000 = var12;
            boolean var29 = false;
            break label77;
         }

         PreparedStatement var23 = var7;
         var26 = 5;

         try {
            var23.setString(var26, var6);
         } catch (SQLException var11) {
            var10000 = var11;
            boolean var31 = false;
            break label77;
         }

         try {
            var7.setString(6, var18);
         } catch (SQLException var10) {
            var10000 = var10;
            boolean var32 = false;
            break label77;
         }

         try {
            var7.executeUpdate();
         } catch (SQLException var9) {
            var10000 = var9;
            boolean var33 = false;
            break label77;
         }

         try {
            var7.close();
            return;
         } catch (SQLException var8) {
            var10000 = var8;
            boolean var34 = false;
         }
      }

      SQLException var19 = var10000;
      Dq.GE();
      var19.printStackTrace();
   }

   static {
      xN();
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private List Aa(String var1) {
      boolean var4 = OabI;

      SQLException var10000;
      label87: {
         ArrayList var2;
         try {
            var2 = new ArrayList();
         } catch (SQLException var15) {
            var10000 = var15;
            boolean var10001 = false;
            break label87;
         }

         Tw var18 = this;

         try {
            var19 = var18.getConnection();
         } catch (SQLException var14) {
            var10000 = var14;
            boolean var22 = false;
            break label87;
         }

         PreparedStatement var24;
         try {
            var20 = var19.prepareStatement(XS[3]);
            var24 = var20;
         } catch (SQLException var13) {
            var10000 = var13;
            boolean var23 = false;
            break label87;
         }

         PreparedStatement var3;
         try {
            var3 = var24;
            var20.setString(1, var1);
         } catch (SQLException var12) {
            var10000 = var12;
            boolean var25 = false;
            break label87;
         }

         try {
            var16 = var3.executeQuery();
         } catch (SQLException var11) {
            var10000 = var11;
            boolean var26 = false;
            break label87;
         }

         while (true) {
            try {
               if (!var16.next()) {
                  return var2;
               }
            } catch (SQLException var10) {
               var10000 = var10;
               boolean var27 = false;
               break;
            }

            hL var10002;
            String var10003;
            ResultSet var10004;
            String[] var10005;
            byte var10006;
            try {
               var21 = var2;
               var29 = new hL;
               var10002 = var29;
               var10003 = var16.getString(XS[4]);
               var10004 = var16;
               var10005 = XS;
               var10006 = 5;
            } catch (SQLException var9) {
               var10000 = var9;
               boolean var28 = false;
               break;
            }

            try {
               var34 = var10004.getString(var10005[var10006]);
            } catch (SQLException var8) {
               var10000 = var8;
               boolean var30 = false;
               break;
            }

            ResultSet var35 = var16;

            ResultSet var10007;
            try {
               var10005 = var35.getString(XS[6]);
               var37 = var16.getString(XS[7]);
               var10007 = var16;
            } catch (SQLException var7) {
               var10000 = var7;
               boolean var31 = false;
               break;
            }

            ResultSet var10008;
            String[] var10009;
            try {
               var38 = var10007.getString(XS[8]);
               var10008 = var16;
               var10009 = XS;
            } catch (SQLException var6) {
               var10000 = var6;
               boolean var32 = false;
               break;
            }

            try {
               var10002.<init>(var10003, var34, var10005, var37, var38, var10008.getString(var10009[9]));
               var21.add(var29);
            } catch (SQLException var5) {
               var10000 = var5;
               boolean var33 = false;
               break;
            }

            if (var4) {
               throw null;
            }
         }
      }

      SQLException var17 = var10000;
      Dq.GE();
      var17.printStackTrace();
      return new ArrayList();
   }

   private static void xN() {
      XS = new String[]{
         "INSERT INTO alice_playerdata (id,server,player,uuid,checktype,information,timestamp) VALUES (null,?,?,?,?,?,?)",
         "Took more than 10 seconds to load the player logs!",
         "Took more than 10 seconds to load the player logs!",
         "SELECT * FROM alice_playerdata WHERE player=?",
         "server",
         "player",
         "uuid",
         "checktype",
         "information",
         "timestamp",
         "SELECT * FROM alice_playerdata",
         "server",
         "player",
         "uuid",
         "checktype",
         "information",
         "timestamp",
         "data.db",
         "org.sqlite.JDBC",
         "jdbc:sqlite:",
         "CREATE TABLE IF NOT EXISTS alice_playerdata (id integer primary key autoincrement,server varchar(50),player varchar(100),uuid varchar(100),checktype varchar(100),information varchar(100),timestamp varchar(100))",
         "SELECT * FROM alice_playerdata",
         "dd.MM.yyyy HH:mm",
         "timestamp",
         "DELETE FROM alice_playerdata WHERE id = ?",
         "id",
         "Couldn't initialize SQLite"
      };
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private void yV() {
      SQLException var10000;
      label170: {
         File var45;
         File var10002;
         String[] var10003;
         try {
            var30 = new File;
            var45 = var30;
            var10002 = this.plugin.getDataFolder();
            var10003 = XS;
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var26) {
            var10000 = var26;
            boolean var10001 = false;
            break label170;
         }

         try {
            var45.<init>(var10002, var10003[17]);
            var45 = var30;
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var25) {
            var10000 = var25;
            boolean var46 = false;
            break label170;
         }

         File var1;
         label159: {
            try {
               var1 = var45;
               if (var30.exists()) {
                  break label159;
               }
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var24) {
               var10000 = var24;
               boolean var48 = false;
               break label170;
            }

            try {
               var1.createNewFile();
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var23) {
               var10000 = var23;
               boolean var49 = false;
               break label170;
            }
         }

         String var31 = XS[18];

         try {
            Class.forName(var31);
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var22) {
            var10000 = var22;
            boolean var50 = false;
            break label170;
         }

         Tw var32 = this;
         String[] var51 = XS;
         byte var77 = 19;

         try {
            var32.connection = DriverManager.getConnection(var51[var77].concat(String.valueOf(var1)));
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var21) {
            var10000 = var21;
            boolean var52 = false;
            break label170;
         }

         Connection var33 = this.connection;
         String[] var53 = XS;

         try {
            var34 = var33.prepareStatement(var53[20]);
            var55 = var34;
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var20) {
            var10000 = var20;
            boolean var54 = false;
            break label170;
         }

         try {
            var27 = var55;
            var34.executeUpdate();
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var19) {
            var10000 = var19;
            boolean var56 = false;
            break label170;
         }

         PreparedStatement var35 = var27;

         try {
            var35.close();
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var18) {
            var10000 = var18;
            boolean var57 = false;
            break label170;
         }

         try {
            var36 = this.getConnection();
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var17) {
            var10000 = var17;
            boolean var58 = false;
            break label170;
         }

         String var59 = XS[21];

         try {
            var37 = var36.prepareStatement(var59).executeQuery();
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var16) {
            var10000 = var16;
            boolean var60 = false;
            break label170;
         }

         ResultSet var28 = var37;

         try {
            var38 = new SimpleDateFormat(XS[22]);
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var15) {
            var10000 = var15;
            boolean var61 = false;
            break label170;
         }

         SimpleDateFormat var2 = var38;

         try {
            var39 = new Date;
            var63 = var39;
            var78 = System.currentTimeMillis();
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var14) {
            var10000 = var14;
            boolean var62 = false;
            break label170;
         }

         Date var3;
         try {
            var63.<init>(var78);
            var3 = var39;
         } catch (ParseException | ClassNotFoundException | IOException | SQLException var12) {
            var10000 = var12;
            boolean var64 = false;
            break label170;
         }

         while (true) {
            ResultSet var40 = var28;

            try {
               var41 = var40.next();
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var6) {
               var10000 = var6;
               boolean var65 = false;
               break;
            }

            if (!var41) {
               try {
                  var28.close();
                  return;
               } catch (ParseException | ClassNotFoundException | IOException | SQLException var5) {
                  var10000 = var5;
                  boolean var76 = false;
                  break;
               }
            }

            SimpleDateFormat var42 = var2;
            ResultSet var66 = var28;

            try {
               var68 = var66.getString(XS[23]);
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var11) {
               var10000 = var11;
               boolean var67 = false;
               break;
            }

            try {
               if (Math.abs(var42.parse(var68).getTime() - var3.getTime()) <= this.jD) {
                  continue;
               }
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var13) {
               var10000 = var13;
               boolean var69 = false;
               break;
            }

            try {
               var43 = this.getConnection();
               var71 = XS[24];
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var10) {
               var10000 = var10;
               boolean var70 = false;
               break;
            }

            PreparedStatement var4;
            try {
               var44 = var4 = var43.prepareStatement(var71);
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var9) {
               var10000 = var9;
               boolean var72 = false;
               break;
            }

            byte var73 = 1;
            ResultSet var79 = var28;
            var10003 = XS;
            byte var10004 = 25;

            try {
               var44.setInt(var73, var79.getInt(var10003[var10004]));
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var8) {
               var10000 = var8;
               boolean var74 = false;
               break;
            }

            try {
               var4.executeUpdate();
            } catch (ParseException | ClassNotFoundException | IOException | SQLException var7) {
               var10000 = var7;
               boolean var75 = false;
               break;
            }
         }
      }

      SQLException var29 = var10000;
      this.plugin.getLogger().info(XS[26]);
      var29.printStackTrace();
   }

   public Connection getConnection() {
      return this.connection;
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private List Fh() {
      SQLException var10000;
      label62: {
         ArrayList var1;
         try {
            var1 = new ArrayList();
         } catch (SQLException var9) {
            var10000 = var9;
            boolean var10001 = false;
            break label62;
         }

         Tw var11 = this;

         String[] var15;
         try {
            var12 = var11.getConnection();
            var15 = XS;
         } catch (SQLException var8) {
            var10000 = var8;
            boolean var14 = false;
            break label62;
         }

         ResultSet var2;
         try {
            var2 = var12.prepareStatement(var15[10]).executeQuery();
         } catch (SQLException var7) {
            var10000 = var7;
            boolean var16 = false;
            break label62;
         }

         while (true) {
            try {
               if (!var2.next()) {
                  return var1;
               }
            } catch (SQLException var6) {
               var10000 = var6;
               boolean var17 = false;
               break;
            }

            ArrayList var13 = var1;

            hL var10002;
            String var10003;
            String var10004;
            ResultSet var10005;
            String[] var10006;
            try {
               var19 = new hL;
               var10002 = var19;
               var10003 = var2.getString(XS[11]);
               var10004 = var2.getString(XS[12]);
               var10005 = var2;
               var10006 = XS;
            } catch (SQLException var5) {
               var10000 = var5;
               boolean var18 = false;
               break;
            }

            byte var10007 = 13;

            try {
               var22 = var10005.getString(var10006[var10007]);
               var10006 = var2.getString(XS[14]);
            } catch (SQLException var4) {
               var10000 = var4;
               boolean var20 = false;
               break;
            }

            ResultSet var24 = var2;

            try {
               var10002.<init>(var10003, var10004, var22, var10006, var24.getString(XS[15]), var2.getString(XS[16]));
               var13.add(var19);
            } catch (SQLException var3) {
               var10000 = var3;
               boolean var21 = false;
               break;
            }
         }
      }

      SQLException var10 = var10000;
      Dq.GE();
      var10.printStackTrace();
      return new ArrayList();
   }
}
