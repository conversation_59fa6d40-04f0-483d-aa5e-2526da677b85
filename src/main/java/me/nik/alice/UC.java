package me.nik.alice;

import java.util.UUID;
import java.util.concurrent.ExecutorService;

import me.nik.alice.checks.CheckType;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;

public class UC {
   private boolean hW;
   private RH dx;
   private final Em dx;
   private final GK Aa;
   private final uq dx = new uq(this);
   private static String[] zP;
   private final Sk dx;
   private final ExecutorService og;
   private final MN dx;
   private UUID dx;
   private final qn dx;
   private final ES dx;
   private long tk;
   private String Ch;
   private long cO;
   private Player player;
   private final Fc dx = new Fc(this);
   private final Qk dx;
   private CheckType[] Aa;

   public Player getPlayer() {
      Player var10000 = this.player;

      try {
         var10000.toString();
      } catch (Exception var1) {
         this.player = Bukkit.getPlayer(this.dx());
      }

      return this.player;
   }

   public UUID dx() {
      UUID var10000 = this.dx;

      try {
         var10000.hashCode();
      } catch (Exception var1) {
         this.dx = this.player.getUniqueId();
      }

      return this.dx;
   }

   public void dC(String var1) {
      this.Ch = var1;
   }

   public void og(Event var1) {
      if (this.AC() > 1000L) {
         PB var2 = new PB();
         this.dx.AC(var1);
         this.dx.AC(var1);
         this.dx.AC(var1);
         this.dx.Qt();
         Alice.dx().dx().AC(var2.gz());
         if (!this.zP()) {
            var2 = new PB();
            this.Aa.Aa(var1);
            Alice.dx().dx().Aa(var2.gz());
         }
      }
   }

   public void sB() {
      if (!Alice.dx().dx().dx(this.dx)) {
         this.nZ();
         this.Aa.DB();
      }
   }

   public qn dx() {
      return this.dx;
   }

   public boolean x() {
      return this.hW;
   }

   public boolean hW() {
      return this.AC() <= 6000L;
   }

   public void dC(long var1) {
      this.tk = var1;
   }

   public void nZ() {
      this.cO = System.currentTimeMillis();
   }

   public RH dx() {
      return this.dx;
   }

   public uq dx() {
      return this.dx;
   }

   public void Eu() {
      this.hW = this.getPlayer().hasPermission(ZM.Aa.h5());
   }

   public Qk dx() {
      return this.dx;
   }

   public Fc dx() {
      return this.dx;
   }

   private boolean zP() {
      return this.hW()
         || Alice.dx().dx().Aa()
         || !Alice.dx().dx().dx()
         || Ku.QJ() <= me.nik.alice.UN.dx.UH.dx()
         || Ku.Vm() < me.nik.alice.UN.dx.sX.Aa()
         || Alice.dx().dx().og(this.getPlayer())
         || this.og() <= 0L
         || Alice.getAPI().isBypassing(this.getPlayer());
   }

   public UC(Player var1) {
      this.dx = new Sk(this);
      this.dx = new ES(this);
      this.dx = new qn(this);
      this.dx = new MN(this);
      this.dx = new Qk(this);
      this.Aa = new GK(this);
      this.Ch = zP[0];
      this.dx = RH.Zm;
      this.og = Alice.dx().dx().Aa();
      this.dx = new Em(this);
      Player var10000 = var1;

      try {
         var10000.getClass();
      } catch (Exception var2) {
         return;
      }

      this.player = var1;
      this.dx = var1.getUniqueId();
      this.dx = kG.dx(var1);
      this.Eu();
      this.sB();
   }

   private static void Aa(Player var0, String var1) {
      var0.kickPlayer(Dq.sB(var1));
   }

   public GK dx() {
      return this.Aa;
   }

   public Sk dx() {
      return this.dx;
   }

   public MN dx() {
      return this.dx;
   }

   public String hW() {
      return this.Ch;
   }

   public long AC() {
      return l.og(this.cO);
   }

   public void dx(CheckType[] var1) {
      this.Aa = var1;
   }

   public CheckType[] dx() {
      return this.Aa;
   }

   private static void _X/* $VF was: 6X*/() {
      zP = new String[]{"Unknown"};
   }

   public void u() {
      this.dx.R2();
   }

   public ExecutorService og() {
      return this.og;
   }

   public void sB(String var1) {
      Player var2;
      Player var10000 = var2 = this.getPlayer();

      try {
         var10000.toString();
      } catch (Exception var3) {
         return;
      }

      wf.dx(UC::Aa);
   }

   public ES dx() {
      return this.dx;
   }

   public long og() {
      return l.og(this.tk);
   }

   public Em dx() {
      return this.dx;
   }

   static {
      6X();
   }

   public void og(DH var1) {
      if (this.AC() > 1000L) {
         PB var2 = new PB();
         this.dx.AC(var1);
         this.dx.AC(var1);
         this.dx.AC(var1);
         this.dx.AC(var1);
         this.dx.AC(var1);
         this.dx.AC(var1);
         this.dx.AC(var1);
         this.dx.Qt();
         Alice.dx().dx().sB(var2.gz());
         if (!this.zP()) {
            var2 = new PB();
            this.Aa.Aa(var1);
            Alice.dx().dx().og(var2.gz());
         }
      }
   }
}
