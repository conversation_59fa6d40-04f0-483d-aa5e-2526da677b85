package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class UH extends PacketCheck {
   private static String[] qR;
   private int Zh;

   private static void RW() {
      qR = new String[]{"Checks for abnormal climbing speed", "Climb Ticks: ", " delta Y: ", "Climb Ticks: ", " delta Y: "};
   }

   @Override
   public String sB() {
      return qR[0];
   }

   public UH(UC var1) {
      super(var1, CheckType.FASTCLIMB, Category.MOVE, 3.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && this.Aa.dx().sX() >= 40
         && this.Aa.dx().gz() >= 60
         && this.Aa.dx().WB() >= 60
         && !this.Aa.dx().o4()
         && !this.Aa.dx().yV()
         && this.Aa.dx().E() >= 20
         && !this.Aa.dx().jA()
         && this.Aa.dx().P() >= 60
         && this.Aa.dx().R2() >= 60
         && !this.Aa.dx().DB()) {
         ES var9;
         if ((var9 = this.Aa.dx()).u() > 0 && var9.DP() > 0) {
            this.Zh++;
         } else {
            this.Zh = 0;
         }

         double var3 = var9.Ch();
         double var5;
         double var7 = (var5 = (double)var9.dx().og(1500L)) > 0.0 ? var5 * 0.1 : 0.0;
         boolean var10 = this.Zh > 2 && var3 > var7 + 0.5 || this.Zh > 0 && var3 >= var7 + 1.0;
         boolean var2 = this.Zh > 5 && var3 > var7 + 15.0;
         if (var10) {
            this.og(qR[1] + this.Zh + qR[2] + var3);
         }

         if (var2) {
            this.Aa(qR[3] + this.Zh + qR[4] + var3);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.075);
         }
      }
   }

   static {
      RW();
   }
}
