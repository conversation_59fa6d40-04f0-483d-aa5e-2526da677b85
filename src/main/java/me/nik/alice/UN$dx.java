package me.nik.alice;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum UN$dx {
   dx(UN$dx.db[1], Boolean.TRUE, new String[]{UN$dx.db[2]}),
   Aa(UN$dx.db[4], Boolean.FALSE, new String[]{UN$dx.db[5], UN$dx.db[6]}),
   og(UN$dx.db[8], UN$dx.db[9], new String[]{UN$dx.db[10]}),
   AC(UN$dx.db[12], UN$dx.db[13], new String[]{UN$dx.db[14]}),
   sB(UN$dx.db[16], UN$dx.db[17], new String[]{UN$dx.db[18]}),
   dC(UN$dx.db[20], Boolean.FALSE, new String[]{UN$dx.db[21]}),
   Zh(UN$dx.db[23], UN$dx.db[24], new String[]{UN$dx.db[25]}),
   h5(UN$dx.db[27], Boolean.TRUE, new String[]{UN$dx.db[28], UN$dx.db[29]}),
   Fh(UN$dx.db[31], Boolean.TRUE, new String[]{UN$dx.db[32], UN$dx.db[33]}),
   b(UN$dx.db[35], Boolean.TRUE, new String[]{UN$dx.db[36], UN$dx.db[37]}),
   VL(UN$dx.db[39], UN$dx.db[40], new String[]{UN$dx.db[41]}),
   UH(UN$dx.db[43], 17.0, new String[]{UN$dx.db[44]}),
   yM(UN$dx.db[46], 1000, new String[]{UN$dx.db[47]}),
   sX(UN$dx.db[49], 5000, new String[]{UN$dx.db[50]}),
   WB(UN$dx.db[52], UN$dx.db[53], new String[]{UN$dx.db[54]}),
   Vm(UN$dx.db[56], UN$dx.db[57], new String[]{UN$dx.db[58]}),
   gz(UN$dx.db[60], Boolean.TRUE, new String[]{UN$dx.db[61]}),
   tm(UN$dx.db[63], Boolean.FALSE, new String[]{UN$dx.db[64]}),
   x(UN$dx.db[66], Collections.singletonList(UN$dx.db[67]), true, new String[]{UN$dx.db[68], UN$dx.db[69]}),
   zP(UN$dx.db[71], Boolean.TRUE, new String[]{UN$dx.db[72], UN$dx.db[73], UN$dx.db[74]}),
   hW(UN$dx.db[76], Boolean.FALSE, new String[]{UN$dx.db[77], UN$dx.db[78]}),
   Ch(UN$dx.db[80], Boolean.TRUE, new String[]{UN$dx.db[81], UN$dx.db[82], UN$dx.db[83]}),
   iv(UN$dx.db[85], Boolean.TRUE, new String[]{UN$dx.db[86]}),
   F7(UN$dx.db[88], UN$dx.db[89], new String[]{UN$dx.db[90]}),
   Zp(UN$dx.db[92], 5, new String[]{UN$dx.db[93]}),
   DB(UN$dx.db[95], UN$dx.db[96], new String[]{UN$dx.db[97]}),
   jD(UN$dx.db[99], UN$dx.db[100], new String[]{UN$dx.db[101], UN$dx.db[102], UN$dx.db[103]}),
   Qt(UN$dx.db[105], Boolean.TRUE, new String[]{UN$dx.db[106]}),
   rc(UN$dx.db[108], UN$dx.db[109], new String[]{UN$dx.db[110], UN$dx.db[111]}),
   Zm(UN$dx.db[113], UN$dx.db[114], new String[]{UN$dx.db[115]}),
   jA(UN$dx.db[117], Boolean.FALSE, new String[]{UN$dx.db[118], UN$dx.db[119], UN$dx.db[120]}),
   bI(UN$dx.db[122], Boolean.TRUE, new String[]{UN$dx.db[123]}),
   yV(UN$dx.db[125], 15, new String[]{UN$dx.db[126]}),
   a(UN$dx.db[128], 5, new String[]{UN$dx.db[129]}),
   cO(UN$dx.db[131], UN$dx.db[132], new String[]{UN$dx.db[133]}),
   tk(UN$dx.db[135], UN$dx.db[136], new String[]{UN$dx.db[137]}),
   Eu(UN$dx.db[139], 80, new String[]{UN$dx.db[140], UN$dx.db[141]}),
   nZ(UN$dx.db[143], UN$dx.db[144], new String[]{UN$dx.db[145]}),
   u(UN$dx.db[147], UN$dx.db[148], new String[]{UN$dx.db[149], UN$dx.db[150]}),
   Q(UN$dx.db[152], 7, new String[]{UN$dx.db[153]}),
   QJ(UN$dx.db[155], UN$dx.db[156], new String[]{UN$dx.db[157]}),
   o4(UN$dx.db[159], UN$dx.db[160], new String[0]),
   tr(UN$dx.db[162], 3306, new String[0]),
   pO(UN$dx.db[164], UN$dx.db[165], new String[0]),
   DP(UN$dx.db[167], UN$dx.db[168], new String[0]),
   p6(UN$dx.db[170], UN$dx.db[171], new String[0]),
   R2(UN$dx.db[173], Boolean.FALSE, new String[0]),
   Sh(UN$dx.db[175], UN$dx.db[176], new String[]{UN$dx.db[177], UN$dx.db[178]}),
   pQ(UN$dx.db[180], 50, new String[0]),
   yk(UN$dx.db[182], 30, new String[0]),
   P(UN$dx.db[184], 1800000, new String[0]),
   R(UN$dx.db[186], 30000, new String[0]),
   xx(UN$dx.db[188], 0, new String[0]),
   M3(UN$dx.db[190], UN$dx.db[191], new String[]{UN$dx.db[192], UN$dx.db[193]}),
   E(UN$dx.db[195], Boolean.TRUE, new String[]{UN$dx.db[196], UN$dx.db[197], UN$dx.db[198]}),
   tV(UN$dx.db[200], Boolean.TRUE, new String[]{UN$dx.db[201], UN$dx.db[202]}),
   GE(UN$dx.db[204], Boolean.TRUE, new String[]{UN$dx.db[205], UN$dx.db[206]}),
   tU(UN$dx.db[208], Boolean.TRUE, new String[]{UN$dx.db[209]}),
   Wx(UN$dx.db[211], Boolean.TRUE, new String[]{UN$dx.db[212], UN$dx.db[213]}),
   hB(UN$dx.db[215], Boolean.TRUE, new String[]{UN$dx.db[216], UN$dx.db[217]});

   private final String key;
   private final Object dx;
   private boolean Fh;
   private final String[] og;
   private Object Aa = null;
   private static final UN$dx[] dx;
   private static String[] db;

   private UN$dx(String var3, Object var4, String[] var5) {
      this.key = var3;
      this.dx = var4;
      this.og = var5 != null ? var5 : new String[0];
   }

   private UN$dx(String var3, Object var4, boolean var5, String[] var6) {
      this.key = var3;
      this.dx = var4;
      this.og = var6 != null ? var6 : new String[0];
      this.Fh = var5;
   }

   public boolean dC() {
      this.x();
      return (Boolean)this.Aa;
   }

   public String Fh() {
      return this.key;
   }

   public int Aa() {
      this.x();
      return (int)this.Aa();
   }

   public long Aa() {
      this.x();
      return (long)this.Aa();
   }

   public double dx() {
      this.x();
      return this.Aa();
   }

   public float sB() {
      this.x();
      return (float)this.Aa();
   }

   public String b() {
      this.x();
      return String.valueOf(this.Aa);
   }

   private double Aa() {
      if (this.Aa instanceof Integer) {
         return (double)((Integer)this.Aa).intValue();
      } else if (this.Aa instanceof Short) {
         return (double)((Short)this.Aa).shortValue();
      } else if (this.Aa instanceof Byte) {
         return (double)((Byte)this.Aa).byteValue();
      } else {
         return this.Aa instanceof Float ? (double)((Float)this.Aa).floatValue() : (Double)this.Aa;
      }
   }

   public List Aa() {
      this.x();
      return (List)this.Aa;
   }

   private boolean dx(D8 var1) {
      this.x();
      if (UN.sB() && this.Fh) {
         return false;
      } else {
         var1.get(this.key);

         try {
            return false;
         } catch (Exception var4) {
            List var2 = (List)Stream.of(this.og).collect(Collectors.toList());
            Object var10000 = this.dx;

            try {
               var10000.getClass();
            } catch (Exception var3) {
               var1.dx((String[])var2.toArray(new String[0]));
               return true;
            }

            var1.dx(this.key, this.dx, (String[])var2.toArray(new String[0]));
            return true;
         }
      }
   }

   public void reset() {
      this.Aa = null;
   }

   public boolean Zh() {
      Object var10000 = this.dx;

      try {
         var10000.equals(null);
         return false;
      } catch (Exception var1) {
         return true;
      }
   }

   private void x() {
      Object var10000 = this.Aa;

      try {
         var10000.equals(null);
      } catch (Exception var1) {
         this.Aa = Alice.dx().dx().get(this.key);
      }
   }

   static boolean dx(UN$dx var0, D8 var1) {
      return var0.dx(var1);
   }

   static {
      Ie();
      dx = new UN$dx[]{
         dx,
         Aa,
         og,
         AC,
         sB,
         dC,
         Zh,
         h5,
         Fh,
         b,
         VL,
         UH,
         yM,
         sX,
         WB,
         Vm,
         gz,
         tm,
         x,
         zP,
         hW,
         Ch,
         iv,
         F7,
         Zp,
         DB,
         jD,
         Qt,
         rc,
         Zm,
         jA,
         bI,
         yV,
         a,
         cO,
         tk,
         Eu,
         nZ,
         u,
         Q,
         QJ,
         o4,
         tr,
         pO,
         DP,
         p6,
         R2,
         Sh,
         pQ,
         yk,
         P,
         R,
         xx,
         M3,
         E,
         tV,
         GE,
         tU,
         Wx,
         hB
      };
   }

   private static void Ie() {
      db = new String[]{
         "CHECK_FOR_UPDATES",
         "check_for_updates",
         "Should we check for updates on startup?",
         "TEST_MODE",
         "test_mode",
         "Should we enable the test mode?",
         "If this mode is enabled the punish command won't work",
         "THEME",
         "theme",
         "default",
         "The theme that alice is going to use",
         "SERVER_NAME",
         "server_name",
         "Server",
         "The server name that will be shown in Player Logs and Discord Messages",
         "DISCORD",
         "discord",
         "",
         "Discord Sync Settings",
         "DISCORD_ENABLED",
         "discord.enabled",
         "Would you like to Sync Alice to your Discord Server?",
         "DISCORD_WEBHOOK",
         "discord.webhook_url",
         "",
         "Insert the Channel Webhook URL",
         "DISCORD_VIOLATIONS",
         "discord.violations",
         "Would you like Alice to send Discord Messages",
         "When a player receives a Violation?",
         "DISCORD_PUNISHMENTS",
         "discord.punishments",
         "Would you like Alice to send Discord Messages",
         "When a player gets Punished?",
         "DISCORD_PUNISHWAVE",
         "discord.punishwave",
         "Would you like Alice to send Discord Messages",
         "When players get punished from the Punish Wave?",
         "TPS_PROTECTION",
         "tps_protection",
         "",
         "TPS Protection Settings",
         "TPS_PROTECTION_MIN_TPS",
         "tps_protection.min_tps",
         "If the Server's TPS reach or go below this value Alice will stop checking players",
         "TPS_PROTECTION_LAG_THRESHOLD",
         "tps_protection.lag_threshold",
         "If the server does not respond for this amount of Milliseconds, Alice will stop checking players",
         "TPS_PROTECTION_RECOVER_MILLIS",
         "tps_protection.recover_millis",
         "How long should we wait for the server to recover once a lag spike occurs? (In milliseconds)",
         "CLIENT_SETTINGS",
         "client_settings",
         "",
         "Client Settings",
         "CLIENT_SETTINGS_ALERT",
         "client_settings.alert",
         "",
         "Client Alert Settings",
         "CLIENT_SETTINGS_ALERT_ENABLED",
         "client_settings.alert.enabled",
         "Should we send an Alert when a player joins specifying his Client Brand?",
         "CLIENT_SETTINGS_ALERT_NON_VANILLA_ONLY",
         "client_settings.alert.non_vanilla_only",
         "Should Alice only send an Alert when a player joins with a Non Vanilla client?",
         "CLIENT_SETTINGS_BANNED_CLIENTS",
         "client_settings.banned_clients",
         "someclientbrand",
         "Client Brands that contain the following, Will be prevented from joining the server",
         "NOTE: At least one value has to exist, If you want to disable this simply put something random",
         "GHOST_MODE",
         "ghost_mode",
         "Should we enable the Alice Ghost Mode?",
         "It is highly recommended to keep this option to true",
         "If Ghost Mode is enabled, Alice will not attempt to Setback players for checks like Speed or Fly while also making certain checks and Alice herself perform much better",
         "ALLOW_EXPERIMENTAL",
         "allow_experimental",
         "Should experimental checks add violations?",
         "If this setting is disabled you will still see notifications but no violations will be added",
         "DISABLE_BYPASS_PERMISSION",
         "disable_bypass_permission",
         "Should we disable the bypass permission?",
         "If enabled Alice will check all players including operators",
         "Disable this for some perfomance gain",
         "TOGGLE_ALERTS_ON_JOIN",
         "toggle_alerts_on_join",
         "Should we enable alerts for admins when they join?",
         "CHECK_SETTINGS",
         "check_settings",
         "",
         "Check Settings",
         "CHECK_SETTINGS_VIOLATION_RESET_INTERVAL",
         "check_settings.violation_reset_interval",
         "How often should Alice clear the player violations? (In minutes)",
         "PUNISH_COMMAND",
         "punish_command",
         "",
         "Punish Command Settings",
         "PUNISH_ANIMATION",
         "punish_command.animation",
         "RANDOM",
         "What Punish Animation Should Alice display before executing the below command?",
         "Animations: NONE, FLAME, BURN, ERROR or RANDOM to let it choose a random animation each time",
         "Feel free to create your own Punish Animations by using the Alice API",
         "PUNISH_COMMAND_BROADCAST",
         "punish_command.broadcast",
         "Should we broadcast in chat when a player gets punished by Alice?",
         "PUNISH_COMMAND_COMMAND",
         "punish_command.command",
         "alice kick %player% %prefix% %reason%",
         "What command should Alice execute once the Punish Animation ends? (%nl% for new line)",
         "Placeholders: %prefix% -> (Alice Prefix), %player% -> (The player that got punished), %reason% -> (The reason provided by the Alice punish command)",
         "PUNISHWAVE",
         "punishwave",
         "",
         "Punish Wave Settings",
         "PUNISHWAVE_ENABLED",
         "punishwave.enabled",
         "Should we enable the Punish Wave?",
         "If the Punish Wave is enabled, Alice will punish all Cheaters at once",
         "If a player gets punished using the /Alice punish command while the Punish Wave is enabled, He will be added to the Queue and get Punished once the Punish Wave starts",
         "PUNISHWAVE_BROADCAST",
         "punishwave.broadcast",
         "Should Alice Broadcast this event?",
         "PUNISHWAVE_TIME",
         "punishwave.time",
         "How often should the Punish Wave get executed? (In minutes)",
         "PUNISHWAVE_COUNTDOWN",
         "punishwave.countdown",
         "The countdown before executing the Punish Wave (-1 to disable)",
         "PUNISHWAVE_REASON",
         "punishwave.reason",
         "Unfair Advantage",
         "What should we Punish the player for? (%nl% for new line)",
         "COMPATIBILITY",
         "compatibility",
         "",
         "Compatibility Settings",
         "COMPATIBILITY_ARTIFICIAL_VELOCITY_TICKS",
         "compatibility.artificial_velocity_ticks",
         "Alice can detect when a plugin adds artificial velocity to the player (IE JumpPad plugins)",
         "For how long should we exempt the movement checks once that velocity is applied? (In ticks)",
         "LOGS",
         "logs",
         "",
         "Log Settings",
         "LOGS_TYPE",
         "logs.type",
         "YAML",
         "What type of Database should Alice use for logging?",
         "Supported types: YAML, MYSQL, SQLITE",
         "LOGS_CLEAR_DAYS",
         "logs.clear_days",
         "Logs older than this value of Days will be cleared by Alice",
         "MYSQL",
         "logs.mysql",
         "",
         "MySQL Settings (Requires MySQL Type)",
         "MYSQL_HOST",
         "logs.mysql.host",
         "localhost",
         "MYSQL_PORT",
         "logs.mysql.port",
         "MYSQL_DATABASE",
         "logs.mysql.database",
         "database",
         "MYSQL_USER",
         "logs.mysql.user",
         "root",
         "MYSQL_PASSWORD",
         "logs.mysql.password",
         "",
         "MYSQL_USESSL",
         "logs.mysql.use_ssl",
         "MYSQL_POOL_SETTINGS",
         "logs.mysql.pool_settings",
         "",
         "MySQL Connection Pool Settings",
         "Do not change unless you know what you're doing",
         "MYSQL_MAXIMUM_POOL_SIZE",
         "logs.mysql.pool_settings.maximum_pool_size",
         "MYSQL_MINIMUM_IDLE",
         "logs.mysql.pool_settings.minimum_idle",
         "MYSQL_MAXIMUM_LIFETIME",
         "logs.mysql.pool_settings.maximum_lifetime",
         "MYSQL_CONNECTION_TIMEOUT",
         "logs.mysql.pool_settings.connection_timeout",
         "MYSQL_LEAK_DETECTION_THRESHOLD",
         "logs.mysql.pool_settings.leak_detection_threshold",
         "ADVANCED",
         "advanced",
         "",
         "Advanced Settings",
         "Do not touch unless you know what you're doing",
         "ACCOUNT_CINEMATIC",
         "advanced.account_cinematic",
         "Should we account for cinematic camera?",
         "It is highly recommended to not enable this option since this will make the aim checks weaker",
         "In a real combat scenerio, none of the opponents are going to fight using cinematic.",
         "GHOSTBLOCKS_TELEPORT",
         "advanced.ghostblocks_teleport",
         "Should Alice teleport players on ghost blocks?",
         "It is highly recommended to not disable this option since it can prevent potential false positives",
         "TIMEOUT_TELEPORT",
         "advanced.timeout_teleport",
         "Should Alice kick players that are stuck in a repeated teleporting state due to them lagging insanely hard due to the chunk being unloaded client side?",
         "Please do not disable this option since this is impossible to account for",
         "TIMEOUT_COMMUNICATION",
         "advanced.timeout_communication",
         "Should Alice kick players that do not respond to transactions or keepalives for a long time?",
         "DISABLE_SELF_SHOOTING",
         "advanced.disable_self_shooting",
         "Prevents players from shooting themselves by using arrows",
         "While this prevents potential issues, It makes players unable to Bow Boost Themselves, Should we re-enable that option? (Not Recommended)",
         "DISABLE_VEHICLE_DISCONNECTING",
         "advanced.disable_vehicle_disconnecting",
         "Ejects players that try to disconnect while riding a vehicle",
         "This fixes a rare bug with the newest versions of minecraft which causes the player's dead status to always be true, Please do not disable this!"
      };
   }
}
