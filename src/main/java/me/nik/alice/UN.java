package me.nik.alice;

import java.io.File;

public class UN {
   private final Alice plugin;
   private static final String[] Aa = new String[]{UN.Ta[1], UN.Ta[2], UN.Ta[3], UN.Ta[4], UN.Ta[5], UN.Ta[6], UN.Ta[7], UN.Ta[8], UN.Ta[9]};
   private static String[] Ta;
   private D8 dx;
   private static boolean exists;

   public void tm() {
      File var1;
      exists = (var1 = new File(this.plugin.getDataFolder(), Ta[0])).exists();
      boolean var2;
      boolean var3 = (boolean)(var2 = !var1.exists());
      this.dx = D8.dx(this.plugin, var1);
      if (var2 && dC.og()) {
         this.dx.dx(Aa);
      }

      me.nik.alice.UN.dx[] var6;
      var2 = (var6 = me.nik.alice.UN.dx.values()).length;

      for (int var4 = 0; var4 < var2; var4++) {
         me.nik.alice.UN.dx var5;
         (var5 = var6[var4]).reset();
         var3 |= me.nik.alice.UN.dx.dx(var5, this.dx);
      }

      if (var3) {
         this.dx.hW();
      }
   }

   static boolean sB() {
      return exists;
   }

   public void reset() {
      boolean var5 = FSCt;
      me.nik.alice.UN.dx[] var1;
      int var2 = (var1 = me.nik.alice.UN.dx.values()).length;
      int var3 = 0;

      while (var3 < var2) {
         var1[var3].reset();
         var3++;
         if (var5) {
            throw null;
         }
      }
   }

   static {
      IV();
   }

   public UN(Alice var1) {
      this.plugin = var1;
   }

   public D8 og() {
      return this.dx;
   }

   private static void IV() {
      Ta = new String[]{
         "config.yml",
         "+----------------------------------------------------------------------------------------------+",
         "|                                                                                              |",
         "|                                              Alice                                           |",
         "|                                                                                              |",
         "|                               Discord: https://discord.gg/m7j2Y9H                            |",
         "|                                                                                              |",
         "|                                           Author: Nik                                        |",
         "|                                                                                              |",
         "+----------------------------------------------------------------------------------------------+"
      };
   }
}
