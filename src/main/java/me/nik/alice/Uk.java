package me.nik.alice;

import java.io.BufferedReader;
import java.io.BufferedWriter;
import java.io.File;
import java.io.FileReader;
import java.io.FileWriter;
import java.io.IOException;
import java.io.PrintWriter;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

public class Uk extends TQ {
   private static String[] qZ;

   static {
      xJ();
   }

   private static void xJ() {
      qZ = new String[]{
         "data.yml",
         "data.yml",
         "Took more than 5 seconds to load the player logs!",
         "data.yml",
         "Took more than 5 seconds to load the player logs!",
         ",",
         ",",
         "data.yml",
         "data_temp.yml",
         "dd.MM.yyyy HH:mm",
         ","
      };
   }

   @Override
   public void dC() {
      wf.Aa(this::yV);
   }

   private static List dx(File param0) {
      // $VF: Couldn't be decompiled
      // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
      // java.lang.RuntimeException: parsing failure!
      //   at org.jetbrains.java.decompiler.modules.decompiler.decompose.DomHelper.parseGraph(DomHelper.java:211)
      //   at org.jetbrains.java.decompiler.main.rels.MethodProcessor.codeToJava(MethodProcessor.java:166)
      //
      // Bytecode:
      // 00: new java/util/ArrayList
      // 03: dup
      // 04: invokespecial java/util/ArrayList.<init> ()V
      // 07: astore 1
      // 08: goto 0b
      // 0b: new java/io/BufferedReader
      // 0e: dup
      // 0f: new java/io/FileReader
      // 12: dup
      // 13: aload 0
      // 14: invokespecial java/io/FileReader.<init> (Ljava/io/File;)V
      // 17: invokespecial java/io/BufferedReader.<init> (Ljava/io/Reader;)V
      // 1a: astore 0
      // 1b: goto 1e
      // 1e: aload 0
      // 1f: invokevirtual java/io/BufferedReader.readLine ()Ljava/lang/String;
      // 22: dup
      // 23: astore 2
      // 24: goto 27
      // 27: invokevirtual java/lang/Object.hashCode ()I
      // 2a: pop
      // 2b: goto 2e
      // 2e: goto 35
      // 31: pop
      // 32: goto 7b
      // 35: aload 2
      // 36: getstatic me/nik/alice/Uk.qZ [Ljava/lang/String;
      // 39: bipush 6
      // 3b: aaload
      // 3c: goto 3f
      // 3f: invokevirtual java/lang/String.split (Ljava/lang/String;)[Ljava/lang/String;
      // 42: goto 45
      // 45: astore 3
      // 46: goto 49
      // 49: aload 1
      // 4a: goto 4d
      // 4d: new me/nik/alice/hL
      // 50: dup
      // 51: aload 3
      // 52: bipush 0
      // 53: aaload
      // 54: goto 57
      // 57: aload 3
      // 58: bipush 1
      // 59: aaload
      // 5a: aload 3
      // 5b: bipush 2
      // 5c: aaload
      // 5d: aload 3
      // 5e: bipush 3
      // 5f: aaload
      // 60: aload 3
      // 61: bipush 4
      // 62: aaload
      // 63: aload 3
      // 64: bipush 5
      // 65: aaload
      // 66: goto 69
      // 69: invokespecial me/nik/alice/hL.<init> (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
      // 6c: goto 6f
      // 6f: invokeinterface java/util/List.add (Ljava/lang/Object;)Z 2
      // 74: pop
      // 75: goto 78
      // 78: goto 1e
      // 7b: aload 0
      // 7c: invokevirtual java/io/BufferedReader.close ()V
      // 7f: goto 82
      // 82: goto aa
      // 85: astore 2
      // 86: goto 89
      // 89: goto 8c
      // 8c: aload 0
      // 8d: invokevirtual java/io/BufferedReader.close ()V
      // 90: goto 93
      // 93: goto a2
      // 96: astore 3
      // 97: goto 9a
      // 9a: aload 2
      // 9b: aload 3
      // 9c: invokevirtual java/lang/Throwable.addSuppressed (Ljava/lang/Throwable;)V
      // 9f: goto a2
      // a2: aload 2
      // a3: athrow
      // a4: invokevirtual java/io/IOException.printStackTrace ()V
      // a7: goto aa
      // aa: aload 1
      // ab: areturn
   }

   public Uk(Alice var1) {
      super(var1);
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   @Override
   public void dx(hL var1) {
      IOException var10000;
      label71: {
         try {
            var15 = new File(this.plugin.getDataFolder(), qZ[0]);
         } catch (IOException var11) {
            var10000 = var11;
            boolean var10001 = false;
            break label71;
         }

         File var2;
         label72: {
            try {
               var2 = var15;
               if (var15.exists()) {
                  break label72;
               }
            } catch (IOException var10) {
               var10000 = var10;
               boolean var19 = false;
               break label71;
            }

            File var16 = var2;

            try {
               var16.createNewFile();
            } catch (IOException var9) {
               var10000 = var9;
               boolean var20 = false;
               break label71;
            }
         }

         try {
            var12 = var1.toString();
         } catch (IOException var8) {
            var10000 = var8;
            boolean var21 = false;
            break label71;
         }

         FileWriter var17 = new FileWriter;
         FileWriter var22 = var17;
         File var10002 = var2;
         boolean var10003 = true;

         try {
            var22.<init>(var10002, var10003);
            var13 = var17;
         } catch (IOException var7) {
            var10000 = var7;
            boolean var23 = false;
            break label71;
         }

         PrintWriter var18 = new PrintWriter;
         PrintWriter var24 = var18;
         FileWriter var29 = var13;

         try {
            var24.<init>(var29);
            var14 = var18;
         } catch (IOException var6) {
            var10000 = var6;
            boolean var25 = false;
            break label71;
         }

         try {
            var18.println(var12);
         } catch (IOException var5) {
            var10000 = var5;
            boolean var26 = false;
            break label71;
         }

         try {
            var14.flush();
         } catch (IOException var4) {
            var10000 = var4;
            boolean var27 = false;
            break label71;
         }

         try {
            var14.close();
            return;
         } catch (IOException var3) {
            var10000 = var3;
            boolean var28 = false;
         }
      }

      var10000.printStackTrace();
   }

   @Override
   public List dx(String var1) {
      File var2;
      if (!(var2 = new File(this.plugin.getDataFolder(), qZ[3])).exists()) {
         return new ArrayList();
      } else {
         CompletableFuture var4 = CompletableFuture.supplyAsync(Uk::dx);

         try {
            return (List)var4.get(5L, TimeUnit.SECONDS);
         } catch (ExecutionException | TimeoutException | InterruptedException var3) {
            this.plugin.getLogger().severe(qZ[4]);
            var3.printStackTrace();
            return new ArrayList();
         }
      }
   }

   private static List dx(File param0, String param1) {
      // $VF: Couldn't be decompiled
      // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
      // java.lang.RuntimeException: parsing failure!
      //   at org.jetbrains.java.decompiler.modules.decompiler.decompose.DomHelper.parseGraph(DomHelper.java:211)
      //   at org.jetbrains.java.decompiler.main.rels.MethodProcessor.codeToJava(MethodProcessor.java:166)
      //
      // Bytecode:
      // 00: new java/util/ArrayList
      // 03: dup
      // 04: invokespecial java/util/ArrayList.<init> ()V
      // 07: astore 2
      // 08: new java/io/BufferedReader
      // 0b: dup
      // 0c: goto 0f
      // 0f: new java/io/FileReader
      // 12: dup
      // 13: aload 0
      // 14: invokespecial java/io/FileReader.<init> (Ljava/io/File;)V
      // 17: goto 1a
      // 1a: invokespecial java/io/BufferedReader.<init> (Ljava/io/Reader;)V
      // 1d: astore 0
      // 1e: goto 21
      // 21: aload 0
      // 22: goto 25
      // 25: invokevirtual java/io/BufferedReader.readLine ()Ljava/lang/String;
      // 28: dup
      // 29: goto 2c
      // 2c: astore 3
      // 2d: goto 30
      // 30: aconst_null
      // 31: invokevirtual java/lang/Object.equals (Ljava/lang/Object;)Z
      // 34: pop
      // 35: goto 38
      // 38: goto 3f
      // 3b: pop
      // 3c: goto bd
      // 3f: aload 3
      // 40: getstatic me/nik/alice/Uk.qZ [Ljava/lang/String;
      // 43: bipush 5
      // 44: goto 47
      // 47: aaload
      // 48: goto 4b
      // 4b: invokevirtual java/lang/String.split (Ljava/lang/String;)[Ljava/lang/String;
      // 4e: goto 51
      // 51: dup
      // 52: astore 4
      // 54: bipush 1
      // 55: goto 58
      // 58: aaload
      // 59: goto 5c
      // 5c: aload 1
      // 5d: invokevirtual java/lang/String.toUpperCase ()Ljava/lang/String;
      // 60: invokevirtual java/lang/Object.hashCode ()I
      // 63: invokestatic java/lang/Integer.valueOf (I)Ljava/lang/Integer;
      // 66: swap
      // 67: invokevirtual java/lang/String.toUpperCase ()Ljava/lang/String;
      // 6a: invokevirtual java/lang/String.hashCode ()I
      // 6d: invokestatic java/lang/Integer.valueOf (I)Ljava/lang/Integer;
      // 70: invokevirtual java/lang/Integer.equals (Ljava/lang/Object;)Z
      // 73: goto 76
      // 76: ifeq 21
      // 79: goto 7c
      // 7c: aload 2
      // 7d: new me/nik/alice/hL
      // 80: dup
      // 81: aload 4
      // 83: bipush 0
      // 84: goto 87
      // 87: aaload
      // 88: aload 4
      // 8a: goto 8d
      // 8d: bipush 1
      // 8e: aaload
      // 8f: goto 92
      // 92: aload 4
      // 94: bipush 2
      // 95: aaload
      // 96: aload 4
      // 98: bipush 3
      // 99: aaload
      // 9a: aload 4
      // 9c: bipush 4
      // 9d: aaload
      // 9e: goto a1
      // a1: aload 4
      // a3: goto a6
      // a6: bipush 5
      // a7: aaload
      // a8: goto ab
      // ab: invokespecial me/nik/alice/hL.<init> (Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;Ljava/lang/String;)V
      // ae: invokeinterface java/util/List.add (Ljava/lang/Object;)Z 2
      // b3: pop
      // b4: goto b7
      // b7: goto ba
      // ba: goto 21
      // bd: aload 0
      // be: invokevirtual java/io/BufferedReader.close ()V
      // c1: goto c4
      // c4: goto f4
      // c7: astore 3
      // c8: goto cb
      // cb: goto ce
      // ce: aload 0
      // cf: invokevirtual java/io/BufferedReader.close ()V
      // d2: goto d5
      // d5: goto d8
      // d8: goto db
      // db: goto ec
      // de: astore 4
      // e0: goto e3
      // e3: aload 3
      // e4: aload 4
      // e6: invokevirtual java/lang/Throwable.addSuppressed (Ljava/lang/Throwable;)V
      // e9: goto ec
      // ec: aload 3
      // ed: athrow
      // ee: invokevirtual java/io/IOException.printStackTrace ()V
      // f1: goto f4
      // f4: aload 2
      // f5: areturn
   }

   @Override
   public void gz() {
   }

   @Override
   public List h5() {
      File var1;
      if (!(var1 = new File(this.plugin.getDataFolder(), qZ[1])).exists()) {
         return new ArrayList();
      } else {
         CompletableFuture var3 = CompletableFuture.supplyAsync(Uk::dx);

         Object var10000;
         try {
            var10000 = var3.get(5L, TimeUnit.SECONDS);
         } catch (ExecutionException | TimeoutException | InterruptedException var2) {
            this.plugin.getLogger().severe(qZ[2]);
            var2.printStackTrace();
            return new ArrayList();
         }

         return (List)var10000;
      }
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private void yV() {
      boolean var9 = MTt0;
      Alice var10000 = this.plugin;

      label176: {
         label177: {
            try {
               if (var10000.getDataFolder().exists()) {
                  break label177;
               }
            } catch (IOException | ParseException var31) {
               var32 = var31;
               boolean var10001 = false;
               break label176;
            }

            try {
               this.plugin.getDataFolder().mkdir();
            } catch (IOException | ParseException var30) {
               var32 = var30;
               boolean var44 = false;
               break label176;
            }
         }

         File var1;
         label163: {
            try {
               if ((var1 = new File(this.plugin.getDataFolder(), qZ[7])).exists()) {
                  break label163;
               }
            } catch (IOException | ParseException var29) {
               var32 = var29;
               boolean var45 = false;
               break label176;
            }

            try {
               var1.createNewFile();
            } catch (IOException | ParseException var28) {
               var32 = var28;
               boolean var46 = false;
               break label176;
            }
         }

         File var33 = new File;
         File var47 = var33;

         File var2;
         try {
            var47.<init>(this.plugin.getDataFolder(), qZ[8]);
            var2 = var33;
            var33.createNewFile();
         } catch (IOException | ParseException var27) {
            var32 = var27;
            boolean var48 = false;
            break label176;
         }

         BufferedReader var34 = new BufferedReader;
         BufferedReader var49 = var34;

         try {
            var49.<init>(new FileReader(var1));
         } catch (IOException | ParseException var26) {
            var32 = var26;
            boolean var50 = false;
            break label176;
         }

         BufferedReader var3 = var34;

         FileWriter var10002;
         try {
            var35 = new BufferedWriter;
            var52 = var35;
            var10002 = new FileWriter(var2);
         } catch (IOException | ParseException var25) {
            var32 = var25;
            boolean var51 = false;
            break label176;
         }

         BufferedWriter var4;
         try {
            var52.<init>(var10002);
            var4 = var35;
         } catch (IOException | ParseException var24) {
            var32 = var24;
            boolean var53 = false;
            break label176;
         }

         SimpleDateFormat var5;
         try {
            var5 = new SimpleDateFormat(qZ[9]);
         } catch (IOException | ParseException var23) {
            var32 = var23;
            boolean var54 = false;
            break label176;
         }

         Date var36 = new Date;
         Date var55 = var36;

         Date var6;
         try {
            var55.<init>(System.currentTimeMillis());
            var6 = var36;
         } catch (IOException | ParseException var22) {
            var32 = var22;
            boolean var56 = false;
            break label176;
         }

         while (true) {
            String var7;
            label179: {
               try {
                  String var37 = var7 = var3.readLine();

                  try {
                     var37.hashCode();
                     break label179;
                  } catch (Exception var20) {
                  }
               } catch (IOException | ParseException var21) {
                  var32 = var21;
                  boolean var57 = false;
                  break;
               }

               BufferedWriter var38 = var4;

               try {
                  var38.close();
               } catch (IOException | ParseException var13) {
                  var32 = var13;
                  boolean var58 = false;
                  break;
               }

               try {
                  var3.close();
               } catch (IOException | ParseException var12) {
                  var32 = var12;
                  boolean var59 = false;
                  break;
               }

               try {
                  var1.delete();
               } catch (IOException | ParseException var11) {
                  var32 = var11;
                  boolean var60 = false;
                  break;
               }

               try {
                  var2.renameTo(var1);
                  return;
               } catch (IOException | ParseException var10) {
                  var32 = var10;
                  boolean var61 = false;
                  break;
               }
            }

            String var39 = var7;
            String[] var62 = qZ;
            byte var70 = 10;

            String[] var8;
            try {
               var8 = var39.split(var62[var70]);
            } catch (IOException | ParseException var19) {
               var32 = var19;
               boolean var63 = false;
               break;
            }

            try {
               var40 = var5.parse(var8[5]);
            } catch (IOException | ParseException var18) {
               var32 = var18;
               boolean var64 = false;
               break;
            }

            try {
               var41 = var40.getTime() - var6.getTime();
            } catch (IOException | ParseException var17) {
               var32 = var17;
               boolean var65 = false;
               break;
            }

            try {
               var42 = Math.abs(var41);
            } catch (IOException | ParseException var16) {
               var32 = var16;
               boolean var66 = false;
               break;
            }

            if (var42 <= this.jD) {
               try {
                  var43 = var4;
                  var68 = new StringBuilder();
               } catch (IOException | ParseException var15) {
                  var32 = var15;
                  boolean var67 = false;
                  break;
               }

               String var71 = var7;

               try {
                  var43.write(var68.append(var71).append(System.lineSeparator()).toString());
               } catch (IOException | ParseException var14) {
                  var32 = var14;
                  boolean var69 = false;
                  break;
               }

               if (var9) {
                  throw null;
               }
            }
         }
      }

      var32.printStackTrace();
   }
}
