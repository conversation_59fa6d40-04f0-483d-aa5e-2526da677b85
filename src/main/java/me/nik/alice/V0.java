package me.nik.alice;

import java.util.LinkedList;

public final class V0 extends LinkedList {
   private final boolean VL;
   private final int Zm;

   public int AC() {
      return this.Zm;
   }

   public boolean h5() {
      return super.size() >= this.Zm;
   }

   public V0(int var1, boolean var2) {
      this.Zm = var1;
      this.VL = var2;
   }

   public V0(int var1) {
      this.Zm = var1;
      this.VL = false;
   }

   public boolean add(Object var1) {
      if (this.h5()) {
         if (this.VL) {
            super.removeFirst();
         } else {
            super.clear();
         }
      }

      return super.add(var1);
   }
}
