package me.nik.alice;

public enum CheckType {
   AIM(CheckType.r7[1]),
   CLICKING(CheckType.r7[3]),
   PACKET(CheckType.r7[5]),
   FLY(CheckType.r7[7]),
   KILLAURA(CheckType.r7[9]),
   SCAFFOLD(CheckType.r7[11]),
   SPEED(CheckType.r7[13]),
   MOTION(CheckType.r7[15]),
   NOFALL(CheckType.r7[17]),
   b(CheckType.r7[19]),
   VEHICLE(CheckType.r7[21]),
   ELYTRA(CheckType.r7[23]),
   TIMER(CheckType.r7[25]),
   OMNISPRINT(CheckType.r7[27]),
   NOSLOW(CheckType.r7[29]),
   REACH(CheckType.r7[31]),
   VELOCITY(CheckType.r7[33]),
   INVENTORY(CheckType.r7[35]),
   INTERACT(CheckType.r7[37]),
   FASTCLIMB(CheckType.r7[39]),
   hW(CheckType.r7[41]),
   Ch(CheckType.r7[43]),
   iv(CheckType.r7[45]);

   private final String checkName;
   private static final CheckType[] dx;
   private static String[] r7;

   private CheckType(String var3) {
      this.checkName = var3;
   }

   public String AC() {
      return this.checkName;
   }

   static {
      7s();
      AIM = new CheckType[]{AIM, CLICKING, PACKET, FLY, KILLAURA, SCAFFOLD, SPEED, MOTION, NOFALL, b, VEHICLE, ELYTRA, TIMER, OMNISPRINT, NOSLOW, REACH, VELOCITY, INVENTORY, INTERACT, FASTCLIMB, hW, Ch, iv};
   }

   private static void _s/* $VF was: 7s*/() {
      r7 = new String[]{
         "AIM",
         "Aim",
         "AUTOCLICKER",
         "AutoClicker",
         "BADPACKETS",
         "BadPackets",
         "FLY",
         "Fly",
         "AUTOFISH",
         "AutoFish",
         "KILLAURA",
         "KillAura",
         "SCAFFOLD",
         "Scaffold",
         "SPEED",
         "Speed",
         "MOTION",
         "Motion",
         "NOFALL",
         "NoFall",
         "JESUS",
         "Jesus",
         "PLAYERESP",
         "PlayerESP",
         "BARITONE",
         "Baritone",
         "VEHICLE",
         "Vehicle",
         "ELYTRA",
         "Elytra",
         "TIMER",
         "Timer",
         "REACH",
         "Reach",
         "VELOCITY",
         "Velocity",
         "INVENTORY",
         "Inventory",
         "INTERACT",
         "Interact",
         "FASTCLIMB",
         "FastClimb",
         "XRAY",
         "XRay",
         "HITBOX",
         "Hitbox"
      };
   }
}
