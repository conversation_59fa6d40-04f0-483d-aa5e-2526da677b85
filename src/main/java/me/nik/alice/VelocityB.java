package me.nik.alice;

import me.nik.alice.checks.PacketCheck;

public class r extends PacketCheck {
   private static String[] m3;

   static {
      T5();
   }

   public r(UC var1) {
      super(var1, VL.tm, m3[0], b.dx);
   }

   @Override
   public void dx(DH var1) {
   }

   private static void T5() {
      m3 = new String[]{"B", "Checks for low vertical velocity ratio"};
   }

   @Override
   public String sB() {
      return m3[1];
   }
}
