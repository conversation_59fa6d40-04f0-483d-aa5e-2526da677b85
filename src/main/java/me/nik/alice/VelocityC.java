package me.nik.alice;

import me.nik.alice.checks.PacketCheck;

public class h extends PacketCheck {
   private static String[] 2i;

   static {
      Bb();
   }

   private static void Bb() {
      2i = new String[]{"C", "Checks for low horizontal velocity ratio"};
   }

   public h(UC var1) {
      super(var1, VL.tm, 2i[0], b.dx, 1.0F);
   }

   @Override
   public String sB() {
      return 2i[1];
   }

   @Override
   public void dx(DH var1) {
   }
}
