package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class W extends PacketCheck {
   private static String[] F1;

   public W(UC var1) {
      super(var1, CheckType.MOTION, F1[0], Category.MOVE, 3.0F);
   }

   @Override
   public String sB() {
      return F1[1];
   }

   static {
      42();
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && !this.Aa.dx().jA()
         && !this.Aa.dx().jD()
         && !this.Aa.dx().QJ()
         && this.Aa.dx().Wx() >= 100
         && !this.Aa.dx().dx().DP()
         && !this.Aa.dx().DB()) {
         ES var12 = this.Aa.dx();
         Qk var2 = this.Aa.dx();
         int var3 = 6;
         if (var12.F7() > 0.0 || var12.Qt()) {
            var3++;
         }

         int var4;
         if ((var4 = var12.dx().og(5000L)) > 0) {
            var3 += 5 * var4;
         }

         if (var12.R2() < 60) {
            var3 += 60;
         }

         var4 = (int)(Math.abs(var2.getVelocityY()) * 75.0);
         if (var2.tr()) {
            var3 += var4 + 10;
         }

         int var13 = var12.M3();
         var4 = var12.u();
         int var5 = var12.Q();
         double var10 = var12.Ch();
         if ((var4 > var3 || var5 > var3) && var13 >= 4 && var10 >= 0.0) {
            this.Aa(F1[2] + var4 + F1[3] + var5 + F1[4] + var13 + F1[5] + var10);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.05);
         }
      }
   }

   private static void _2/* $VF was: 42*/() {
      F1 = new String[]{"E", "Checks for air jump", "Client: ", " server: ", " air: ", " delta Y: "};
   }
}
