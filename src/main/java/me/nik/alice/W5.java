package me.nik.alice;

import org.bukkit.entity.Arrow;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.player.PlayerQuitEvent;
import org.bukkit.event.player.PlayerRespawnEvent;
import org.bukkit.event.player.PlayerTeleportEvent;

public class W5 implements Listener {
   @EventHandler(
      priority = EventPriority.MONITOR,
      ignoreCancelled = true
   )
   public void dx(PlayerRespawnEvent var1) {
      Player var2 = var1.getPlayer();
      if (w.Sx() && var2.isGliding()) {
         var2.setGliding(false);
      }

      if (var2.isSprinting()) {
         var2.setSprinting(false);
      }

      if (var2.isSneaking()) {
         var2.setSneaking(false);
      }
   }

   @EventHandler(
      priority = EventPriority.MONITOR,
      ignoreCancelled = true
   )
   public void dx(PlayerTeleportEvent var1) {
      Player var2;
      if ((var2 = var1.getPlayer()).isSprinting()) {
         var2.setSprinting(false);
      }

      if (var2.isSneaking()) {
         var2.setSneaking(false);
      }
   }

   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void dx(PlayerQuitEvent var1) {
      if (me.nik.alice.UN.dx.hB.dC()) {
         Entity var10000 = var1.getPlayer().getVehicle();

         try {
            var10000.hashCode();
         } catch (Exception var2) {
            return;
         }

         var1.getPlayer().getVehicle().eject();
      }
   }

   @EventHandler(
      priority = EventPriority.LOWEST
   )
   public void dx(EntityDamageByEntityEvent var1) {
      if (me.nik.alice.UN.dx.Wx.dC()) {
         Entity var2;
         if ((var2 = var1.getDamager()) instanceof Arrow && ((Arrow)var2).getShooter() == var1.getEntity()) {
            var1.setCancelled(true);
         }
      }
   }
}
