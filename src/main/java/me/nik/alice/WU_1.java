package me.nik.alice;

import com.comphenix.protocol.wrappers.PlayerInfoData;
import com.comphenix.protocol.wrappers.WrappedChatComponent;
import com.comphenix.protocol.wrappers.WrappedGameProfile;
import com.comphenix.protocol.wrappers.EnumWrappers.NativeGameMode;
import com.comphenix.protocol.wrappers.EnumWrappers.PlayerInfoAction;
import java.util.Collections;
import java.util.UUID;
import me.nik.alice.WU.1;
import me.nik.alice.WU.2;
import me.nik.alice.wrappers.WrapperPlayServerEntityDestroy;
import me.nik.alice.wrappers.WrapperPlayServerEntityHeadRotation;
import me.nik.alice.wrappers.WrapperPlayServerEntityTeleport;
import me.nik.alice.wrappers.WrapperPlayServerNamedEntitySpawn;
import me.nik.alice.wrappers.WrapperPlayServerPlayerInfo;
import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class WU {
   private final WrapperPlayServerEntityHeadRotation dx;
   private final WrapperPlayServerNamedEntitySpawn dx;
   private final WrapperPlayServerEntityDestroy dx;
   private final Player player;
   private int Eu;
   private boolean zP;
   private final WrapperPlayServerPlayerInfo dx;
   private final WrapperPlayServerEntityTeleport dx;

   public WU(Player var1) {
      this.player = var1;
      this.dx = new WrapperPlayServerNamedEntitySpawn();
      this.dx = new WrapperPlayServerPlayerInfo();
      this.dx = new WrapperPlayServerEntityHeadRotation();
      this.dx = new WrapperPlayServerEntityDestroy();
      this.dx = new WrapperPlayServerEntityTeleport();
   }

   static void dx(WU var0) {
      var0.cO();
   }

   private Vector dC() {
      Location var10000 = this.player.getLocation();
      return var10000.add(var10000.getDirection().normalize().multiply(me.nik.alice.Ww.dx.dC.dx())).add(0.0, me.nik.alice.Ww.dx.Zh.dx(), 0.0).toVector();
   }

   static Player dx(WU var0) {
      return var0.player;
   }

   public int dC() {
      return this.Eu;
   }

   public void a() {
      if (this.player.isOnline()) {
         if (!this.zP) {
            UUID var1 = UUID.randomUUID();
            String var2 = Ae.zP();
            this.Eu = Ae.Zh();
            this.dx.setEntityID(this.dC());
            this.dx.setPosition(this.dC());
            this.dx.setPlayerUUID(var1);
            WrappedGameProfile var3 = new WrappedGameProfile(var1, var2);
            PlayerInfoData var4 = new PlayerInfoData(var3, 93, NativeGameMode.SURVIVAL, WrappedChatComponent.fromText(var2));
            this.dx.setData(Collections.singletonList(var4));
            this.dx.setEntityID(this.Eu);
            this.dx.setEntityIds(new int[]{this.Eu});
            this.dx.setEntityID(this.Eu);
            this.dx.setAction(PlayerInfoAction.ADD_PLAYER);
            this.dx.setHeadYaw(this.player.getLocation().getYaw());
            this.dx.sendPacket(this.player);
            this.dx.sendPacket(this.player);
            this.dx.sendPacket(this.player);
            this.zP = true;
            int var5 = me.nik.alice.Ww.dx.Fh.Aa();
            if (me.nik.alice.Ww.dx.sB.dC()) {
               new 1(this).runTaskTimerAsynchronously(Alice.dx(), 0L, (long)var5);
            } else {
               new 2(this).runTaskTimer(Alice.dx(), 0L, (long)var5);
            }

            wf.Aa(this::remove, (long)me.nik.alice.Ww.dx.h5.Aa());
         }
      }
   }

   private void remove() {
      if (this.zP) {
         this.zP = false;
         this.dx.setAction(PlayerInfoAction.REMOVE_PLAYER);
         this.dx.sendPacket(this.player);
         this.dx.sendPacket(this.player);
      }
   }

   public boolean tm() {
      return this.zP;
   }

   static boolean dx(WU var0) {
      return var0.zP;
   }

   static void Aa(WU var0) {
      var0.remove();
   }

   private void cO() {
      this.dx.setPos(this.dC());
      this.dx.setOnGround(true);
      this.dx.sendPacket(this.player);
      this.dx.setHeadYaw(this.player.getLocation().getYaw());
      this.dx.sendPacket(this.player);
   }
}
