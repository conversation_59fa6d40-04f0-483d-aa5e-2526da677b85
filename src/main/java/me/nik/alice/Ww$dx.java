package me.nik.alice;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public enum Ww$dx {
   dx(Ww$dx.yl[1], Ww$dx.yl[2], new String[]{Ww$dx.yl[3]}),
   Aa(Ww$dx.yl[5], Ww$dx.yl[6], new String[]{Ww$dx.yl[7]}),
   og(Ww$dx.yl[9], Boolean.TRUE, new String[]{Ww$dx.yl[10]}),
   AC(Ww$dx.yl[12], 10, new String[]{Ww$dx.yl[13]}),
   sB(Ww$dx.yl[15], Boolean.TRUE, new String[]{Ww$dx.yl[16]}),
   dC(Ww$dx.yl[18], -2.5, new String[]{Ww$dx.yl[19]}),
   Zh(Ww$dx.yl[21], 1.65, new String[]{Ww$dx.yl[22]}),
   h5(Ww$dx.yl[24], 140, new String[]{Ww$dx.yl[25]}),
   Fh(Ww$dx.yl[27], 1, new String[]{Ww$dx.yl[28]}),
   b(Ww$dx.yl[30], Boolean.TRUE, new String[]{Ww$dx.yl[31]}),
   VL(Ww$dx.yl[33], Boolean.TRUE, new String[]{Ww$dx.yl[34]}),
   UH(Ww$dx.yl[36], Boolean.TRUE, new String[]{Ww$dx.yl[37]}),
   yM(Ww$dx.yl[39], Boolean.TRUE, new String[]{Ww$dx.yl[40]}),
   sX(Ww$dx.yl[42], Boolean.TRUE, new String[]{Ww$dx.yl[43]}),
   WB(Ww$dx.yl[45], 10, new String[]{Ww$dx.yl[46]}),
   Vm(Ww$dx.yl[48], Collections.singletonList(Ww$dx.yl[49]), new String[]{Ww$dx.yl[50]}),
   gz(Ww$dx.yl[52], Ww$dx.yl[53], new String[]{Ww$dx.yl[54]}),
   tm(Ww$dx.yl[56], Boolean.TRUE, new String[]{Ww$dx.yl[57]}),
   x(Ww$dx.yl[59], Boolean.TRUE, new String[]{Ww$dx.yl[60]}),
   zP(Ww$dx.yl[62], Boolean.TRUE, new String[]{Ww$dx.yl[63]}),
   hW(Ww$dx.yl[65], Boolean.TRUE, new String[]{Ww$dx.yl[66]}),
   Ch(Ww$dx.yl[68], Boolean.TRUE, new String[]{Ww$dx.yl[69]}),
   iv(Ww$dx.yl[71], Boolean.TRUE, new String[]{Ww$dx.yl[72]}),
   F7(Ww$dx.yl[74], Boolean.TRUE, new String[]{Ww$dx.yl[75]}),
   Zp(Ww$dx.yl[77], Boolean.TRUE, new String[]{Ww$dx.yl[78]}),
   DB(Ww$dx.yl[80], Boolean.TRUE, new String[]{Ww$dx.yl[81]}),
   jD(Ww$dx.yl[83], Boolean.TRUE, new String[]{Ww$dx.yl[84]}),
   Qt(Ww$dx.yl[86], Boolean.TRUE, new String[]{Ww$dx.yl[87]}),
   rc(Ww$dx.yl[89], Boolean.TRUE, new String[]{Ww$dx.yl[90]}),
   Zm(Ww$dx.yl[92], Boolean.TRUE, new String[]{Ww$dx.yl[93]}),
   jA(Ww$dx.yl[95], Boolean.TRUE, new String[]{Ww$dx.yl[96]}),
   bI(Ww$dx.yl[98], Boolean.TRUE, new String[]{Ww$dx.yl[99]}),
   yV(Ww$dx.yl[101], Boolean.TRUE, new String[]{Ww$dx.yl[102]}),
   a(Ww$dx.yl[104], Boolean.TRUE, new String[]{Ww$dx.yl[105]}),
   cO(Ww$dx.yl[107], Boolean.TRUE, new String[]{Ww$dx.yl[108]}),
   tk(Ww$dx.yl[110], 15, new String[]{Ww$dx.yl[111]}),
   Eu(Ww$dx.yl[113], Collections.singletonList(Ww$dx.yl[114]), new String[]{Ww$dx.yl[115]}),
   nZ(Ww$dx.yl[117], Ww$dx.yl[118], new String[]{Ww$dx.yl[119]}),
   u(Ww$dx.yl[121], Boolean.TRUE, new String[]{Ww$dx.yl[122]}),
   Q(Ww$dx.yl[124], Ww$dx.yl[125], new String[]{Ww$dx.yl[126]}),
   QJ(Ww$dx.yl[128], Boolean.TRUE, new String[]{Ww$dx.yl[129]}),
   o4(Ww$dx.yl[131], 24, new String[]{Ww$dx.yl[132], Ww$dx.yl[133]}),
   tr(Ww$dx.yl[135], Boolean.TRUE, new String[]{Ww$dx.yl[136]}),
   pO(Ww$dx.yl[138], Boolean.TRUE, new String[]{Ww$dx.yl[139]}),
   DP(Ww$dx.yl[141], Boolean.TRUE, new String[]{Ww$dx.yl[142]}),
   p6(Ww$dx.yl[144], Boolean.TRUE, new String[]{Ww$dx.yl[145]}),
   R2(Ww$dx.yl[147], Boolean.TRUE, new String[]{Ww$dx.yl[148]}),
   Sh(Ww$dx.yl[150], Boolean.TRUE, new String[]{Ww$dx.yl[151]}),
   pQ(Ww$dx.yl[153], 10, new String[]{Ww$dx.yl[154]}),
   yk(Ww$dx.yl[156], Collections.singletonList(Ww$dx.yl[157]), new String[]{Ww$dx.yl[158]}),
   P(Ww$dx.yl[160], Ww$dx.yl[161], new String[]{Ww$dx.yl[162]}),
   R(Ww$dx.yl[164], Boolean.TRUE, new String[]{Ww$dx.yl[165]}),
   xx(Ww$dx.yl[167], Boolean.TRUE, new String[]{Ww$dx.yl[168]}),
   M3(Ww$dx.yl[170], Boolean.TRUE, new String[]{Ww$dx.yl[171]}),
   E(Ww$dx.yl[173], Boolean.TRUE, new String[]{Ww$dx.yl[174]}),
   tV(Ww$dx.yl[176], Boolean.TRUE, new String[]{Ww$dx.yl[177]}),
   GE(Ww$dx.yl[179], Boolean.TRUE, new String[]{Ww$dx.yl[180]}),
   tU(Ww$dx.yl[182], Boolean.TRUE, new String[]{Ww$dx.yl[183]}),
   Wx(Ww$dx.yl[185], Boolean.TRUE, new String[]{Ww$dx.yl[186]}),
   hB(Ww$dx.yl[188], 20, new String[]{Ww$dx.yl[189]}),
   uX(Ww$dx.yl[191], Collections.singletonList(Ww$dx.yl[192]), new String[]{Ww$dx.yl[193]}),
   iK(Ww$dx.yl[195], Ww$dx.yl[196], new String[]{Ww$dx.yl[197]}),
   K(Ww$dx.yl[199], Boolean.TRUE, new String[]{Ww$dx.yl[200]}),
   SP(Ww$dx.yl[202], Boolean.TRUE, new String[]{Ww$dx.yl[203]}),
   uD(Ww$dx.yl[205], Boolean.TRUE, new String[]{Ww$dx.yl[206]}),
   PU(Ww$dx.yl[208], Boolean.TRUE, new String[]{Ww$dx.yl[209]}),
   J4(Ww$dx.yl[211], Boolean.TRUE, new String[]{Ww$dx.yl[212]}),
   Lh(Ww$dx.yl[214], Boolean.TRUE, new String[]{Ww$dx.yl[215]}),
   Ej(Ww$dx.yl[217], Boolean.TRUE, new String[]{Ww$dx.yl[218]}),
   CS(Ww$dx.yl[220], Boolean.TRUE, new String[]{Ww$dx.yl[221]}),
   d(Ww$dx.yl[223], Boolean.TRUE, new String[]{Ww$dx.yl[224]}),
   Vl(Ww$dx.yl[226], 20, new String[]{Ww$dx.yl[227]}),
   i2(Ww$dx.yl[229], Collections.singletonList(Ww$dx.yl[230]), new String[]{Ww$dx.yl[231]}),
   y(Ww$dx.yl[233], Ww$dx.yl[234], new String[]{Ww$dx.yl[235]}),
   qx(Ww$dx.yl[237], Boolean.TRUE, new String[]{Ww$dx.yl[238]}),
   Tq(Ww$dx.yl[240], Boolean.TRUE, new String[]{Ww$dx.yl[241]}),
   qa(Ww$dx.yl[243], Boolean.TRUE, new String[]{Ww$dx.yl[244]}),
   v(Ww$dx.yl[246], Boolean.TRUE, new String[]{Ww$dx.yl[247]}),
   N7(Ww$dx.yl[249], Boolean.TRUE, new String[]{Ww$dx.yl[250]}),
   Sx(Ww$dx.yl[252], Boolean.TRUE, new String[]{Ww$dx.yl[253]}),
   rr(Ww$dx.yl[255], Boolean.TRUE, new String[]{Ww$dx.yl[256]}),
   cf(Ww$dx.yl[258], Boolean.TRUE, new String[]{Ww$dx.yl[259]}),
   EZ(Ww$dx.yl[261], 20, new String[]{Ww$dx.yl[262]}),
   IK(Ww$dx.yl[264], Collections.singletonList(Ww$dx.yl[265]), new String[]{Ww$dx.yl[266]}),
   sp(Ww$dx.yl[268], Ww$dx.yl[269], new String[]{Ww$dx.yl[270]}),
   PP(Ww$dx.yl[272], Boolean.FALSE, new String[]{Ww$dx.yl[273]}),
   nA(Ww$dx.yl[275], 60, new String[]{Ww$dx.yl[276]}),
   qJ(Ww$dx.yl[278], Boolean.TRUE, new String[]{Ww$dx.yl[279]}),
   GD(Ww$dx.yl[281], 40, new String[]{Ww$dx.yl[282]}),
   Zb(Ww$dx.yl[284], Arrays.asList(Ww$dx.yl[285], Ww$dx.yl[286], Ww$dx.yl[287], Ww$dx.yl[288], Ww$dx.yl[289], Ww$dx.yl[290]), new String[]{Ww$dx.yl[291]}),
   k0(Ww$dx.yl[293], 2, new String[]{Ww$dx.yl[294]}),
   fa(Ww$dx.yl[296], Collections.singletonList(Ww$dx.yl[297]), new String[]{Ww$dx.yl[298]}),
   Pu(Ww$dx.yl[300], Ww$dx.yl[301], new String[]{Ww$dx.yl[302]}),
   f7(Ww$dx.yl[304], Boolean.TRUE, new String[]{Ww$dx.yl[305]}),
   fq(Ww$dx.yl[307], 5, new String[]{Ww$dx.yl[308]}),
   Zt(Ww$dx.yl[310], Collections.singletonList(Ww$dx.yl[311]), new String[]{Ww$dx.yl[312]}),
   xB(Ww$dx.yl[314], Ww$dx.yl[315], new String[]{Ww$dx.yl[316]}),
   EG(Ww$dx.yl[318], Boolean.TRUE, new String[]{Ww$dx.yl[319]}),
   Tf(Ww$dx.yl[321], Boolean.TRUE, new String[]{Ww$dx.yl[322]}),
   KJ(Ww$dx.yl[324], Boolean.TRUE, new String[]{Ww$dx.yl[325]}),
   FH(Ww$dx.yl[327], 20, new String[]{Ww$dx.yl[328]}),
   J(Ww$dx.yl[330], Collections.singletonList(Ww$dx.yl[331]), new String[]{Ww$dx.yl[332]}),
   kV(Ww$dx.yl[334], Ww$dx.yl[335], new String[]{Ww$dx.yl[336]}),
   G(Ww$dx.yl[338], Ww$dx.yl[339], new String[]{Ww$dx.yl[340], Ww$dx.yl[341], Ww$dx.yl[342], Ww$dx.yl[343]}),
   W(Ww$dx.yl[345], Boolean.TRUE, new String[]{Ww$dx.yl[346]}),
   z(Ww$dx.yl[348], 150, new String[]{Ww$dx.yl[349], Ww$dx.yl[350], Ww$dx.yl[351], Ww$dx.yl[352]}),
   mQ(Ww$dx.yl[354], Boolean.TRUE, new String[]{Ww$dx.yl[355], Ww$dx.yl[356]}),
   YL(Ww$dx.yl[358], 3.01, new String[]{Ww$dx.yl[359]}),
   iL(Ww$dx.yl[361], 10, new String[]{Ww$dx.yl[362]}),
   DC(Ww$dx.yl[364], Collections.singletonList(Ww$dx.yl[365]), new String[]{Ww$dx.yl[366]}),
   D1(Ww$dx.yl[368], Ww$dx.yl[369], new String[]{Ww$dx.yl[370]}),
   tN(Ww$dx.yl[372], Boolean.TRUE, new String[]{Ww$dx.yl[373]}),
   p(Ww$dx.yl[375], Boolean.TRUE, new String[]{Ww$dx.yl[376]}),
   Qf(Ww$dx.yl[378], Boolean.TRUE, new String[]{Ww$dx.yl[379]}),
   O5(Ww$dx.yl[381], 15, new String[]{Ww$dx.yl[382]}),
   KB(Ww$dx.yl[384], Collections.singletonList(Ww$dx.yl[385]), new String[]{Ww$dx.yl[386]}),
   yx(Ww$dx.yl[388], Ww$dx.yl[389], new String[]{Ww$dx.yl[390]}),
   UZ(Ww$dx.yl[392], Ww$dx.yl[393], new String[]{Ww$dx.yl[394], Ww$dx.yl[395], Ww$dx.yl[396]}),
   Hy(Ww$dx.yl[398], Boolean.TRUE, new String[]{Ww$dx.yl[399]}),
   fT(Ww$dx.yl[401], 0.125, new String[]{Ww$dx.yl[402], Ww$dx.yl[403]}),
   C3(Ww$dx.yl[405], 3.01, new String[]{Ww$dx.yl[406]}),
   vg(Ww$dx.yl[408], 1, new String[]{Ww$dx.yl[409], Ww$dx.yl[410], Ww$dx.yl[411], Ww$dx.yl[412], Ww$dx.yl[413]}),
   oN(Ww$dx.yl[415], 200, new String[]{Ww$dx.yl[416], Ww$dx.yl[417], Ww$dx.yl[418], Ww$dx.yl[419]}),
   Ac(Ww$dx.yl[421], Ww$dx.yl[422], new String[]{Ww$dx.yl[423], Ww$dx.yl[424]}),
   Xm(Ww$dx.yl[426], Boolean.TRUE, new String[]{Ww$dx.yl[427]}),
   A(Ww$dx.yl[429], 0.05, new String[]{Ww$dx.yl[430]}),
   SU(Ww$dx.yl[432], 10, new String[]{Ww$dx.yl[433]}),
   vi(Ww$dx.yl[435], Collections.singletonList(Ww$dx.yl[436]), new String[]{Ww$dx.yl[437]}),
   pn(Ww$dx.yl[439], Ww$dx.yl[440], new String[]{Ww$dx.yl[441]}),
   Bo(Ww$dx.yl[443], Boolean.TRUE, new String[]{Ww$dx.yl[444]}),
   Eg(Ww$dx.yl[446], Ww$dx.yl[447], new String[]{Ww$dx.yl[448]}),
   Qp(Ww$dx.yl[450], Boolean.TRUE, new String[]{Ww$dx.yl[451]}),
   Mt(Ww$dx.yl[453], 80, new String[]{Ww$dx.yl[454]}),
   PJ(Ww$dx.yl[456], 10, new String[]{Ww$dx.yl[457]}),
   r(Ww$dx.yl[459], Collections.singletonList(Ww$dx.yl[460]), new String[]{Ww$dx.yl[461]}),
   h(Ww$dx.yl[463], Ww$dx.yl[464], new String[]{Ww$dx.yl[465]}),
   gU(Ww$dx.yl[467], Boolean.TRUE, new String[]{Ww$dx.yl[468]}),
   ts(Ww$dx.yl[470], Boolean.TRUE, new String[]{Ww$dx.yl[471]}),
   kL(Ww$dx.yl[473], Boolean.TRUE, new String[]{Ww$dx.yl[474]}),
   Zv(Ww$dx.yl[476], Boolean.TRUE, new String[]{Ww$dx.yl[477]}),
   Zc(Ww$dx.yl[479], 20, new String[]{Ww$dx.yl[480]}),
   m(Ww$dx.yl[482], Collections.singletonList(Ww$dx.yl[483]), new String[]{Ww$dx.yl[484]}),
   K0(Ww$dx.yl[486], Ww$dx.yl[487], new String[]{Ww$dx.yl[488]}),
   lg(Ww$dx.yl[490], Boolean.TRUE, new String[]{Ww$dx.yl[491]}),
   Zi(Ww$dx.yl[493], Boolean.TRUE, new String[]{Ww$dx.yl[494]}),
   Al(Ww$dx.yl[496], 15, new String[]{Ww$dx.yl[497]}),
   be(Ww$dx.yl[499], Collections.singletonList(Ww$dx.yl[500]), new String[]{Ww$dx.yl[501]}),
   zB(Ww$dx.yl[503], Ww$dx.yl[504], new String[]{Ww$dx.yl[505], Ww$dx.yl[506]}),
   zk(Ww$dx.yl[508], Boolean.TRUE, new String[]{Ww$dx.yl[509]}),
   Q7(Ww$dx.yl[511], Boolean.TRUE, new String[]{Ww$dx.yl[512]}),
   OL(Ww$dx.yl[514], Boolean.TRUE, new String[]{Ww$dx.yl[515]}),
   u2(Ww$dx.yl[517], Boolean.TRUE, new String[]{Ww$dx.yl[518]}),
   aO(Ww$dx.yl[520], Boolean.TRUE, new String[]{Ww$dx.yl[521]}),
   rP(Ww$dx.yl[523], Boolean.TRUE, new String[]{Ww$dx.yl[524]}),
   L(Ww$dx.yl[526], Boolean.TRUE, new String[]{Ww$dx.yl[527]}),
   I(Ww$dx.yl[529], Boolean.TRUE, new String[]{Ww$dx.yl[530]}),
   f2(Ww$dx.yl[532], Boolean.TRUE, new String[]{Ww$dx.yl[533]}),
   C(Ww$dx.yl[535], Boolean.TRUE, new String[]{Ww$dx.yl[536]}),
   rn(Ww$dx.yl[538], Boolean.TRUE, new String[]{Ww$dx.yl[539]}),
   QF(Ww$dx.yl[541], Boolean.TRUE, new String[]{Ww$dx.yl[542]}),
   ZO(Ww$dx.yl[544], Boolean.TRUE, new String[]{Ww$dx.yl[545]}),
   Od(Ww$dx.yl[547], Boolean.TRUE, new String[]{Ww$dx.yl[548]}),
   e(Ww$dx.yl[550], Boolean.TRUE, new String[]{Ww$dx.yl[551]}),
   qg(Ww$dx.yl[553], Boolean.TRUE, new String[]{Ww$dx.yl[554]}),
   Ww(Ww$dx.yl[556], Boolean.TRUE, new String[]{Ww$dx.yl[557]}),
   UN(Ww$dx.yl[559], Boolean.TRUE, new String[]{Ww$dx.yl[560]}),
   Y(Ww$dx.yl[562], Boolean.TRUE, new String[]{Ww$dx.yl[563]}),
   D8(Ww$dx.yl[565], Boolean.TRUE, new String[]{Ww$dx.yl[566]}),
   q(Ww$dx.yl[568], Boolean.TRUE, new String[]{Ww$dx.yl[569]}),
   lk(Ww$dx.yl[571], Boolean.TRUE, new String[]{Ww$dx.yl[572]}),
   EJ(Ww$dx.yl[574], Boolean.TRUE, new String[]{Ww$dx.yl[575]}),
   QZ(Ww$dx.yl[577], Boolean.TRUE, new String[]{Ww$dx.yl[578]}),
   AH(Ww$dx.yl[580], Boolean.TRUE, new String[]{Ww$dx.yl[581]}),
   pl(Ww$dx.yl[583], Boolean.TRUE, new String[]{Ww$dx.yl[584]}),
   AZ(Ww$dx.yl[586], Boolean.TRUE, new String[]{Ww$dx.yl[587]}),
   Mx(Ww$dx.yl[589], Boolean.TRUE, new String[]{Ww$dx.yl[590]}),
   jl(Ww$dx.yl[592], Boolean.TRUE, new String[]{Ww$dx.yl[593]}),
   Dw(Ww$dx.yl[595], Boolean.TRUE, new String[]{Ww$dx.yl[596]}),
   gr(Ww$dx.yl[598], 25, new String[]{Ww$dx.yl[599]}),
   wZ(Ww$dx.yl[601], Collections.singletonList(Ww$dx.yl[602]), new String[]{Ww$dx.yl[603]}),
   D(Ww$dx.yl[605], Ww$dx.yl[606], new String[]{Ww$dx.yl[607]}),
   W5(Ww$dx.yl[609], Boolean.FALSE, new String[]{Ww$dx.yl[610]}),
   tu(Ww$dx.yl[612], 50, new String[]{Ww$dx.yl[613]}),
   O8(Ww$dx.yl[615], 100, new String[]{Ww$dx.yl[616], Ww$dx.yl[617]}),
   N(Ww$dx.yl[619], Ww$dx.yl[620], new String[]{Ww$dx.yl[621]}),
   Ib(Ww$dx.yl[623], Ww$dx.yl[624], new String[]{Ww$dx.yl[625]}),
   Sy(Ww$dx.yl[627], Boolean.TRUE, new String[]{Ww$dx.yl[628]}),
   Gy(Ww$dx.yl[630], -80, new String[]{Ww$dx.yl[631]}),
   NE(Ww$dx.yl[633], Boolean.TRUE, new String[]{Ww$dx.yl[634]}),
   CI(Ww$dx.yl[636], Boolean.TRUE, new String[]{Ww$dx.yl[637]}),
   F(Ww$dx.yl[639], Ww$dx.yl[640], new String[]{Ww$dx.yl[641]}),
   ZM(Ww$dx.yl[643], Boolean.TRUE, new String[]{Ww$dx.yl[644]}),
   EK(Ww$dx.yl[646], Collections.singletonList(Ww$dx.yl[647]), new String[]{Ww$dx.yl[648]}),
   ia(Ww$dx.yl[650], 10, new String[]{Ww$dx.yl[651]}),
   bV(Ww$dx.yl[653], Collections.singletonList(Ww$dx.yl[654]), new String[]{Ww$dx.yl[655]}),
   gW(Ww$dx.yl[657], Ww$dx.yl[658], new String[]{Ww$dx.yl[659]}),
   GK(Ww$dx.yl[661], Boolean.TRUE, new String[]{Ww$dx.yl[662]}),
   Ne(Ww$dx.yl[664], Boolean.TRUE, new String[]{Ww$dx.yl[665]}),
   QU(Ww$dx.yl[667], Boolean.TRUE, new String[]{Ww$dx.yl[668]}),
   Em(Ww$dx.yl[670], Boolean.TRUE, new String[]{Ww$dx.yl[671]}),
   jx(Ww$dx.yl[673], Boolean.TRUE, new String[]{Ww$dx.yl[674]}),
   s6(Ww$dx.yl[676], Boolean.TRUE, new String[]{Ww$dx.yl[677]}),
   V0(Ww$dx.yl[679], Boolean.TRUE, new String[]{Ww$dx.yl[680]}),
   M5(Ww$dx.yl[682], 10, new String[]{Ww$dx.yl[683]}),
   Sm(Ww$dx.yl[685], Collections.singletonList(Ww$dx.yl[686]), new String[]{Ww$dx.yl[687]}),
   o2(Ww$dx.yl[689], Ww$dx.yl[690], new String[]{Ww$dx.yl[691]}),
   xt(Ww$dx.yl[693], Boolean.TRUE, new String[]{Ww$dx.yl[694]}),
   OB(Ww$dx.yl[696], Boolean.TRUE, new String[]{Ww$dx.yl[697]}),
   TQ(Ww$dx.yl[699], 10, new String[]{Ww$dx.yl[700]}),
   oG(Ww$dx.yl[702], Collections.singletonList(Ww$dx.yl[703]), new String[]{Ww$dx.yl[704]}),
   hL(Ww$dx.yl[706], Ww$dx.yl[707], new String[]{Ww$dx.yl[708]}),
   Uk(Ww$dx.yl[710], Boolean.TRUE, new String[]{Ww$dx.yl[711]}),
   Iy(Ww$dx.yl[713], 10, new String[]{Ww$dx.yl[714]}),
   Tw(Ww$dx.yl[716], Collections.singletonList(Ww$dx.yl[717]), new String[]{Ww$dx.yl[718]}),
   WU(Ww$dx.yl[720], Ww$dx.yl[721], new String[]{Ww$dx.yl[722]}),
   Ae(Ww$dx.yl[724], Boolean.TRUE, new String[]{Ww$dx.yl[725]}),
   MB(Ww$dx.yl[727], Boolean.TRUE, new String[]{Ww$dx.yl[728]}),
   ZU(Ww$dx.yl[730], Boolean.TRUE, new String[]{Ww$dx.yl[731], Ww$dx.yl[732]}),
   mz(Ww$dx.yl[734], 25, new String[]{Ww$dx.yl[735]}),
   UC(Ww$dx.yl[737], Collections.singletonList(Ww$dx.yl[738]), new String[]{Ww$dx.yl[739]}),
   Xp(Ww$dx.yl[741], Ww$dx.yl[742], new String[]{Ww$dx.yl[743]}),
   uq(Ww$dx.yl[745], Boolean.FALSE, new String[]{Ww$dx.yl[746]}),
   Fc(Ww$dx.yl[748], Boolean.TRUE, new String[]{Ww$dx.yl[749]}),
   Sk(Ww$dx.yl[751], 2, new String[]{Ww$dx.yl[752]}),
   ES(Ww$dx.yl[754], Collections.singletonList(Ww$dx.yl[755]), new String[]{Ww$dx.yl[756]});

   private final String key;
   private final Object dx;
   private boolean Fh;
   private final String[] og;
   private Object Aa = null;
   private static final Ww$dx[] dx;
   private static String[] yl;

   private Ww$dx(String var3, Object var4, String[] var5) {
      this.key = var3;
      this.dx = var4;
      this.og = var5 != null ? var5 : new String[0];
   }

   private Ww$dx(String var3, Object var4, boolean var5, String[] var6) {
      this.key = var3;
      this.dx = var4;
      this.og = var6 != null ? var6 : new String[0];
      this.Fh = var5;
   }

   public boolean dC() {
      this.x();
      return (Boolean)this.Aa;
   }

   public String Fh() {
      return this.key;
   }

   public int Aa() {
      this.x();
      return (int)this.Aa();
   }

   public long Aa() {
      this.x();
      return (long)this.Aa();
   }

   public double dx() {
      this.x();
      return this.Aa();
   }

   public float sB() {
      this.x();
      return (float)this.Aa();
   }

   public String b() {
      this.x();
      return String.valueOf(this.Aa);
   }

   private double Aa() {
      if (this.Aa instanceof Integer) {
         return (double)((Integer)this.Aa).intValue();
      } else if (this.Aa instanceof Short) {
         return (double)((Short)this.Aa).shortValue();
      } else if (this.Aa instanceof Byte) {
         return (double)((Byte)this.Aa).byteValue();
      } else {
         return this.Aa instanceof Float ? (double)((Float)this.Aa).floatValue() : (Double)this.Aa;
      }
   }

   public List Aa() {
      this.x();
      return (List)this.Aa;
   }

   private boolean dx(D8 var1) {
      this.x();
      if (me.nik.alice.Ww.sB() && this.Fh) {
         return false;
      } else {
         var1.get(this.key);

         try {
            return false;
         } catch (Exception var4) {
            List var2 = (List)Stream.of(this.og).collect(Collectors.toList());
            Object var10000 = this.dx;

            try {
               var10000.getClass();
            } catch (Exception var3) {
               var1.dx((String[])var2.toArray(new String[0]));
               return true;
            }

            var1.dx(this.key, this.dx, (String[])var2.toArray(new String[0]));
            return true;
         }
      }
   }

   public void reset() {
      this.Aa = null;
   }

   public boolean Zh() {
      Object var10000 = this.dx;

      try {
         var10000.equals(null);
         return false;
      } catch (Exception var1) {
         return true;
      }
   }

   private void x() {
      Object var10000 = this.Aa;

      try {
         var10000.equals(null);
      } catch (Exception var1) {
         this.Aa = Alice.dx().Aa().get(this.key);
      }
   }

   static boolean dx(Ww$dx var0, D8 var1) {
      return var0.dx(var1);
   }

   static {
      ye();
      dx = new Ww$dx[]{
         dx,
         Aa,
         og,
         AC,
         sB,
         dC,
         Zh,
         h5,
         Fh,
         b,
         VL,
         UH,
         yM,
         sX,
         WB,
         Vm,
         gz,
         tm,
         x,
         zP,
         hW,
         Ch,
         iv,
         F7,
         Zp,
         DB,
         jD,
         Qt,
         rc,
         Zm,
         jA,
         bI,
         yV,
         a,
         cO,
         tk,
         Eu,
         nZ,
         u,
         Q,
         QJ,
         o4,
         tr,
         pO,
         DP,
         p6,
         R2,
         Sh,
         pQ,
         yk,
         P,
         R,
         xx,
         M3,
         E,
         tV,
         GE,
         tU,
         Wx,
         hB,
         uX,
         iK,
         K,
         SP,
         uD,
         PU,
         J4,
         Lh,
         Ej,
         CS,
         d,
         Vl,
         i2,
         y,
         qx,
         Tq,
         qa,
         v,
         N7,
         Sx,
         rr,
         cf,
         EZ,
         IK,
         sp,
         PP,
         nA,
         qJ,
         GD,
         Zb,
         k0,
         fa,
         Pu,
         f7,
         fq,
         Zt,
         xB,
         EG,
         Tf,
         KJ,
         FH,
         J,
         kV,
         G,
         W,
         z,
         mQ,
         YL,
         iL,
         DC,
         D1,
         tN,
         p,
         Qf,
         O5,
         KB,
         yx,
         UZ,
         Hy,
         fT,
         C3,
         vg,
         oN,
         Ac,
         Xm,
         A,
         SU,
         vi,
         pn,
         Bo,
         Eg,
         Qp,
         Mt,
         PJ,
         r,
         h,
         gU,
         ts,
         kL,
         Zv,
         Zc,
         m,
         K0,
         lg,
         Zi,
         Al,
         be,
         zB,
         zk,
         Q7,
         OL,
         u2,
         aO,
         rP,
         L,
         I,
         f2,
         C,
         rn,
         QF,
         ZO,
         Od,
         e,
         qg,
         Ww,
         UN,
         Y,
         D8,
         q,
         lk,
         EJ,
         QZ,
         AH,
         pl,
         AZ,
         Mx,
         jl,
         Dw,
         gr,
         wZ,
         D,
         W5,
         tu,
         O8,
         N,
         Ib,
         Sy,
         Gy,
         NE,
         CI,
         F,
         ZM,
         EK,
         ia,
         bV,
         gW,
         GK,
         Ne,
         QU,
         Em,
         jx,
         s6,
         V0,
         M5,
         Sm,
         o2,
         xt,
         OB,
         TQ,
         oG,
         hL,
         Uk,
         Iy,
         Tw,
         WU,
         Ae,
         MB,
         ZU,
         mz,
         UC,
         Xp,
         uq,
         Fc,
         Sk,
         ES
      };
   }

   private static void ye() {
      yl = new String[]{
         "KILLAURA",
         "killaura",
         "",
         "KillAura Check",
         "KILLAURA_A",
         "killaura.a",
         "",
         "KillAura A Module (NPC)",
         "KILLAURA_A_ENABLED",
         "killaura.a.enabled",
         "Should we enable this module?",
         "KILLAURA_A_VL_REQUIRED",
         "killaura.a.vl_required",
         "How many Combat Violations does the Player need to reach in order to spawn the NPC? (Set to 0 to always spawn)",
         "KILLAURA_A_ASYNC",
         "killaura.a.async",
         "Should we run this Asynchronously? (Highly Recommended)",
         "KILLAURA_A_DISTANCE",
         "killaura.a.distance",
         "The distance between the NPC and the Player",
         "KILLAURA_A_HEIGHT",
         "killaura.a.height",
         "The height between the NPC and the Player",
         "KILLAURA_A_LIVING_TICKS",
         "killaura.a.living_ticks",
         "For how long should the NPC be spawned in ticks? (20 Ticks = 1 Second)",
         "KILLAURA_A_TELEPORT_TICKS",
         "killaura.a.teleport_ticks",
         "The ticks between teleporting the NPC behind the player",
         "KILLAURA_B",
         "killaura.b",
         "Should we enable this module?",
         "KILLAURA_C",
         "killaura.c",
         "Should we enable this module?",
         "KILLAURA_D",
         "killaura.d",
         "Should we enable this module?",
         "KILLAURA_E",
         "killaura.e",
         "Should we enable this module?",
         "KILLAURA_F",
         "killaura.f",
         "Should we enable this module?",
         "KILLAURA_MAX_VL",
         "killaura.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "KILLAURA_COMMANDS",
         "killaura.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "AIM",
         "aim",
         "",
         "Aim Check",
         "AIM_A",
         "aim.a",
         "Should we enable this module?",
         "AIM_B",
         "aim.b",
         "Should we enable this module?",
         "AIM_C",
         "aim.c",
         "Should we enable this module?",
         "AIM_D",
         "aim.d",
         "Should we enable this module?",
         "AIM_E",
         "aim.e",
         "Should we enable this module?",
         "AIM_F",
         "aim.f",
         "Should we enable this module?",
         "AIM_G",
         "aim.g",
         "Should we enable this module?",
         "AIM_H",
         "aim.h",
         "Should we enable this module?",
         "AIM_I",
         "aim.i",
         "Should we enable this module?",
         "AIM_J",
         "aim.j",
         "Should we enable this module?",
         "AIM_K",
         "aim.k",
         "Should we enable this module?",
         "AIM_L",
         "aim.l",
         "Should we enable this module?",
         "AIM_M",
         "aim.m",
         "Should we enable this module?",
         "AIM_N",
         "aim.n",
         "Should we enable this module?",
         "AIM_O",
         "aim.o",
         "Should we enable this module?",
         "AIM_P",
         "aim.p",
         "Should we enable this module?",
         "AIM_Q",
         "aim.q",
         "Should we enable this module?",
         "AIM_R",
         "aim.r",
         "Should we enable this module?",
         "AIM_MAX_VL",
         "aim.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "AIM_COMMANDS",
         "aim.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "AUTOCLICKER",
         "autoclicker",
         "",
         "AutoClicker Check",
         "AUTOCLICKER_A",
         "autoclicker.a",
         "Should we enable this module?",
         "AUTOCLICKER_B",
         "autoclicker.b",
         "",
         "AutoClicker B Module",
         "AUTOCLICKER_B_ENABLED",
         "autoclicker.b.enabled",
         "Should we enable this module?",
         "AUTOCLICKER_B_MAX_CPS",
         "autoclicker.b.max_cps",
         "How many Clicks per Second a player needs to go above in order to fail this check?",
         "You may want to increase this value if you want to allow drag clicking",
         "AUTOCLICKER_C",
         "autoclicker.c",
         "Should we enable this module?",
         "AUTOCLICKER_D",
         "autoclicker.d",
         "Should we enable this module?",
         "AUTOCLICKER_E",
         "autoclicker.e",
         "Should we enable this module?",
         "AUTOCLICKER_F",
         "autoclicker.f",
         "Should we enable this module?",
         "AUTOCLICKER_G",
         "autoclicker.g",
         "Should we enable this module?",
         "AUTOCLICKER_H",
         "autoclicker.h",
         "Should we enable this module?",
         "AUTOCLICKER_MAX_VL",
         "autoclicker.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "AUTOCLICKER_COMMANDS",
         "autoclicker.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "SPEED",
         "speed",
         "",
         "Speed Check",
         "SPEED_AIR",
         "speed.air",
         "Should we enable this module?",
         "SPEED_FRICTION",
         "speed.friction",
         "Should we enable this module?",
         "SPEED_ACCELERATION",
         "speed.acceleration",
         "Should we enable this module?",
         "SPEED_EXPECT",
         "speed.expect",
         "Should we enable this module?",
         "SPEED_SPRINT",
         "speed.sprint",
         "Should we enable this module?",
         "SPEED_NOSLOW",
         "speed.noslow",
         "Should we enable this module?",
         "SPEED_STRAFE",
         "speed.strafe",
         "Should we enable this module?",
         "SPEED_STATIC",
         "speed.static",
         "Should we enable this module?",
         "SPEED_MAX_VL",
         "speed.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "SPEED_COMMANDS",
         "speed.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "MOTION",
         "motion",
         "",
         "Motion Check",
         "MOTION_A",
         "motion.a",
         "Should we enable this module?",
         "MOTION_B",
         "motion.b",
         "Should we enable this module?",
         "MOTION_C",
         "motion.c",
         "Should we enable this module?",
         "MOTION_D",
         "motion.d",
         "Should we enable this module?",
         "MOTION_E",
         "motion.e",
         "Should we enable this module?",
         "MOTION_F",
         "motion.f",
         "Should we enable this module?",
         "MOTION_G",
         "motion.g",
         "Should we enable this module?",
         "MOTION_H",
         "motion.h",
         "Should we enable this module?",
         "MOTION_I",
         "motion.i",
         "Should we enable this module?",
         "MOTION_MAX_VL",
         "motion.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "MOTION_COMMANDS",
         "motion.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "FLY",
         "fly",
         "",
         "Fly Check",
         "FLY_A",
         "fly.a",
         "Should we enable this module?",
         "FLY_B",
         "fly.b",
         "Should we enable this module?",
         "FLY_C",
         "fly.c",
         "Should we enable this module?",
         "FLY_D",
         "fly.d",
         "Should we enable this module?",
         "FLY_E",
         "fly.e",
         "Should we enable this module?",
         "FLY_F",
         "fly.f",
         "Should we enable this module?",
         "FLY_G",
         "fly.g",
         "Should we enable this module?",
         "FLY_H",
         "fly.h",
         "Should we enable this module?",
         "FLY_MAX_VL",
         "fly.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "FLY_COMMANDS",
         "fly.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "XRAY",
         "xray",
         "",
         "XRay Check",
         "XRAY_ENABLED",
         "xray.enabled",
         "Should we enable this check?",
         "XRAY_RESET_INTERVAL",
         "xray.reset_interval",
         "After how many seconds should Alice reset the XRay data?",
         "XRAY_SEALED",
         "xray.sealed",
         "Should we check whether or not the player is inside a sealed room?",
         "XRAY_MAX_ORES",
         "xray.max_ores",
         "How many ores does the player need to mine within the above amount of minutes in order to fail this check?",
         "XRAY_ORES",
         "xray.ores",
         "IRON_ORE",
         "GOLD_ORE",
         "REDSTONE_ORE",
         "LAPIS_ORE",
         "EMERALD_ORE",
         "DIAMOND_ORE",
         "The blocks listed below will be considered as ores",
         "XRAY_MAX_VL",
         "xray.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "XRAY_COMMANDS",
         "xray.commands",
         "alice alert %player% Is using XRay",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "BARITONE",
         "baritone",
         "",
         "Baritone Check",
         "BARITONE_A",
         "baritone.a",
         "Should we enable this module?",
         "BARITONE_MAX_VL",
         "baritone.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "BARITONE_COMMANDS",
         "baritone.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "VEHICLE",
         "vehicle",
         "",
         "Vehicle Check",
         "VEHICLE_A",
         "vehicle.a",
         "Should we enable this module?",
         "VEHICLE_B",
         "vehicle.b",
         "Should we enable this module?",
         "VEHICLE_C",
         "vehicle.c",
         "Should we enable this module?",
         "VEHICLE_MAX_VL",
         "vehicle.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "VEHICLE_COMMANDS",
         "vehicle.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "REACH",
         "reach",
         "",
         "Reach Check",
         "REACH_A",
         "reach.a",
         "",
         "Reach A Module",
         "This will only work on real players",
         "This check uses a heavy but accurate math method in order to get the distance",
         "With a proper configuration this check can be very powerful",
         "REACH_A_ENABLED",
         "reach.a.enabled",
         "Should we enable this module?",
         "REACH_A_DELTA",
         "reach.a.delta",
         "How many past locations does this check need to use in order to calculate a proper distance?",
         "High delta: More locations, Accurate, Slower",
         "Low delta: Less locations, Can be inaccurate, Fast",
         "Do not set this value lower than 100 or higher than 200",
         "REACH_B",
         "reach.b",
         "Should we enable this module?",
         "This check will only detect blatant reach but it will work on all living entities",
         "REACH_MAX_REACH",
         "reach.max_reach",
         "The maximum reach",
         "REACH_MAX_VL",
         "reach.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "REACH_COMMANDS",
         "reach.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "VELOCITY",
         "velocity",
         "",
         "Velocity Check",
         "VELOCITY_A",
         "velocity.a",
         "Should we enable this module?",
         "VELOCITY_B",
         "velocity.b",
         "Should we enable this module?",
         "VELOCITY_C",
         "velocity.c",
         "Should we enable this module?",
         "VELOCITY_MAX_VL",
         "velocity.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "VELOCITY_COMMANDS",
         "velocity.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "HITBOX",
         "hitbox",
         "",
         "Hitbox Check",
         "HITBOX_A",
         "hitbox.a",
         "",
         "Hitbox A Module",
         "This check uses a raytrace implementation which is quite heavy but very accurate",
         "With a proper configuration this check can be very powerful",
         "HITBOX_A_ENABLED",
         "hitbox.a.enabled",
         "Should we enable this module?",
         "HITBOX_A_EXPAND",
         "hitbox.a.expand",
         "How much should we expand the Bounding Box to compensate?",
         "The default value is a magic value, It fixes any inconsistency that may occur",
         "HITBOX_A_MAX_DISTANCE",
         "hitbox.a.max_distance",
         "The maximum distance",
         "HITBOX_A_SENSITIVITY",
         "hitbox.a.sensitivity",
         "The sensitivity of this check",
         "0: Less accurate, Safe",
         "1: Medium accuracy, Usually safe",
         "2: High accuracy, Possibly unsafe",
         "Do not set this value higher than 2",
         "HITBOX_A_DELTA",
         "hitbox.a.delta",
         "How many past locations does this check need to use in order to calculate a proper offset and distance?",
         "High delta: More locations, Accurate, Slower",
         "Low delta: Less locations, Can be inaccurate, Fast",
         "Do not set this value lower than 100 or higher than 200",
         "HITBOX_B",
         "hitbox.b",
         "",
         "Hitbox B Module",
         "This check is lightweight, but less accurate",
         "HITBOX_B_ENABLED",
         "hitbox.b.enabled",
         "Should we enable this module?",
         "HITBOX_B_PING_COMPENSATION",
         "hitbox.b.ping_compensation",
         "How much should we compensate for laggy players?",
         "HITBOX_MAX_VL",
         "hitbox.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "HITBOX_COMMANDS",
         "hitbox.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "INVENTORY",
         "inventory",
         "",
         "Inventory Check",
         "INVENTORY_INVENTORYMOVE",
         "inventory.inventorymove",
         "Should we enable this module?",
         "INVENTORY_CHESTSTEALER",
         "inventory.cheststealer",
         "",
         "Inventory ChestStealer Module",
         "INVENTORY_CHESTSTEALER_ENABLED",
         "inventory.cheststealer.enabled",
         "Should we enable this module?",
         "INVENTORY_CHESTSTEALER_DELAY",
         "inventory.cheststealer.delay",
         "The minimum delay in milliseconds that a player needs to interact with an item in order to fail this check",
         "INVENTORY_MAX_VL",
         "inventory.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "INVENTORY_COMMANDS",
         "inventory.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "JESUS",
         "jesus",
         "",
         "Jesus Check",
         "JESUS_A",
         "jesus.a",
         "Should we enable this module?",
         "JESUS_B",
         "jesus.b",
         "Should we enable this module?",
         "JESUS_C",
         "jesus.c",
         "Should we enable this module?",
         "JESUS_D",
         "jesus.d",
         "Should we enable this module?",
         "JESUS_MAX_VL",
         "jesus.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "JESUS_COMMANDS",
         "jesus.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "NOFALL",
         "nofall",
         "",
         "NoFall Check",
         "NOFALL_A",
         "nofall.a",
         "Should we enable this module?",
         "NOFALL_B",
         "nofall.b",
         "Should we enable this module?",
         "NOFALL_MAX_VL",
         "nofall.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "NOFALL_COMMANDS",
         "nofall.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "BADPACKETS",
         "badpackets",
         "",
         "BadPackets Check",
         "This will also prevent some types of Anti Crashers",
         "BADPACKETS_A",
         "badpackets.a",
         "Should we enable this module?",
         "BADPACKETS_B",
         "badpackets.b",
         "Should we enable this module?",
         "BADPACKETS_C",
         "badpackets.c",
         "Should we enable this module?",
         "BADPACKETS_D",
         "badpackets.d",
         "Should we enable this module?",
         "BADPACKETS_E",
         "badpackets.e",
         "Should we enable this module?",
         "BADPACKETS_F",
         "badpackets.f",
         "Should we enable this module?",
         "BADPACKETS_G",
         "badpackets.g",
         "Should we enable this module?",
         "BADPACKETS_H",
         "badpackets.h",
         "Should we enable this module?",
         "BADPACKETS_I",
         "badpackets.i",
         "Should we enable this module?",
         "BADPACKETS_J",
         "badpackets.j",
         "Should we enable this module?",
         "BADPACKETS_K",
         "badpackets.k",
         "Should we enable this module?",
         "BADPACKETS_L",
         "badpackets.l",
         "Should we enable this module?",
         "BADPACKETS_M",
         "badpackets.m",
         "Should we enable this module?",
         "BADPACKETS_N",
         "badpackets.n",
         "Should we enable this module?",
         "BADPACKETS_O",
         "badpackets.o",
         "Should we enable this module?",
         "BADPACKETS_P",
         "badpackets.p",
         "Should we enable this module?",
         "BADPACKETS_Q",
         "badpackets.q",
         "Should we enable this module?",
         "BADPACKETS_R",
         "badpackets.r",
         "Should we enable this module?",
         "BADPACKETS_S",
         "badpackets.s",
         "Should we enable this module?",
         "BADPACKETS_T",
         "badpackets.t",
         "Should we enable this module?",
         "BADPACKETS_U",
         "badpackets.u",
         "Should we enable this module?",
         "BADPACKETS_V",
         "badpackets.v",
         "Should we enable this module?",
         "BADPACKETS_W",
         "badpackets.w",
         "Should we enable this module?",
         "BADPACKETS_X",
         "badpackets.x",
         "Should we enable this module?",
         "BADPACKETS_Y",
         "badpackets.y",
         "Should we enable this module?",
         "BADPACKETS_Z",
         "badpackets.z",
         "Should we enable this module?",
         "BADPACKETS_AA",
         "badpackets.aa",
         "Should we enable this module?",
         "BADPACKETS_BB",
         "badpackets.bb",
         "Should we enable this module?",
         "BADPACKETS_CC",
         "badpackets.cc",
         "Should we enable this module?",
         "BADPACKETS_DD",
         "badpackets.dd",
         "Should we enable this module?",
         "BADPACKETS_MAX_VL",
         "badpackets.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "BADPACKETS_COMMANDS",
         "badpackets.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "PLAYERESP",
         "playeresp",
         "",
         "Player ESP Check",
         "PLAYERESP_ENABLED",
         "playeresp.enabled",
         "Should we enable this check?",
         "PLAYERESP_RADIUS",
         "playeresp.radius",
         "The radius to check for entities around the player",
         "PLAYERESP_INTERVAL",
         "playeresp.ticks",
         "In how many movement ticks should we hide other players around the player that he's unable to see?",
         "This is a heavy check, So i would suggest setting a high tick value",
         "INTERACT",
         "interact",
         "",
         "Interact Check",
         "INTERACT_FASTBREAK",
         "interact.fastbreak",
         "",
         "FastBreak Module",
         "INTERACT_FASTBREAK_ENABLED",
         "interact.fastbreak.enabled",
         "Should we enable this module?",
         "INTERACT_FASTBREAK_MIN_DIFFERENCE",
         "interact.fastbreak.min_difference",
         "The minimum break time difference a player needs to reach in order to fail this check",
         "INTERACT_FASTPLACE",
         "interact.fastplace",
         "Should we enable this module?",
         "INTERACT_INVALID",
         "interact.invalid",
         "Should we enable this module?",
         "INTERACT_GHOSTHAND",
         "interact.ghosthand",
         "",
         "GhostHand Module",
         "INTERACT_GHOSTHAND_ENABLED",
         "interact.ghosthand.enabled",
         "Should we enable this module?",
         "INTERACT_GHOSTHAND_BLOCKS",
         "interact.ghosthand.blocks",
         "BED_BLOCK",
         "The block types this check will work on",
         "INTERACT_MAX_VL",
         "interact.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "INTERACT_COMMANDS",
         "interact.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "SCAFFOLD",
         "scaffold",
         "",
         "Scaffold Check",
         "SCAFFOLD_A",
         "scaffold.a",
         "Should we enable this module?",
         "SCAFFOLD_B",
         "scaffold.b",
         "Should we enable this module?",
         "SCAFFOLD_C",
         "scaffold.c",
         "Should we enable this module?",
         "SCAFFOLD_D",
         "scaffold.d",
         "Should we enable this module?",
         "SCAFFOLD_E",
         "scaffold.e",
         "Should we enable this module?",
         "SCAFFOLD_F",
         "scaffold.f",
         "Should we enable this module?",
         "SCAFFOLD_G",
         "scaffold.g",
         "Should we enable this module?",
         "SCAFFOLD_MAX_VL",
         "scaffold.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "SCAFFOLD_COMMANDS",
         "scaffold.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "TIMER",
         "timer",
         "",
         "Timer Check",
         "TIMER_A",
         "timer.a",
         "Should we enable this module?",
         "TIMER_B",
         "timer.b",
         "Should we enable this module?",
         "TIMER_MAX_VL",
         "timer.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "TIMER_COMMANDS",
         "timer.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "FASTCLIMB",
         "fastclimb",
         "",
         "FastClimb Check",
         "FASTCLIMB_ENABLED",
         "fastclimb.enabled",
         "Should we enable this check?",
         "FASTCLIMB_MAX_VL",
         "fastclimb.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "FASTCLIMB_COMMANDS",
         "fastclimb.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "ELYTRA",
         "elytra",
         "",
         "Elytra Check",
         "ELYTRA_A",
         "elytra.a",
         "Should we enable this module?",
         "ELYTRA_B",
         "elytra.b",
         "Should we enable this module?",
         "ELYTRA_EJECT",
         "elytra.eject",
         "Should we eject players when they get close to flagging?",
         "It is suggested to leave this option enabled since it can sometimes help prevent issues such as desync",
         "ELYTRA_MAX_VL",
         "elytra.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "ELYTRA_COMMANDS",
         "elytra.commands",
         "alice punish %player% Unfair Advantage",
         "The commands that will get executed once a player reaches the maximum violation amount",
         "AUTOFISH",
         "autofish",
         "",
         "AutoFish Check",
         "AUTOFISH_ENABLED",
         "autofish.enabled",
         "Should we enable this check?",
         "AUTOFISH_A",
         "autofish.a",
         "Should we enable this module?",
         "AUTOFISH_MAX_VL",
         "autofish.max_vl",
         "The maximum violation amount a player needs to reach in order to get punished",
         "AUTOFISH_COMMANDS",
         "autofish.commands",
         "alice alert %player% Is using AutoFish",
         "The commands that will get executed once a player reaches the maximum violation amount"
      };
   }
}
