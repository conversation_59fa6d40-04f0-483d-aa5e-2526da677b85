package me.nik.alice;

import java.io.File;

public class Ww {
   private final Alice plugin;
   private static boolean exists;
   private D8 dx;
   private static final String[] Aa = new String[]{Ww.dl[1], Ww.dl[2], Ww.dl[3], Ww.dl[4], Ww.dl[5], Ww.dl[6], Ww.dl[7], Ww.dl[8], Ww.dl[9]};
   private static String[] dl;

   private static void Ax() {
      dl = new String[]{
         "checks.yml",
         "+----------------------------------------------------------------------------------------------+",
         "|                                                                                              |",
         "|                                              Alice                                           |",
         "|                                                                                              |",
         "|                               Discord: https://discord.gg/m7j2Y9H                            |",
         "|                                                                                              |",
         "|                                           Author: Nik                                        |",
         "|                                                                                              |",
         "+----------------------------------------------------------------------------------------------+"
      };
   }

   public Ww(Alice var1) {
      this.plugin = var1;
   }

   public void tm() {
      File var1;
      exists = (var1 = new File(this.plugin.getDataFolder(), dl[0])).exists();
      boolean var2;
      boolean var3 = (boolean)(var2 = !var1.exists());
      this.dx = D8.dx(this.plugin, var1);
      if (var2) {
         this.dx.dx(Aa);
      }

      me.nik.alice.Ww.dx[] var6;
      var2 = (var6 = me.nik.alice.Ww.dx.values()).length;

      for (int var4 = 0; var4 < var2; var4++) {
         me.nik.alice.Ww.dx var5;
         (var5 = var6[var4]).reset();
         var3 |= me.nik.alice.Ww.dx.dx(var5, this.dx);
      }

      if (var3) {
         this.dx.hW();
      }
   }

   static boolean sB() {
      return exists;
   }

   public void reset() {
      boolean var5 = EhNP;
      me.nik.alice.Ww.dx[] var1;
      int var2 = (var1 = me.nik.alice.Ww.dx.values()).length;
      int var3 = 0;

      while (var3 < var2) {
         var1[var3].reset();
         var3++;
         if (var5) {
            throw null;
         }
      }
   }

   static {
      Ax();
   }

   public D8 Aa() {
      return this.dx;
   }
}
