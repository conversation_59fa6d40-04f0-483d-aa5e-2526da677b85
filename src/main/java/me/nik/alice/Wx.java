package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Wx extends PacketCheck {
   private static String[] t7;
   private int yM;

   private static void ic() {
      t7 = new String[]{"N", "Checks for invalid abilities packets", "Sent abilities packet without being allowed to fly"};
   }

   public Wx(UC var1) {
      super(var1, CheckType.PACKET, t7[0], Category.WORLD);
   }

   @Override
   public String sB() {
      return t7[1];
   }

   static {
      ic();
   }

   @Override
   public void dx(DH var1) {
      if (var1.y()) {
         if (this.yM++ >= 2 && !this.Aa.getPlayer().getAllowFlight() && !this.Aa.getPlayer().isFlying()) {
            this.og(t7[2]);
         }
      } else {
         if (var1.qx()) {
            this.yM = 0;
         }
      }
   }
}
