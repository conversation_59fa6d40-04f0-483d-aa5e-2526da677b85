package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;
import org.bukkit.Difficulty;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;

public class Xm extends PacketCheck {
   private int zP;
   private int hW;
   private int x;
   private int Ch;
   private static String[] 5f;

   private static void ge() {
      5f = new String[]{
         "NoSlow",
         "Checks for invalid deceleration when using items",
         "Blocking, Last delta XZ: ",
         " limit: ",
         "Web, Last delta XZ: ",
         " limit: ",
         "Sneaking, Last delta XZ: ",
         " limit: ",
         "Soul Sand, Last delta XZ: ",
         " limit: ",
         "HungerSprint",
         "Consumed, Last delta XZ: ",
         " limit: ",
         "Bow, Last delta XZ: ",
         " limit: "
      };
   }

   @Override
   public String sB() {
      return 5f[1];
   }

   public Xm(UC var1) {
      super(var1, CheckType.SPEED, 5f[0], Category.MOVE, 3.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()) {
         ES var13 = this.Aa.dx();
         uq var2;
         if ((var2 = this.Aa.dx()).sX() >= 60
            && var13.P() >= 60
            && !this.Aa.dx().o4()
            && !var13.dx().DP()
            && var2.WB() >= 60
            && var2.gz() >= 60
            && var13.pQ() >= 60
            && var2.Vm() >= 60
            && !this.Aa.dx().tr()) {
            Block var3;
            if ((var3 = fD.dx(var13.Aa())) != null) {
               Player var4 = this.Aa.getPlayer();
               String var16 = var3.getType().toString();
               if (var4.isBlocking()) {
                  this.x++;
               } else {
                  this.x = 0;
               }

               if (var2.iv()) {
                  this.zP++;
               } else {
                  this.zP = 0;
               }

               if (!Integer.valueOf(85812).equals(var16.hashCode()) && !Integer.valueOf(1993163294).equals(var16.hashCode())) {
                  this.hW = 0;
               } else {
                  this.hW++;
               }

               if (!Integer.valueOf(942687056).equals(var16.hashCode()) || w.N7() && JA.dx(var4.getInventory().getBoots(), Enchantment.SOUL_SPEED) > 0) {
                  this.Ch = 0;
               } else {
                  this.Ch++;
               }

               double var8 = var13.Zp();
               boolean var17 = var13.tk() < 5 && var13.R() < 5;
               double var11 = (this.hW >= 15 ? 0.1 : (var17 ? 0.39 : 0.12)) + (double)(FastMath.max(0.2F, this.Aa.getPlayer().getWalkSpeed()) - 0.2F);
               if (var13.p6() < 60) {
                  var11 += var17 ? 0.41 : 0.05;
               }

               if (var13.yk() < 10) {
                  var11 += 0.75;
               }

               if (this.Ch >= 3) {
                  var11 += 0.05;
               }

               int var14;
               if ((var14 = var13.dx().sB(2000L)) > 0) {
                  var11 += 0.05 * (double)var14;
               }

               if (var8 > var11) {
                  boolean var15 = false;
                  boolean var18 = false;
                  if (this.x >= 15) {
                     this.Aa(5f[2] + var8 + 5f[3] + var11);
                     var18 = true;
                     var15 = true;
                  }

                  if (this.hW >= 15) {
                     this.Aa(5f[4] + var8 + 5f[5] + var11);
                     var18 = true;
                  }

                  if (this.zP >= 15) {
                     this.Aa(5f[6] + var8 + 5f[7] + var11);
                     var18 = true;
                  }

                  if (this.Ch >= 15) {
                     this.Aa(5f[8] + var8 + 5f[9] + var11);
                     var18 = true;
                  }

                  if (var4.getFoodLevel() <= 3 && var2.Ch() && var4.getWorld().getDifficulty() != Difficulty.PEACEFUL) {
                     this.Aa(5f[10]);
                     var18 = true;
                  }

                  if (var8 > var11 + 0.1) {
                     if (var2.tm() == 0) {
                        this.Aa(5f[11] + var8 + 5f[12] + var11);
                        var18 = true;
                     }

                     if (var2.b() == 0) {
                        this.Aa(5f[13] + var8 + 5f[14] + var11);
                        var18 = true;
                     }
                  }

                  if (var18) {
                     if (var15) {
                        gE.h5(var4);
                        return;
                     }

                     if (this.Aa() > this.dx()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.01);
                  }
               }
            }
         }
      }
   }

   static {
      ge();
   }
}
