package me.nik.alice;

import java.util.Map;
import java.util.UUID;
import java.util.concurrent.ConcurrentHashMap;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

public class Xp {
   private final Map Aa = new ConcurrentHashMap();

   public void gz() {
      this.Aa.clear();
   }

   public Map dx() {
      return this.Aa;
   }

   public void dC(Player var1) {
      UUID var2 = var1.getUniqueId();
      if (this.Aa.containsKey(var2)) {
         this.Aa.remove(var2);
      }
   }

   public void sB(Player var1) {
      this.Aa.put(var1.getUniqueId(), new UC(var1));
   }

   public UC dx(Player var1) {
      return (UC)this.Aa.get(var1.getUniqueId());
   }

   public void dC() {
      Bukkit.getOnlinePlayers().forEach(this::sB);
   }
}
