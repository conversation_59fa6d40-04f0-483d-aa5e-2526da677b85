package me.nik.alice;

import java.util.List;
import java.util.Map;
import java.util.Set;
import org.bukkit.Color;
import org.bukkit.Location;
import org.bukkit.OfflinePlayer;
import org.bukkit.configuration.Configuration;
import org.bukkit.configuration.ConfigurationSection;
import org.bukkit.configuration.serialization.ConfigurationSerializable;
import org.bukkit.inventory.ItemStack;
import org.bukkit.util.Vector;

public class Y implements ConfigurationSection {
   protected ConfigurationSection dx;

   public List getByteList(String var1) {
      return this.dx.getByteList(var1);
   }

   public ConfigurationSerializable getSerializable(String var1, Class var2, ConfigurationSerializable var3) {
      return this.dx.getSerializable(var1, var2, var3);
   }

   public boolean isInt(String var1) {
      return this.dx.isInt(var1);
   }

   public Vector getVector(String var1, Vector var2) {
      return this.dx.getVector(var1, var2);
   }

   public List getIntegerList(String var1) {
      return this.dx.getIntegerList(var1);
   }

   public OfflinePlayer getOfflinePlayer(String var1) {
      return this.dx.getOfflinePlayer(var1);
   }

   public boolean isColor(String var1) {
      return this.dx.isColor(var1);
   }

   public String getString(String var1, String var2) {
      return this.dx.getString(var1, var2);
   }

   public Y dx(String var1, Map var2) {
      return new Y(this.dx.createSection(var1, var2));
   }

   public boolean isVector(String var1) {
      return this.dx.isVector(var1);
   }

   public Configuration getRoot() {
      return this.dx.getRoot();
   }

   public boolean getBoolean(String var1, boolean var2) {
      return this.dx.getBoolean(var1, var2);
   }

   public ItemStack getItemStack(String var1) {
      return this.dx.getItemStack(var1);
   }

   public boolean getBoolean(String var1) {
      return this.dx.getBoolean(var1);
   }

   public List getStringList(String var1) {
      return this.dx.getStringList(var1);
   }

   public Y Aa(String var1) {
      ConfigurationSection var2;
      ConfigurationSection var10000 = var2 = this.dx.getConfigurationSection(var1);

      try {
         var10000.hashCode();
      } catch (Exception var3) {
         return this.dx(var1);
      }

      return new Y(var2);
   }

   public OfflinePlayer getOfflinePlayer(String var1, OfflinePlayer var2) {
      return this.dx.getOfflinePlayer(var1, var2);
   }

   public Object getObject(String var1, Class var2, Object var3) {
      return this.dx.getObject(var1, var2, var3);
   }

   public List getList(String var1) {
      return this.dx.getList(var1);
   }

   public List getLongList(String var1) {
      return this.dx.getLongList(var1);
   }

   public int getInt(String var1, int var2) {
      return this.dx.getInt(var1, var2);
   }

   public boolean contains(String var1) {
      return this.dx.contains(var1);
   }

   public long getLong(String var1, long var2) {
      return this.dx.getLong(var1, var2);
   }

   public String getName() {
      return this.dx.getName();
   }

   public List getList(String var1, List var2) {
      return this.dx.getList(var1, var2);
   }

   public Y(ConfigurationSection var1) {
      this.dx = var1;
   }

   public boolean isConfigurationSection(String var1) {
      return this.dx.isConfigurationSection(var1);
   }

   public boolean isItemStack(String var1) {
      return this.dx.isItemStack(var1);
   }

   public Color getColor(String var1, Color var2) {
      return this.dx.getColor(var1, var2);
   }

   public int getInt(String var1) {
      return this.dx.getInt(var1);
   }

   public boolean isBoolean(String var1) {
      return this.dx.isBoolean(var1);
   }

   public void set(String var1, Object var2) {
      this.dx.set(var1, var2);
   }

   public ConfigurationSerializable getSerializable(String var1, Class var2) {
      return this.dx.getSerializable(var1, var2);
   }

   public Color getColor(String var1) {
      return this.dx.getColor(var1);
   }

   public boolean isDouble(String var1) {
      return this.dx.isDouble(var1);
   }

   public Map getValues(boolean var1) {
      return this.dx.getValues(var1);
   }

   public String getCurrentPath() {
      return this.dx.getCurrentPath();
   }

   public Y dx(String var1) {
      return new Y(this.dx.createSection(var1));
   }

   public Location getLocation(String var1, Location var2) {
      return (Location)this.getSerializable(var1, Location.class, var2);
   }

   public ItemStack getItemStack(String var1, ItemStack var2) {
      return this.dx.getItemStack(var1, var2);
   }

   public Vector getVector(String var1) {
      return this.dx.getVector(var1);
   }

   public Boolean dx(String var1, Boolean var2) {
      Object var4;
      if ((var4 = this.get(var1)) instanceof Boolean) {
         return (Boolean)var4;
      } else if (var4 instanceof String && Integer.valueOf(-2032180703).equals(((String)var4).toUpperCase().hashCode())) {
         return null;
      } else {
         Object var10000 = var4;

         try {
            var10000.toString();
            return null;
         } catch (Exception var3) {
            return var2;
         }
      }
   }

   public List getDoubleList(String var1) {
      return this.dx.getDoubleList(var1);
   }

   public Object get(String var1, Object var2) {
      return this.dx.get(var1, var2);
   }

   public Object get(String var1) {
      return this.dx.get(var1);
   }

   public Location getLocation(String var1) {
      return (Location)this.getSerializable(var1, Location.class);
   }

   public Object getObject(String var1, Class var2) {
      return this.dx.getObject(var1, var2);
   }

   public String getString(String var1) {
      return this.dx.getString(var1);
   }

   public List getBooleanList(String var1) {
      return this.dx.getBooleanList(var1);
   }

   public boolean contains(String var1, boolean var2) {
      return this.dx.contains(var1, var2);
   }

   public boolean isSet(String var1) {
      return this.dx.isSet(var1);
   }

   public ConfigurationSection createSection(String var1) {
      return this.dx(var1);
   }

   public boolean isList(String var1) {
      return this.dx.isList(var1);
   }

   public boolean isOfflinePlayer(String var1) {
      return this.dx.isOfflinePlayer(var1);
   }

   public List getFloatList(String var1) {
      return this.dx.getFloatList(var1);
   }

   public void addDefault(String var1, Object var2) {
      this.dx.addDefault(var1, var2);
   }

   public double getDouble(String var1, double var2) {
      return this.dx.getDouble(var1, var2);
   }

   public ConfigurationSection getDefaultSection() {
      return this.dx();
   }

   public List getShortList(String var1) {
      return this.dx.getShortList(var1);
   }

   public List getCharacterList(String var1) {
      return this.dx.getCharacterList(var1);
   }

   public long getLong(String var1) {
      return this.dx.getLong(var1);
   }

   public boolean isLong(String var1) {
      return this.dx.isLong(var1);
   }

   public ConfigurationSection getParent() {
      return this.dx.getParent();
   }

   public Boolean dx(String var1) {
      if (this.isBoolean(var1)) {
         return this.getBoolean(var1);
      } else {
         if (this.isString(var1)) {
            String var3;
            String var10000 = var3 = this.getString(var1);

            try {
               var10000.hashCode();
            } catch (Exception var2) {
               return null;
            }

            if (Integer.valueOf(-2032180703).equals(var3.toUpperCase().hashCode())) {
               return null;
            }
         }

         return null;
      }
   }

   public Y dx() {
      return new Y(this.dx.getDefaultSection());
   }

   public ConfigurationSection createSection(String var1, Map var2) {
      return this.dx(var1, var2);
   }

   public ConfigurationSection getConfigurationSection(String var1) {
      return this.Aa(var1);
   }

   public double getDouble(String var1) {
      return this.dx.getDouble(var1);
   }

   public boolean isLocation(String var1) {
      ConfigurationSerializable var10000 = this.getSerializable(var1, Location.class);

      try {
         var10000.toString();
         return true;
      } catch (Exception var2) {
         return false;
      }
   }

   public boolean isString(String var1) {
      return this.dx.isString(var1);
   }

   public List getMapList(String var1) {
      return this.dx.getMapList(var1);
   }

   public Set getKeys(boolean var1) {
      return this.dx.getKeys(var1);
   }
}
