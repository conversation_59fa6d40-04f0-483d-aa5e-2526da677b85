package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class YL extends PacketCheck {
   private static String[] 6U;

   @Override
   public String sB() {
      return 6U[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().dx().DP() && !this.Aa.dx().jA() && this.Aa.dx().dx().og(1000L) <= 0 && this.Aa.dx().R2() >= 20) {
         ES var13;
         double var3 = (var13 = this.Aa.dx()).Ch();
         boolean var2 = var13.tk();
         int var5 = var13.u() && var2;
         boolean var6 = this.Aa.dx().tr();
         if (var3 >= 0.6 && var5 && !var6) {
            this.og(6U[2].concat(String.valueOf(var3)));
         }

         var5 = var13.Q();
         double var9 = var13.iv();
         if (var3 <= -0.9 && var9 > -0.1 && var5 >= 0 && var5 < 3 && !var6) {
            this.og(6U[3] + var3 + 6U[4] + var9);
         }

         double var11;
         if ((var11 = var13.Qt()) >= 0.9 && var2 && var3 >= 0.9) {
            this.og(6U[5] + var11 + 6U[6] + var3);
         }
      }
   }

   private static void Vj() {
      6U = new String[]{"H", "Checks for step", "Step, Delta Y: ", "Reverse Step, Delta Y: ", " last delta Y: ", "Step, Accel: ", " Delta Y: "};
   }

   static {
      Vj();
   }

   public YL(UC var1) {
      super(var1, CheckType.MOTION, 6U[0], Category.MOVE);
   }
}
