package me.nik.alice;

public enum ZM {
   dx(ZM.A8[1]),
   Aa(ZM.A8[3]),
   og(ZM.A8[5]),
   AC(ZM.A8[7]),
   sB(ZM.A8[9]),
   dC(ZM.A8[11]),
   Zh(ZM.A8[13]),
   h5(ZM.A8[15]),
   Fh(ZM.A8[17]),
   b(ZM.A8[19]),
   VL(ZM.A8[21]),
   UH(ZM.A8[23]),
   yM(ZM.A8[25]),
   sX(ZM.A8[27]),
   WB(ZM.A8[29]),
   Vm(ZM.A8[31]),
   gz(ZM.A8[33]),
   tm(ZM.A8[35]),
   x(ZM.A8[37]),
   zP(ZM.A8[39]);

   private final String VL;
   private static final ZM[] dx;
   private static String[] A8;

   private ZM(String var3) {
      this.VL = var3;
   }

   public String h5() {
      return this.VL;
   }

   static {
      tD();
      dx = new ZM[]{dx, Aa, og, AC, sB, dC, Zh, h5, Fh, b, VL, UH, yM, sX, WB, Vm, gz, tm, x, zP};
   }

   private static void tD() {
      A8 = new String[]{
         "ADMIN",
         "alice.admin",
         "BYPASS",
         "alice.bypass",
         "COMMAND_CHECKS",
         "alice.commands.checks",
         "COMMAND_THEMES",
         "alice.commands.themes",
         "COMMAND_LOGS",
         "alice.commands.logs",
         "COMMAND_ALERTS",
         "alice.commands.alerts",
         "COMMAND_DEBUG",
         "alice.commands.debug",
         "COMMAND_INFO",
         "alice.commands.info",
         "COMMAND_LAG",
         "alice.commands.lag",
         "COMMAND_MENU",
         "alice.commands.menu",
         "COMMAND_PUNISH",
         "alice.commands.punish",
         "COMMAND_RELOAD",
         "alice.commands.reload",
         "COMMAND_TEMPBYPASS",
         "alice.commands.tempbypass",
         "COMMAND_PROFILER",
         "alice.commands.profiler",
         "COMMAND_SPECTATE",
         "alice.commands.spectate",
         "COMMAND_VL",
         "alice.commands.vl",
         "COMMAND_KICK",
         "alice.commands.kick",
         "GUI_CHECKS",
         "alice.gui.checks",
         "GUI_THEMES",
         "alice.gui.themes",
         "COMMAND_VERBOSE",
         "alice.commands.verbose"
      };
   }
}
