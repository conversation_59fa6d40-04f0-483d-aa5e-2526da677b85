package me.nik.alice;

import com.gmail.nossr50.api.AbilityAPI;
import org.bukkit.entity.Player;

public class ZO extends f2 {
   private static String[] hV;

   public ZO() {
      super(hV[0]);
   }

   @Override
   public boolean Aa(Player var1) {
      return false;
   }

   static {
      bQ();
   }

   private static void bQ() {
      hV = new String[]{"McMMO"};
   }

   @Override
   public boolean og(Player var1) {
      return false;
   }

   @Override
   public boolean dx(Player var1) {
      return AbilityAPI.gigaDrillBreakerEnabled(var1) || AbilityAPI.superBreakerEnabled(var1) || AbilityAPI.treeFellerEnabled(var1);
   }
}
