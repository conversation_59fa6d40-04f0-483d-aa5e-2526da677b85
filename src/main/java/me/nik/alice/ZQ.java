package me.nik.alice;

import java.util.Collection;
import java.util.Comparator;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import me.nik.fastmath.FastMath;

public class ZQ {
   private double Sh;
   private final V0 Fh = new V0(30);
   private float hW;
   private float zP;
   private final V0 VL;
   private double pQ;
   private final V0 b = new V0(40, true);
   private double jD;
   private double jA;
   private final UC Aa;
   private float Ch;
   private long yk;
   private double R2;
   private long pQ;
   private long Sh;
   private double yk;

   private static int dx(float var0, float var1) {
      float var2 = var0 / var1;
      var0 = Math.max(var0, var1) * 0.001F;
      return (int)(var2 + var0);
   }

   private static double Zh(double var0) {
      return (double)(og(var0) * 0.6F) + 0.2;
   }

   public boolean h5() {
      return this.b.h5() && this.VL.h5();
   }

   private void M3() {
      qn var1;
      float var2;
      if (!((var2 = (var1 = this.Aa.dx()).UH()) >= 20.0F) && !(var2 < 1.0F) && Math.abs(var1.getPitch()) != 90.0F) {
         this.Fh.add(var2);
         if (this.Fh.h5()) {
            float var8;
            if (!l.yM(var8 = dx(this.Fh))) {
               long var6;
               if ((
                        var6 = (long)(
                           l.Aa((double)((float)((l.Aa((double)((float)FastMath.exp(FastMath.log((double)var8 / 0.15 / 8.0) / 3.0)), 5) - 0.2) / 0.6)), 5)
                              * 200.0
                        )
                     )
                     > 0L
                  && var6 <= 200L) {
                  if (var6 == 99L) {
                     var6 = 100L;
                  }

                  this.yk = var6;
                  this.Ch = (Float)Rk.Aa().get(var6);
               } else {
                  this.Ch = -1.0F;
               }
            }
         }
      }
   }

   public float tm() {
      return this.zP;
   }

   private static double Aa(double var0) {
      return FastMath.pow(dC(var0), 3) * 8.0;
   }

   private static double AC(double var0) {
      boolean var3 = vfbb;
      byte var10000;
      if (var0 >= 0.0) {
         var10000 = 1;
         if (var3) {
            throw null;
         }
      } else {
         var10000 = -1;
      }

      byte var2 = var10000;
      return var0 / 0.15 / (double)var2;
   }

   public long yM() {
      return this.Sh;
   }

   private static jx dx(Map var0, Number var1) {
      return new jx(var1, (Integer)var0.get(var1));
   }

   private static double og(double var0) {
      return var0 / 0.15;
   }

   public float x() {
      return this.hW;
   }

   public long sX() {
      return this.yk;
   }

   public long UH() {
      return this.pQ;
   }

   public double u() {
      return this.R2;
   }

   private static Number dx(Collection var0) {
      boolean var3 = vfbb;
      HashMap var1 = new HashMap();

      for (Number var2 : var0) {
         var1.put(var2, (Integer)var1.getOrDefault(var2, 0) + 1);
         if (var3) {
            throw null;
         }
      }

      return (Number)((jx)var1.keySet()
            .stream()
            .map(ZQ::dx)
            .max(Comparator.comparing(jx::Aa, Comparator.naturalOrder()))
            .orElseThrow(NullPointerException::new))
         .dx();
   }

   private static float dx(float var0, float var1) {
      if ((double)var0 <= 1.0E-4) {
         return var1;
      } else {
         int var2 = dx(var1, var0);
         float var3;
         if (Math.abs(var3 = (var1 / var0 - (float)var2) * var0) < Math.max(var0, var1) * 0.001F) {
            var3 = 0.0F;
         }

         return dx(var3, var0);
      }
   }

   private static double sB(double var0) {
      return (double)((float)FastMath.pow(Zh(var0), 3) * 8.0F);
   }

   public void xx() {
      this.Fh.clear();
      this.yk = -1L;
      this.Ch = -1.0F;
   }

   public float zP() {
      return this.Ch;
   }

   public double Ch() {
      return this.jA;
   }

   public double tm() {
      return this.jD;
   }

   private static int dx(double var0, double var2) {
      return FastMath.floorInt(og(var0) / Aa(var2));
   }

   private static float og(double var0) {
      return (float)((FastMath.cbrt(AC(var0) / 8.0) - 0.2F) / 0.6F);
   }

   public double Eu() {
      return this.yk;
   }

   public double nZ() {
      return this.pQ;
   }

   public ZQ(UC var1) {
      this.VL = new V0(40, true);
      this.yk = -1L;
      this.Ch = -1.0F;
      this.Aa = var1;
   }

   private static float AC(double var0) {
      return (float)((FastMath.cbrt(og(var0) / 8.0) - 0.2F) / 0.6F);
   }

   private static float dx(List var0) {
      float var1 = (Float)var0.toArray()[0];

      for (int var2 = 1; var2 < var0.size(); var2++) {
         var1 = dx((Float)var0.toArray()[var2], var1);
      }

      return var1;
   }

   private static int Aa(double var0, double var2) {
      return FastMath.floorInt(AC(var0) / sB(var2));
   }

   private static double dC(double var0) {
      return (double)(AC(var0) * 0.6F) + 0.2;
   }

   public void yk() {
      qn var1;
      float var2 = (var1 = this.Aa.dx()).b();
      float var3 = var1.UH();
      float var4 = var1.VL();
      float var10 = var1.yM();
      this.Sh = l.dx(var2, var4);
      this.pQ = l.dx(var3, var10);
      this.pQ = (double)this.Sh / 1.6777216E7;
      this.yk = (double)this.pQ / 1.6777216E7;
      this.R2 = (double)((int)((double)var2 / this.pQ));
      this.Sh = (double)((int)((double)var3 / this.yk));
      if (this.Sh > 90000L && (double)this.Sh < 2.0E7 && this.pQ > 0.01F && var2 < 8.0F) {
         this.VL.add(this.pQ);
      }

      if (this.pQ > 90000L && (double)this.pQ < 2.0E7 && var3 < 8.0F) {
         this.b.add(this.yk);
      }

      if (this.VL.size() > 3 && this.b.size() > 3) {
         double var6 = (Double)dx(this.VL);
         double var8 = (Double)dx(this.b);
         this.zP = AC(var6);
         this.hW = og(var8);
         this.jD = (double)dx((double)var2, (double)((float)var6));
         this.jA = (double)Aa((double)var3, (double)((float)var8));
      }

      this.M3();
   }

   public double Q() {
      return this.Sh;
   }
}
