package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class Zb extends PacketCheck {
   private static String[] 9e;

   @Override
   public void dx(DH var1) {
      if (var1.Lh()) {
         ES var10 = this.Aa.dx();
         uq var2;
         if ((var2 = this.Aa.dx()).Vm() >= 60
            && !this.Aa.dx().o4()
            && var10.Sh() >= 60
            && var10.P() >= 60
            && var10.p6() >= 60
            && var10.pQ() >= 60
            && var2.sX() >= 60
            && var2.gz() >= 60
            && var10.nZ() >= 60
            && var2.Zp() >= 60
            && var2.WB() >= 60
            && !var10.jA()
            && var10.Eu() >= 60
            && !var10.rc()
            && !this.Aa.dx().tr()) {
            double var4 = (double)JA.Aa(this.Aa) - 0.108;
            double var6 = (double)JA.dx(this.Aa);
            double var8 = var10.F7();
            boolean var11 = var10.tk() > 8;
            int var12 = var2.hW();
            if (var11 && var8 > var4 || var8 > var6 || this.Aa.dx().Qt() == 0 || var12 > 5) {
               this.og(9e[2] + var8 + 9e[3] + var12);
            }
         }
      }
   }

   static {
      T4();
   }

   public Zb(UC var1) {
      super(var1, CheckType.INVENTORY, 9e[0], Category.WORLD);
   }

   private static void T4() {
      9e = new String[]{"InventoryMove", "Checks for inventory interactions while moving", "Delta XZ: ", " sprinting ticks: "};
   }

   @Override
   public String sB() {
      return 9e[1];
   }
}
