package me.nik.alice;

import java.util.List;
import org.bukkit.command.CommandSender;

public abstract class Zc {
   protected abstract String Zh();

   protected abstract boolean AC();

   protected abstract int dx();

   protected abstract String h5();

   protected abstract void dx(CommandSender var1, String[] var2);

   protected abstract String getName();

   protected abstract String getDescription();

   protected abstract List dx(CommandSender var1, String[] var2);
}
