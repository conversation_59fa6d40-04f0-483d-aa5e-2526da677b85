package me.nik.alice;

import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.Base64;
import javax.crypto.BadPaddingException;
import javax.crypto.Cipher;
import javax.crypto.IllegalBlockSizeException;
import javax.crypto.NoSuchPaddingException;
import javax.crypto.spec.SecretKeySpec;

public final class Zh {
   protected static final String URL = "mJ0FWIjPl5C1cJsRZT7Qsd7pyYKG3Ee8wHTN4SncRMpyFddde62AO/x4ij9DHdQhY5zzCSuKbc1p6PxjX1nNULkTRgdcLkBqXxpxx08AFH8=";
   private static String[] bk;
   private static final byte[] dx = new byte[]{67, 111, 102, 102, 101, 101, 76, 111, 118, 101, 114, 115, 33, 33, 60, 51};

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   protected static String Aa(String var0) {
      SecretKeySpec var10000 = new SecretKeySpec;
      SecretKeySpec var10001 = var10000;
      byte[] var10002 = dx;

      label51: {
         SecretKeySpec var1;
         try {
            var10001.<init>(var10002, bk[0]);
            var1 = var10000;
         } catch (InvalidKeyException | NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException | NoSuchAlgorithmException var8) {
            var11 = var8;
            boolean var14 = false;
            break label51;
         }

         Cipher var2;
         try {
            (var2 = Cipher.getInstance(bk[1])).init(2, var1);
         } catch (InvalidKeyException | NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException | NoSuchAlgorithmException var7) {
            var11 = var7;
            boolean var15 = false;
            break label51;
         }

         try {
            var12 = Base64.getDecoder();
         } catch (InvalidKeyException | NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException | NoSuchAlgorithmException var6) {
            var11 = var6;
            boolean var16 = false;
            break label51;
         }

         try {
            var9 = var12.decode(var0);
         } catch (InvalidKeyException | NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException | NoSuchAlgorithmException var5) {
            var11 = var5;
            boolean var17 = false;
            break label51;
         }

         try {
            var10 = var2.doFinal(var9);
         } catch (InvalidKeyException | NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException | NoSuchAlgorithmException var4) {
            var11 = var4;
            boolean var18 = false;
            break label51;
         }

         String var13 = new String;
         String var19 = var13;
         var10002 = var10;

         try {
            var19.<init>(var10002);
            return var13;
         } catch (InvalidKeyException | NoSuchPaddingException | BadPaddingException | IllegalBlockSizeException | NoSuchAlgorithmException var3) {
            var11 = var3;
            boolean var20 = false;
         }
      }

      var11.printStackTrace();
      return bk[2];
   }

   private Zh() {
   }

   static {
      bm();
   }

   private static void bm() {
      bk = new String[]{"AES", "AES", ""};
   }
}
