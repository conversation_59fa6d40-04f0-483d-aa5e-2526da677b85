package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class AimK extends PacketCheck {
   private static String[] bF;

   private static void L3() {
      bF = new String[]{"K", "Checks for impossible rotations", "Pitch: ", " Dy: ", " Ya: "};
   }

   static {
      L3();
   }

   @Override
   public String sB() {
      return bF[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.PU() && !this.Aa.dx().yM() && !(this.Aa.dx().F7() < 0.2) && !(this.Aa.dx().gz() <= 0.125)) {
         qn var4;
         float var2 = Math.abs((var4 = this.Aa.dx()).getPitch());
         float var3 = var4.b();
         float var5 = var4.sX();
         if (var2 < 0.1F && var3 > 0.1F && var5 <= 0.5F) {
            this.Aa(bF[2] + var2 + bF[3] + var3 + bF[4] + var5);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.og();
         }
      }
   }

   public AimK(UC var1) {
      super(var1, CheckType.AIM, bF[0], Category.COMBAT, 15.0F);
   }
}
