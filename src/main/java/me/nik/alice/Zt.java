package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;

public class Zt extends PacketCheck {
   private Location Aa;
   private int b;
   private long dx;
   private int VL;
   private static String[] 89;
   private final V0 sB = new V0(50);
   private float Aa;
   private int WB;

   @Override
   public String sB() {
      return 89[1];
   }

   private static void _V/* $VF was: 2V*/() {
      89 = new String[]{"B", "Checks for high accuracy or repeated fail rate", "Fail rate: ", "Fail rate: ", " dev: "};
   }

   public Zt(UC var1) {
      super(var1, CheckType.KILLAURA, 89[0], Category.COMBAT);
   }

   @Override
   public void dx(DH var1) {
      boolean var11 = b8kp;
      if (var1.i2()) {
         if (this.b++ >= 100) {
            int var2 = this.WB;
            this.WB = this.VL;
            if (this.VL > 90 && this.VL == var2 || this.VL >= 95) {
               this.og(89[2] + this.Aa);
            }

            this.reset();
         }

         if (this.b > 1) {
            float var14 = FastMath.min((float)this.VL / (float)this.b * 100.0F, 100.0F);
            this.Aa = 100.0F - var14;
            return;
         }
      } else if (var1.Tq()) {
         long var15 = l.og(this.dx);
         this.dx = var1.getTimeStamp();
         Entity var13;
         Entity var16 = var13 = this.Aa.dx().Aa();

         try {
            var16.equals(null);
         } catch (Exception var12) {
            return;
         }

         Location var4 = var13.getLocation();
         Location var17;
         if (this.Aa != null) {
            var17 = this.Aa;
            if (var11) {
               throw null;
            }
         } else {
            var17 = var4;
         }

         Location var5 = var17;
         this.Aa = var4;
         boolean var18;
         if (var13 instanceof Player && var13 == this.Aa.dx().og() && var15 <= 5000L) {
            var18 = false;
         } else {
            var18 = true;
            if (var11) {
               throw null;
            }
         }

         if (var18) {
            this.reset();
         }

         boolean var19;
         if (this.Aa.dx().Aa() instanceof Player
            && !(this.Aa.dx().F7() < 0.1)
            && var4.getWorld() == var5.getWorld()
            && !(var4.distanceSquared(var5) < 0.125)
            && !(this.Aa.dx().b() <= 0.1F)) {
            var19 = false;
         } else {
            var19 = true;
            if (var11) {
               throw null;
            }
         }

         if (var19) {
            return;
         }

         this.VL++;
         this.sB.add(this.Aa);
         double var9;
         if (this.sB.h5() && (var9 = l.sB(this.sB)) < 2.0 && this.VL >= 10 && this.VL <= 55) {
            this.og(89[3] + this.Aa + 89[4] + var9);
         }
      }
   }

   private void reset() {
      this.VL = 0;
      this.b = 0;
   }

   static {
      2V();
   }
}
