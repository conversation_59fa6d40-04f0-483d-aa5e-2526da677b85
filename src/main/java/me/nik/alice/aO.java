package me.nik.alice;

import java.util.List;
import org.bukkit.Bukkit;
import org.bukkit.GameMode;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerTeleportEvent.TeleportCause;

public class aO extends Zc {
   private static String[] cr;

   @Override
   protected void dx(CommandSender var1, String[] var2) {
      if (Integer.valueOf(var1.getName().toUpperCase().hashCode()).equals(var2[1].toUpperCase().hashCode())) {
         var1.sendMessage(F.dx.getMessage() + cr[3]);
      } else {
         Player var3;
         Player var10000 = var3 = Bukkit.getPlayer(var2[1]);

         try {
            var10000.toString();
         } catch (Exception var4) {
            var1.sendMessage(F.UH.getMessage().replace(cr[4], var2[1]));
            return;
         }

         Player var5;
         if ((var5 = (Player)var1).getGameMode() != GameMode.SPECTATOR) {
            var5.setGameMode(GameMode.SPECTATOR);
         }

         var5.teleport(var3.getLocation(), TeleportCause.PLUGIN);
      }
   }

   @Override
   protected String h5() {
      return ZM.WB.h5();
   }

   @Override
   protected String getName() {
      return cr[0];
   }

   @Override
   protected int dx() {
      return 2;
   }

   @Override
   protected String getDescription() {
      return cr[1];
   }

   @Override
   protected String Zh() {
      return cr[2];
   }

   static {
      v0();
   }

   @Override
   protected List dx(CommandSender var1, String[] var2) {
      return null;
   }

   private static void v0() {
      cr = new String[]{
         "spectate", "Spectate a player to see if he's possibly cheating", "/alice spectate <player>", "You cannot spectate yourself", "%player%"
      };
   }

   @Override
   protected boolean AC() {
      return false;
   }
}
