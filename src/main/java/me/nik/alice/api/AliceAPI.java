package me.nik.alice.api;

import java.util.Collection;
import me.nik.alice.api.custom.CheckData;
import org.bukkit.entity.Player;

public interface AliceAPI {
   void setTempBypass(Player var1, long var2);

   boolean isBypassing(Player var1);

   void updateBypass(Player var1);

   boolean isTempBypassing(Player var1);

   void punish(String var1, String var2);

   void sendAlert(String var1);

   void registerPunishAnimation(PunishAnimation var1);

   void flag(Player var1, String var2, String var3);

   void flag(Player var1, String var2, String var3, String var4);

   void setViolations(Player var1, String var2, String var3, int var4);

   int getViolations(Player var1, String var2);

   int getViolations(Player var1, String var2, String var3);

   Collection<CheckData> getCheckData(Player var1);

   int getTotalViolations(Player var1);

   int getCombatViolations(Player var1);

   int getMovementViolations(Player var1);

   int getWorldViolations(Player var1);

   void setServerName(String var1);
}
