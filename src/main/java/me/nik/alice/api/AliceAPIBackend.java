package me.nik.alice.api;

import java.util.Collection;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.UUID;
import me.nik.alice.Alice;
import me.nik.alice.Dq;
import me.nik.alice.F;
import me.nik.alice.GK;
import me.nik.alice.UC;
import me.nik.alice.UN$dx;
import me.nik.alice.checks.Category;
import me.nik.alice.gE;
import me.nik.alice.checks.Check;
import me.nik.alice.api.custom.CheckData;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

public class AliceAPIBackend implements AliceAPI {
   private final Alice plugin;

   public AliceAPIBackend(Alice var1) {
      this.plugin = var1;
   }

   @Override
   public void setTempBypass(Player var1, long var2) {
      UC var4;
      if ((var4 = this.plugin.dx().dx(var1)) != null) {
         var4.dC(var2 * 1000L + System.currentTimeMillis());
      }
   }

   @Override
   public boolean isBypassing(Player var1) {
      UC var2;
      return !this.plugin.dx().dx() || this.plugin.dx().Aa() || !UN$dx.Ch.dC() && (var2 = this.plugin.dx().dx(var1)) != null && var2.x();
   }

   @Override
   public void updateBypass(Player var1) {
      UC var2;
      if ((var2 = this.plugin.dx().dx(var1)) != null) {
         var2.Eu();
      }
   }

   @Override
   public boolean isTempBypassing(Player var1) {
      UC var2;
      return (var2 = this.plugin.dx().dx(var1)) != null && var2.og() < 0L;
   }

   @Override
   public void punish(String var1, String var2) {
      gE.h5("alice punish " + var1 + " " + var2);
   }

   @Override
   public void sendAlert(String var1) {
      this.plugin.dx().dx().execute(this::lambda$sendAlert$0);
   }

   @Override
   public void registerPunishAnimation(PunishAnimation var1) {
      if (!this.plugin.dx().dx().contains(var1)) {
         this.plugin.dx().dx(var1);
      }
   }

   @Override
   public void flag(Player var1, String var2, String var3) {
      UC var5;
      if ((var5 = this.plugin.dx().dx(var1)) != null) {
         Iterator var6 = var5.dx().dC().iterator();

         while (var6.hasNext()) {
            Check var4;
            if ((var4 = (Check)var6.next()).AC().equalsIgnoreCase(var2)) {
               var4.og(var3.isEmpty() ? "Custom Flag" : var3);
               break;
            }
         }
      }
   }

   @Override
   public void flag(Player var1, String var2, String var3, String var4) {
      UC var6;
      if ((var6 = this.plugin.dx().dx(var1)) != null) {
         Iterator var7 = var6.dx().dC().iterator();

         while (var7.hasNext()) {
            Check var5;
            if ((var5 = (Check)var7.next()).AC().equalsIgnoreCase(var2) && var5.dC().equalsIgnoreCase(var3)) {
               var5.og(var4.isEmpty() ? "Custom Flag" : var4);
               break;
            }
         }
      }
   }

   @Override
   public void setViolations(Player var1, String var2, String var3, int var4) {
      UC var6;
      if ((var6 = this.plugin.dx().dx(var1)) != null) {
         Iterator var7 = var6.dx().dC().iterator();

         while (var7.hasNext()) {
            Check var5;
            if ((var5 = (Check)var7.next()).AC().equalsIgnoreCase(var2) && var5.dC().equalsIgnoreCase(var3)) {
               var5.Aa(var4);
               break;
            }
         }
      }
   }

   @Override
   public int getViolations(Player var1, String var2) {
      UC var3;
      return (var3 = this.plugin.dx().dx(var1)) == null ? 0 : var3.dx().dC().stream().filter(AliceAPIBackend::lambda$getViolations$1).mapToInt(Check::getVl).sum();
   }

   @Override
   public int getViolations(Player var1, String var2, String var3) {
      UC var4;
      return (var4 = this.plugin.dx().dx(var1)) == null ? 0 : var4.dx().dC().stream().filter(AliceAPIBackend::lambda$getViolations$2).mapToInt(Check::getVl).sum();
   }

   @Override
   public Collection<CheckData> getCheckData(Player var1) {
      LinkedList var2 = new LinkedList();
      if (var1 == null) {
         GK var4;
         (var4 = new GK(null)).DB();
         var4.dC().forEach(AliceAPIBackend::lambda$getCheckData$3);
         return var2;
      } else {
         UC var3;
         if ((var3 = this.plugin.dx().dx(var1)) == null) {
            return var2;
         } else {
            var3.dx().dC().forEach(AliceAPIBackend::lambda$getCheckData$4);
            return var2;
         }
      }
   }

   @Override
   public int getTotalViolations(Player var1) {
      UC var2;
      return (var2 = this.plugin.dx().dx(var1)) == null ? 0 : var2.dx().dC().stream().mapToInt(Check::getVl).sum();
   }

   @Override
   public int getCombatViolations(Player var1) {
      UC var2;
      return (var2 = this.plugin.dx().dx(var1)) == null
         ? 0
         : var2.dx().dC().stream().filter(AliceAPIBackend::lambda$getCombatViolations$5).mapToInt(Check::getVl).sum();
   }

   @Override
   public int getMovementViolations(Player var1) {
      UC var2;
      return (var2 = this.plugin.dx().dx(var1)) == null
         ? 0
         : var2.dx().dC().stream().filter(AliceAPIBackend::lambda$getMovementViolations$6).mapToInt(Check::getVl).sum();
   }

   @Override
   public int getWorldViolations(Player var1) {
      UC var2;
      return (var2 = this.plugin.dx().dx(var1)) == null
         ? 0
         : var2.dx().dC().stream().filter(AliceAPIBackend::lambda$getWorldViolations$7).mapToInt(Check::getVl).sum();
   }

   @Override
   public void setServerName(String var1) {
      this.plugin.dx().set("server_name", var1);
      this.plugin.dx().hW();
      this.plugin.dx().zP();
      this.plugin.AC();
   }

   private static boolean lambda$getWorldViolations$7(Check var0) {
      return var0.dx() == Category.WORLD;
   }

   private static boolean lambda$getMovementViolations$6(Check var0) {
      return var0.dx() == Category.MOVE;
   }

   private static boolean lambda$getCombatViolations$5(Check var0) {
      return var0.dx() == Category.COMBAT;
   }

   private static void lambda$getCheckData$4(List var0, Check var1) {
      var0.add(new CheckData(var1.AC(), var1.dC(), var1.getFullCheckName(), var1.getVl()));
   }

   private static void lambda$getCheckData$3(List var0, Check var1) {
      var0.add(new CheckData(var1.AC(), var1.dC(), var1.getFullCheckName(), var1.getVl()));
   }

   private static boolean lambda$getViolations$2(String var0, String var1, Check var2) {
      return var2.AC().equalsIgnoreCase(var0) && var2.dC().equalsIgnoreCase(var1);
   }

   private static boolean lambda$getViolations$1(String var0, Check var1) {
      return var1.AC().equalsIgnoreCase(var0);
   }

   private void lambda$sendAlert$0(String var1) {
      var1 = F.dx.getMessage() + Dq.sB(var1);
      Iterator var2 = this.plugin.dx().og().iterator();

      while (var2.hasNext()) {
         Player var3;
         if ((var3 = Bukkit.getPlayer((UUID)var2.next())) != null) {
            var3.sendMessage(var1);
         }
      }
   }
}
