package me.nik.alice.api;

import me.clip.placeholderapi.expansion.PlaceholderExpansion;
import me.nik.alice.Alice;
import me.nik.alice.UC;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;

public class PapiHook extends PlaceholderExpansion {
   private final Alice plugin;

   public PapiHook(Alice var1) {
      this.plugin = var1;
   }

   public String getIdentifier() {
      return "alice";
   }

   public String getAuthor() {
      return this.plugin.getDescription().getAuthors().toString();
   }

   public String getVersion() {
      return this.plugin.getDescription().getVersion();
   }

   public boolean canRegister() {
      return true;
   }

   public boolean persist() {
      return true;
   }

   public String onRequest(OfflinePlayer var1, String var2) {
      if (!var1.isOnline()) {
         return null;
      } else {
         Player var5 = (Player)var1;
         UC var3;
         if ((var3 = this.plugin.dx().dx(var5)) == null) {
            return null;
         } else {
            var2 = var2.toLowerCase();
            switch (var2) {
               case "client":
                  return var3.hW();
               case "total_violations":
                  return String.valueOf(Alice.getAPI().getTotalViolations(var5));
               case "combat_violations":
                  return String.valueOf(Alice.getAPI().getCombatViolations(var5));
               case "world_violations":
                  return String.valueOf(Alice.getAPI().getWorldViolations(var5));
               case "movement_violations":
                  return String.valueOf(Alice.getAPI().getMovementViolations(var5));
               default:
                  return null;
            }
         }
      }
   }
}
