package me.nik.alice.api.events;

import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

public final class AlicePunishEvent extends Event {
   private static final HandlerList handlers = new HandlerList();
   private final String player;
   private final String from;
   private final String reason;

   public AlicePunishEvent(String var1, String var2, String var3) {
      this.player = var1;
      this.from = var2;
      this.reason = var3;
   }

   public static HandlerList getHandlerList() {
      return handlers;
   }

   public String getPlayer() {
      return this.player;
   }

   public String getFrom() {
      return this.from;
   }

   public String getReason() {
      return this.reason;
   }

   public HandlerList getHandlers() {
      return handlers;
   }
}
