package me.nik.alice.api.events;

import java.util.Collection;
import org.bukkit.event.Event;
import org.bukkit.event.HandlerList;

public final class AlicePunishWaveEvent extends Event {
   private static final HandlerList handlers = new HandlerList();
   private final Collection<String> punishedPlayers;

   public AlicePunishWaveEvent(Collection<String> var1) {
      this.punishedPlayers = var1;
   }

   public static HandlerList getHandlerList() {
      return handlers;
   }

   public HandlerList getHandlers() {
      return handlers;
   }

   public Collection<String> getPunishedPlayers() {
      return this.punishedPlayers;
   }
}
