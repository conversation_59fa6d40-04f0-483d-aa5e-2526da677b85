package me.nik.alice.api.events;

public final class AliceViolationEvent extends org.bukkit.event.Event implements org.bukkit.event.Cancellable
{
    private static final org.bukkit.event.HandlerList handlers;
    private final org.bukkit.entity.Player player;
    private final java.lang.String checkName;
    private final java.lang.String description;
    private final java.lang.String type;
    private final java.lang.String information;
    private final int vl;
    private final int maxVl;
    private final boolean experimental;
    private boolean cancel;
    
    public AliceViolationEvent(final org.bukkit.entity.Player player, final java.lang.String checkName, final java.lang.String description, final java.lang.String type, final java.lang.String information, final int vl, final int maxVl, final boolean experimental) {
        super(!org.bukkit.Bukkit.isPrimaryThread());
        this.cancel = false;
        this.player = player;
        this.checkName = checkName;
        this.description = description;
        this.type = type;
        this.information = information;
        this.vl = vl;
        this.maxVl = maxVl;
        this.experimental = experimental;
    }
    
    public boolean isCancelled() {
        return this.cancel;
    }
    
    public void setCancelled(final boolean cancel) {
        this.cancel = cancel;
    }
    
    public java.lang.String getCheck() {
        return this.checkName;
    }
    
    public java.lang.String getDescription() {
        return this.description;
    }
    
    public java.lang.String getType() {
        return this.type;
    }
    
    public int getVl() {
        return this.vl;
    }
    
    public int getMaxVl() {
        return this.maxVl;
    }
    
    public java.lang.String getInformation() {
        return this.information;
    }
    
    public static org.bukkit.event.HandlerList getHandlerList() {
        return me.nik.alice.api.events.AliceViolationEvent.handlers;
    }
    
    public org.bukkit.entity.Player getPlayer() {
        return this.player;
    }
    
    public boolean isExperimental() {
        return this.experimental;
    }
    
    public org.bukkit.event.HandlerList getHandlers() {
        return me.nik.alice.api.events.AliceViolationEvent.handlers;
    }
    
    static {
        handlers = new org.bukkit.event.HandlerList();
    }
}
