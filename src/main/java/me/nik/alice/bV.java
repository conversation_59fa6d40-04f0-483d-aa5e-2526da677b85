package me.nik.alice;

import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.util.Vector;

public class bV {
   private final double Vm;
   private final float dC;
   private final long timeStamp;
   private final double sX;
   private final World Aa;
   private final double WB;
   private final float sB;

   public Location dx() {
      return new Location(this.Aa, this.sX, this.WB, this.Vm, this.sB, this.dC);
   }

   public long getTimeStamp() {
      return this.timeStamp;
   }

   public float getYaw() {
      return this.sB;
   }

   public bV(World var1, double var2, double var4, double var6, float var8, float var9) {
      this.Aa = var1;
      this.sX = var2;
      this.WB = var4;
      this.Vm = var6;
      this.sB = var8;
      this.dC = var9;
      this.timeStamp = System.currentTimeMillis();
   }

   public World dx() {
      return this.Aa;
   }

   public double getY() {
      return this.WB;
   }

   public double getZ() {
      return this.Vm;
   }

   public Vector dx() {
      return new Vector(this.sX, this.WB, this.Vm);
   }

   public bV dx() {
      return new bV(this.Aa, this.sX, this.WB, this.Vm, this.sB, this.dC);
   }

   public Object clone() {
      return this.dx();
   }

   public float getPitch() {
      return this.dC;
   }

   public bV(Location var1) {
      this.Aa = var1.getWorld();
      this.sX = var1.getX();
      this.WB = var1.getY();
      this.Vm = var1.getZ();
      this.sB = var1.getYaw();
      this.dC = var1.getPitch();
      this.timeStamp = System.currentTimeMillis();
   }

   public double getX() {
      return this.sX;
   }
}
