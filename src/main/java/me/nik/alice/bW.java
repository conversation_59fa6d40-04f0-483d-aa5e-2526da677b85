package me.nik.alice;

import java.util.Iterator;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;

public final class bW {
   private bW() {
   }

   public static void dx(Plugin var0, Player var1, Player var2) {
      if (w.v()) {
         var1.hidePlayer(var2);
      } else {
         var1.hidePlayer(var0, var2);
      }
   }

   public static void Aa(Plugin var0, Player var1) {
      Iterator var2 = Bukkit.getOnlinePlayers().iterator();

      while (var2.hasNext()) {
         Player var3;
         Player var10000 = var3 = (Player)var2.next();

         try {
            var10000.hashCode();
         } catch (Exception var4) {
            continue;
         }

         Aa(var0, var3, var1);
      }
   }

   public static void dx(Plugin var0, Player var1) {
      Iterator var2 = Bukkit.getOnlinePlayers().iterator();

      while (var2.hasNext()) {
         Player var3;
         Player var10000 = var3 = (Player)var2.next();

         try {
            var10000.toString();
         } catch (Exception var4) {
            continue;
         }

         dx(var0, var3, var1);
      }
   }

   public static void Aa(Plugin var0, Player var1, Player var2) {
      if (w.v()) {
         var1.showPlayer(var2);
      } else {
         var1.showPlayer(var0, var2);
      }
   }
}
