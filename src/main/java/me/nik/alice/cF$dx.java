package me.nik.alice;

import me.nik.alice.cF.1;
import org.bukkit.entity.Player;
import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockMultiPlaceEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.entity.EntityDamageByEntityEvent;
import org.bukkit.event.entity.EntityDamageEvent;
import org.bukkit.event.entity.EntityShootBowEvent;
import org.bukkit.event.entity.ProjectileLaunchEvent;
import org.bukkit.event.player.PlayerFishEvent;
import org.bukkit.event.player.PlayerItemConsumeEvent;
import org.bukkit.event.player.PlayerTeleportEvent;
import org.bukkit.event.player.PlayerVelocityEvent;
import org.bukkit.projectiles.ProjectileSource;

class cF$dx implements Listener {
   final cF dx;

   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void dx(PlayerItemConsumeEvent var1) {
      UC var3;
      UC var10000 = var3 = cF.dx(this.dx).dx().dx(var1.getPlayer());

      try {
         var10000.getClass();
      } catch (Exception var2) {
         return;
      }

      var3.dx().o4();
   }

   @EventHandler(
      priority = EventPriority.HIGHEST
   )
   public void Aa(BlockBreakEvent var1) {
      this.dx.dx(var1, var1.getPlayer());
   }

   @EventHandler(
      priority = EventPriority.MONITOR,
      ignoreCancelled = true
   )
   public void dx(PlayerTeleportEvent var1) {
      this.dx.dx(var1, var1.getPlayer());
   }

   cF$dx(cF var1, 1 var2) {
      this(var1);
   }

   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void dx(PlayerVelocityEvent var1) {
      UC var3;
      UC var10000 = var3 = cF.dx(this.dx).dx().dx(var1.getPlayer());

      try {
         var10000.toString();
      } catch (Exception var2) {
         return;
      }

      if (var3.dx().DB() > 7) {
         var3.dx().pO();
      }
   }

   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void dx(EntityShootBowEvent var1) {
      if (var1.getEntity() instanceof Player) {
         UC var3;
         UC var10000 = var3 = cF.dx(this.dx).dx().dx((Player)var1.getEntity());

         try {
            var10000.equals(null);
         } catch (Exception var2) {
            return;
         }

         var3.dx().Q();
      }
   }

   @EventHandler(
      priority = EventPriority.HIGHEST,
      ignoreCancelled = true
   )
   public void Aa(BlockPlaceEvent var1) {
      if (!(var1 instanceof BlockMultiPlaceEvent)) {
         this.dx.dx(var1, var1.getPlayer());
      }
   }

   private cF$dx(cF var1) {
      this.dx = var1;
   }

   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void dx(EntityDamageEvent var1) {
      if (var1.getEntity() instanceof Player) {
         UC var3;
         UC var10000 = var3 = cF.dx(this.dx).dx().dx((Player)var1.getEntity());

         try {
            var10000.hashCode();
         } catch (Exception var2) {
            return;
         }

         var3.dx().p6();
      }
   }

   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void dx(PlayerFishEvent var1) {
      label17:
      if (var1.getCaught() instanceof Player) {
         UC var2;
         UC var10000 = var2 = cF.dx(this.dx).dx().dx((Player)var1.getCaught());

         try {
            var10000.hashCode();
         } catch (Exception var3) {
            break label17;
         }

         var2.dx().tr();
      }

      this.dx.dx(var1, var1.getPlayer());
   }

   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void Aa(EntityDamageByEntityEvent var1) {
      if (var1.getDamager() instanceof Player) {
         this.dx.dx(var1, (Player)var1.getDamager());
      }

      if (var1.getEntity() instanceof Player) {
         this.dx.dx(var1, (Player)var1.getEntity());
      }
   }

   @EventHandler(
      priority = EventPriority.MONITOR
   )
   public void dx(ProjectileLaunchEvent var1) {
      ProjectileSource var3;
      if ((var3 = var1.getEntity().getShooter()) instanceof Player) {
         UC var4;
         UC var10000 = var4 = cF.dx(this.dx).dx().dx((Player)var3);

         try {
            var10000.equals(null);
         } catch (Exception var2) {
            return;
         }

         var4.dx().QJ();
      }
   }
}
