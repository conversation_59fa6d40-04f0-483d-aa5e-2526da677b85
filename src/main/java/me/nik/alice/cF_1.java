package me.nik.alice;

import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;

public class cF {
   private final Alice plugin;

   private static void dx(UC var0, Event var1) {
      var0.og(var1);
   }

   static Alice dx(cF var0) {
      return var0.plugin;
   }

   protected void dx(Event var1, Player var2) {
      if (var2.isOnline()) {
         UC var4;
         UC var10000 = var4 = this.plugin.dx().dx(var2);

         try {
            var10000.equals(null);
         } catch (Exception var3) {
            return;
         }

         var4.og().execute(cF::dx);
      }
   }

   public cF(Alice var1) {
      this.plugin = var1;
      Bukkit.getPluginManager().registerEvents(new me.nik.alice.cF.dx(this, null), var1);
   }
}
