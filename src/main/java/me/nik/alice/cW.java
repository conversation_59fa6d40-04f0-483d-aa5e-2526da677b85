package me.nik.alice;

import org.bukkit.entity.Entity;

public enum cW {
   dx(cW.nb[1], 0.6F),
   Aa(cW.nb[3], 0.6F),
   og(cW.nb[5], 0.6F),
   AC(cW.nb[7], 0.9F),
   sB(cW.nb[9], 0.8F),
   dC(cW.nb[11], 1.4F),
   Zh(cW.nb[13], 3.6F),
   h5(cW.nb[15], 0.6F),
   Fh(cW.nb[17], 0.6F),
   b(cW.nb[19], 0.6F),
   VL(cW.nb[21], 0.6F),
   UH(cW.nb[23], 0.6F),
   yM(cW.nb[25], 0.4F),
   sX(cW.nb[27], 2.04F),
   WB(cW.nb[29], 4.0F),
   Vm(cW.nb[31], 0.6F),
   gz(cW.nb[33], 0.6F),
   tm(cW.nb[35], 0.7F),
   x(cW.nb[37], 0.4F),
   zP(cW.nb[39], 0.4F),
   hW(cW.nb[41], 0.6F),
   Ch(cW.nb[43], 2.04F),
   iv(cW.nb[45], 16.0F),
   F7(cW.nb[47], 0.9F),
   Zp(cW.nb[49], 0.5F),
   DB(cW.nb[51], 0.6F),
   jD(cW.nb[53], 0.85F),
   Qt(cW.nb[55], 0.9F),
   rc(cW.nb[57], 0.9F),
   Zm(cW.nb[59], 0.9F),
   jA(cW.nb[61], 0.4F),
   bI(cW.nb[63], 0.8F),
   yV(cW.nb[65], 0.6F),
   a(cW.nb[67], 0.9F),
   cO(cW.nb[69], 0.7F),
   tk(cW.nb[71], 0.6F),
   Eu(cW.nb[73], 1.4F),
   nZ(cW.nb[75], 1.3964F),
   u(cW.nb[77], 1.3964F),
   Q(cW.nb[79], 0.4F),
   QJ(cW.nb[81], 0.6F);

   private final float iv;
   private final String name;
   private static final cW[] dx;
   private static String[] nb;

   private cW(String var3, float var4) {
      this.name = var3;
      this.iv = var4;
   }

   public static float dx(Entity var0) {
      cW[] var1;
      for (cW var4 : var1 = values()) {
         String var5 = var0.getType().toString();
         String var10000 = var4.name;
         if (Integer.valueOf(var5.hashCode()).equals(var10000.hashCode())) {
            return var4.iv;
         }
      }

      return -1.0F;
   }

   static {
      qu();
      dx = new cW[]{
         dx,
         Aa,
         og,
         AC,
         sB,
         dC,
         Zh,
         h5,
         Fh,
         b,
         VL,
         UH,
         yM,
         sX,
         WB,
         Vm,
         gz,
         tm,
         x,
         zP,
         hW,
         Ch,
         iv,
         F7,
         Zp,
         DB,
         jD,
         Qt,
         rc,
         Zm,
         jA,
         bI,
         yV,
         a,
         cO,
         tk,
         Eu,
         nZ,
         u,
         Q,
         QJ
      };
   }

   private static void qu() {
      nb = new String[]{
         "PLAYER",
         "PLAYER",
         "CREEPER",
         "CREEPER",
         "SKELETON",
         "SKELETON",
         "DOLPHIN",
         "DOLPHIN",
         "PHANTOM",
         "PHANTOM",
         "SPIDER",
         "SPIDER",
         "GIANT",
         "GIANT",
         "ZOMBIE",
         "ZOMBIE",
         "HUSK",
         "HUSK",
         "EVOKER",
         "EVOKER",
         "VINDICATOR",
         "VINDICATOR",
         "PILLAGER",
         "PILLAGER",
         "VEX",
         "VEX",
         "SLIME",
         "SLIME",
         "GHAST",
         "GHAST",
         "PIGLIN",
         "PIGLIN",
         "ENDERMAN",
         "ENDERMAN",
         "CAVE_SPIDER",
         "CAVE_SPIDER",
         "SILVERFISH",
         "SILVERFISH",
         "ENDERMITE",
         "ENDERMITE",
         "BLAZE",
         "BLAZE",
         "MAGMA_CUBE",
         "MAGMA_CUBE",
         "ENDER_DRAGON",
         "ENDERDRAGON",
         "WITHER",
         "WITHER",
         "BAT",
         "BAT",
         "WITCH",
         "WITCH",
         "GUARDIAN",
         "GUARDIAN",
         "PIG",
         "PIG",
         "SHEEP",
         "SHEEP",
         "COW",
         "COW",
         "CHICKEN",
         "CHICKEN",
         "SQUID",
         "SQUID",
         "WOLF",
         "WOLF",
         "MUSHROOM_COW",
         "MUSHROOM_COW",
         "SNOWMAN",
         "SNOWMAN",
         "CAT",
         "CAT",
         "IRON_GOLEM",
         "IRON_GOLEM",
         "HORSE",
         "HORSE",
         "DONKEY",
         "DONKEY",
         "RABBIT",
         "RABBIT",
         "VILLAGER",
         "VILLAGER"
      };
   }
}
