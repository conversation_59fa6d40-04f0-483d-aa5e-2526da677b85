package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientInstanceOfFlying;

public class cf extends PacketCheck {
   private static String[] i1;
   private float dx;

   public cf(UC var1) {
      super(var1, CheckType.FLY, i1[0], Category.MOVE, 1.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.M3()
         && !this.Aa.dx().Fh()
         && this.Aa.dx().R2() >= 20
         && this.Aa.dx().cO() >= 20
         && !this.Aa.dx().jA()
         && this.Aa.dx().yV() >= 20
         && !this.Aa.dx().dx().DP()
         && !this.Aa.dx().tr()
         && this.Aa.dx().pQ() >= 40
         && this.Aa.dx().GE() >= 20
         && this.Aa.dx().nZ() >= 80
         && this.Aa.dx().yk() >= 10
         && !(this.Aa.dx().Aa().getY() < 4.0)) {
         WrapperPlayClientInstanceOfFlying var6;
         boolean var2 = (var6 = new WrapperPlayClientInstanceOfFlying(var1.dx())).getOnGround();
         double var4;
         if ((var4 = var6.getY()) != 0.0) {
            boolean var7 = var4 % 0.015625 < 1.0E-4;
            boolean var3 = var2 != var7;
            int var8 = this.Aa.dx().M3();
            if (var3) {
               if (var8 > 3) {
                  this.og(i1[2]);
               }

               this.Aa(i1[3] + var2 + i1[4] + var7);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.05);
            }
         }
      }
   }

   static {
      W4();
   }

   private static void W4() {
      i1 = new String[]{"H", "Checks for spoofed ground status", "Impossible ground status", "C: ", " S: "};
   }

   @Override
   public String sB() {
      return i1[1];
   }
}
