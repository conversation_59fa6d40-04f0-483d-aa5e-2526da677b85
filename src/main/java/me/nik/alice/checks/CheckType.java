package me.nik.alice.checks;

public enum CheckType {
	<PERSON>M("Aim"),
	AUTOFISH("AutoFish"),
	BARITONE("Baritone"),
	CLICKING("Clicking"),
	ELYTRA("Ely<PERSON>"),
	ESP("ESP"),
	FASTCLIMB("FastClimb"),
	FLY("Fly"),
	HITBOX("Hitbox"),
	INTERACT("Interact"),
	INVENTORY("Inventory"),
	JESUS("Jesus"),
	KILLAURA("KillAura"),
	MOTION("Motion"),
	NOFALL("NoFall"),
	PACKET("Packet"),
	REACH("Reach"),
	SCAFFOLD("Scaffold"),
	SPEED("Speed"),
	T<PERSON><PERSON>("Timer"),
	VEH<PERSON><PERSON>("Vehicle"),
	VELOCITY("Velocity"),
	XRAY("XRay");

	private final String checkName;

	CheckType(String name) {
		this.checkName = name;
	}

	public String getCheckName() {
		return this.checkName;
	}
}
