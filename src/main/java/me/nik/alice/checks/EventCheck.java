package me.nik.alice.checks;

import me.nik.alice.UC;
import org.bukkit.event.Event;

public abstract class EventCheck extends Check {
   private static String[] xi;

   private static void lK() {
      xi = new String[]{"", ""};
   }

   public EventCheck(UC var1, CheckType var2, Category var3) {
      super(var1, var2, xi[1], var3, 0.0F);
   }

   public EventCheck(UC var1, CheckType var2, Category var3, float var4) {
      super(var1, var2, xi[0], var3, var4);
   }

   public EventCheck(UC var1, CheckType var2, String var3, Category var4, float var5) {
      super(var1, var2, var3, var4, var5);
   }

   public EventCheck(UC var1, CheckType var2, String var3, Category var4) {
      super(var1, var2, var3, var4, 0.0F);
   }

   static {
      lK();
   }

   public abstract void on(Event var1);
}
