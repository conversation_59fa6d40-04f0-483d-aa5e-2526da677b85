package me.nik.alice.checks;

import me.nik.alice.*;

public abstract class PacketCheck extends Check {
   private static String[] 9m;

   public PacketCheck(UC var1, CheckType var2, String var3, Category var4, float var5) {
      super(var1, var2, var3, var4, var5);
   }

   static {
      t4();
   }

   public PacketCheck(UC var1, CheckType var2, Category var3) {
      super(var1, var2, 9m[1], var3, 0.0F);
   }

   public PacketCheck(UC var1, CheckType var2, String var3, Category var4) {
      super(var1, var2, var3, var4, 0.0F);
   }

   public PacketCheck(UC var1, CheckType var2, Category var3, float var4) {
      super(var1, var2, 9m[0], var3, var4);
   }

   public abstract void dx(DH var1);

   private static void t4() {
      9m = new String[]{"", ""};
   }
}
