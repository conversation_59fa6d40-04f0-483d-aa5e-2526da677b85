package me.nik.alice.checks.aim;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.ZQ;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.qn;
import me.nik.fastmath.FastMath;

public class AimA extends PacketCheck {
   private static String[] Dx;

   static {
      GA();
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         qn var13;
         float var2 = (var13 = this.Aa.dx()).b();
         float var3 = var13.UH();
         if (var2 != 0.0F && var3 != 0.0F) {
            ZQ var4;
            float var5 = (var4 = var13.dx()).zP();
            long var7 = var4.sX();
            if (var5 != -1.0F) {
               float var16 = var13.getYaw();
               float var6 = var13.h5();
               float var9 = var13.getPitch();
               float var14 = var13.Fh();
               float var17;
               float var10000 = var17 = var5 * 0.6F + 0.2F;
               var5 = var10000 * var10000 * var17 * 1.2F;
               boolean var10 = false;
               if (!(var2 <= 1.0F) && !(var3 <= 1.0F)) {
                  float var11 = var16 - var16 % var5;
                  float var12 = var9 - var9 % var5;
                  var11 = Math.abs(var11 - var16);
                  var12 = Math.abs(var12 - var9);
                  if (var11 == 0.0F || var12 == 0.0F) {
                     this.Aa(Dx[2]);
                     var10 = true;
                  }
               }

               if (!(var3 < var5 * 5.0F) && !(var2 < var5 * 5.0F) && !var10) {
                  float var20 = var16 - var6 - (var16 - var6) % var5;
                  var20 = var6 + var20 / 2.0F;
                  float var31 = var9 - var14 - (var9 - var14) % var5;
                  var31 = var14 + var31 / 2.0F;
                  var20 = Math.abs(var20 - var16);
                  float var15 = Math.abs(var31 - var9);
                  if (var20 == 0.0F || var15 == 0.0F) {
                     this.Aa(Dx[3]);
                     var10 = true;
                  }
               }

               if (!(var2 <= 1.0F) && !(var3 <= 1.0F) && !var10) {
                  float var23 = var16 + var16 % var5;
                  float var33 = var9 + var9 % var5;
                  var23 = Math.abs(var23 - var16);
                  var33 = Math.abs(var33 - var9);
                  if (var23 == 0.0F || var33 == 0.0F) {
                     this.Aa(Dx[4]);
                     var10 = true;
                  }
               }

               if (!(var2 <= 1.0F) && !(var3 <= 1.0F) && !var10) {
                  float var25 = var16 - (float)FastMath.round(var16) % var5;
                  float var35 = var9 - (float)FastMath.round(var9) % var5;
                  var25 = Math.abs(var25 - var16);
                  var35 = Math.abs(var35 - var9);
                  if (var25 == 0.0F || var35 == 0.0F) {
                     this.Aa(Dx[5]);
                     var10 = true;
                  }
               }

               if (!(var2 <= 1.0F) && !(var3 <= 1.0F) && !var10) {
                  float var27 = var16 + (float)FastMath.round(var16) % var5;
                  float var37 = var9 + (float)FastMath.round(var9) % var5;
                  var27 = Math.abs(var27 - var16);
                  var37 = Math.abs(var37 - var9);
                  if (var27 == 0.0F || var37 == 0.0F) {
                     this.Aa(Dx[6]);
                     var10 = true;
                  }
               }

               if (!var10) {
                  this.Aa(0.125);
               } else {
                  float var29 = var7 != 145L && var7 != 111L && var7 != 67L ? this.dx() : this.dx() * 2.0F;
                  if (this.Aa() > var29) {
                     this.sX();
                  }
               }
            }
         }
      }
   }

   public AimA(UC var1) {
      super(var1, CheckType.AIM, Dx[0], Category.COMBAT, 10.0F);
   }

   @Override
   public String sB() {
      return Dx[1];
   }

   private static void GA() {
      Dx = new String[]{"A", "Checks for gcd bypassing methods", "A", "B", "C", "D", "E"};
   }
}
