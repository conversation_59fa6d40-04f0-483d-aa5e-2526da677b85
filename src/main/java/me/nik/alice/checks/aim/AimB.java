package me.nik.alice.checks.aim;

import java.util.function.Predicate;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.l;
import me.nik.alice.qn;
import me.nik.fastmath.FastMath;

public class AimB extends PacketCheck {
   private static String[] k0;
   private final Predicate dx = AimB::broadCast;

   private static void yT() {
      k0 = new String[]{"B", "Checks for rounded rotations", "Ya: ", " Pa: ", " Dy: ", " Dp: ", " yaw: ", " pitch: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM() && !(this.Aa.dx().gz() <= 0.125)) {
         qn var7;
         float var2 = (var7 = this.Aa.dx()).sX();
         float var3 = var7.Vm();
         float var4 = var7.b() % 360.0F;
         float var5 = var7.UH();
         float var6 = var7.getYaw();
         float var8 = var7.getPitch();
         if (this.dx.test(var2) || this.dx.test(var3) || this.dx.test(var4) || this.dx.test(var5) || this.dx.test(var6) || this.dx.test(var8)) {
            this.Aa(k0[2] + var2 + k0[3] + var3 + k0[4] + var4 + k0[5] + var5 + k0[6] + var6 + k0[7] + var8);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.25);
         }
      }
   }

   private static boolean dx(Float var0) {
      return (var0 == (float)FastMath.round(var0) || var0 % 1.0F == 0.0F || var0 == (float)((int) l.Aa((double)var0.floatValue(), 1))) && var0 > 0.0F;
   }

   @Override
   public String sB() {
      return k0[1];
   }

   public AimB(UC var1) {
      super(var1, CheckType.AIM, k0[0], Category.COMBAT, 5.0F);
   }

   static {
      yT();
   }
}
