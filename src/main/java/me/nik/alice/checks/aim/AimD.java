package me.nik.alice.checks.aim;

import me.nik.alice.*;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;

public class AimD extends PacketCheck {
   private boolean AC;
   private boolean og;
   private static String[] Td;
   private final V0 dx = new V0(200);
   private final V0 og;
   private final V0 Aa = new V0(200);
   private boolean sB;
   private final V0 AC;
   private long dx;

   @Override
   public String sB() {
      return Td[1];
   }

   static {
      tf();
   }

   private static boolean Aa(Number var0) {
      return var0.floatValue() < 0.75F;
   }

   private static boolean VL(Number var0) {
      return var0.floatValue() < 1.25F;
   }

   private static boolean h5(Number var0) {
      return var0.floatValue() < 1.25F;
   }

   private static boolean sB(Number var0) {
      return var0.floatValue() < 0.75F;
   }

   private static boolean dx(Number var0) {
      return var0.floatValue() == (float)FastMath.round(var0.floatValue());
   }

   private static void tf() {
      Td = new String[]{
         "D",
         "Checks for aim assist by using heuristics",
         "(DY) Impossible consistency, D: ",
         " A: ",
         "(DY) Poor randomization, High: ",
         " Low: ",
         "(DY) Extreme randomization, Low: ",
         " D: ",
         "(DY) Impossible consistency, Low: ",
         " D: ",
         "(DY) Invalid rotation, A: ",
         "(DY) Rounded rotations, R: ",
         "(DP) Impossible consistency, D: ",
         " A: ",
         "(DP) Poor randomization, High: ",
         " Low: ",
         "(DP) Extreme randomization, Low: ",
         " D: ",
         "(DP) Impossible consistency, Low: ",
         " D: ",
         "(DP) Invalid rotation, A: ",
         "(DP) Rounded rotations, R: ",
         "(YA) Impossible consistency, D: ",
         " A: ",
         "(YA) Poor randomization, High: ",
         " Low: ",
         "(YA) Extremely smooth, Low: ",
         " D: ",
         "(YA) Impossible randomization, A: ",
         "(YA) Impossible consistency, Low: ",
         " D: ",
         "(YA) Blatant randomization, A: ",
         " D: ",
         "(YA) Impossible randomization, A: ",
         " D: ",
         "(YA) Impossible randomization, D: ",
         "(YA) Impossible randomization, A: ",
         "(YA) Rounded rotations, R: ",
         "(PA) Impossible consistency, D: ",
         " A: ",
         "(PA) Poor randomization, High: ",
         " Low: ",
         "(PA) Extremely smooth, Low: ",
         " D: ",
         "(PA) Impossible randomization, A: ",
         "(PA) Impossible consistency, Low: ",
         " D: ",
         "(PA) Bad randomization, A: ",
         " D: ",
         "(PA) Impossible randomization, A: ",
         " D: ",
         "(PA) Impossible randomization, D: ",
         "(PA) Impossible randomization, A: ",
         "(PA) Rounded rotations, R: ",
         "(GR) Extreme randomization, PA: ",
         " DP: ",
         "(GR) Extreme randomization, YA: ",
         " DY: ",
         "(GR) No randomization, DY: ",
         " YA: ",
         "(GR) No randomization, DP: ",
         " PA: ",
         "(GS) Extremely smooth, YAD: ",
         " PAD: ",
         "(GS) Impossible deviation, YAD: ",
         " PAD: ",
         "(GC) Small Constant rotations, YA: ",
         " DY: ",
         "(GC) Small Constant rotations, PA: ",
         " DP: "
      };
   }

   private static boolean og(Number var0) {
      return var0.floatValue() > 7.5F;
   }

   public AimD(UC var1) {
      super(var1, CheckType.AIM, Td[0], Category.COMBAT);
      this.og = new V0(200);
      this.AC = new V0(200);
   }

   private static boolean Zh(Number var0) {
      return var0.floatValue() == (float)FastMath.round(var0.floatValue());
   }

   private static boolean UH(Number var0) {
      return var0.floatValue() > 7.5F;
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && this.Aa.dx().tV() >= 3 && !this.Aa.dx().yM() && !(this.Aa.dx().gz() <= 0.125)) {
         long var2 = l.og(this.dx);
         this.dx = var1.getTimeStamp();
         if (var2 >= 10000L) {
            this.dx.clear();
            this.Aa.clear();
            this.og.clear();
            this.AC.clear();
         }

         qn var22;
         float var24 = (var22 = this.Aa.dx()).b();
         float var3 = var22.UH();
         float var4 = var22.sX();
         float var5 = var22.Vm();
         if (var24 > 0.0F) {
            this.dx.add(var24);
         }

         if (var3 > 0.0F) {
            this.Aa.add(var3);
         }

         if (var4 > 0.0F) {
            this.og.add(var4);
         }

         if (var5 > 0.0F) {
            this.AC.add(var5);
         }

         if (this.dx.size() <= 1 || this.Aa.size() <= 1 || this.og.size() <= 1 || this.AC.size() <= 1) {
            this.og = false;
            this.AC = false;
            this.sB = false;
         }

         boolean var23 = var22.dx().hB() < 120;
         if (this.dx.h5()) {
            double var10 = l.Zh(this.dx);
            int var12 = l.AC(this.dx);
            int var13 = l.dx(this.dx, AimD::UH);
            int var14 = l.dx(this.dx, AimD::VL);
            int var15 = l.dx(this.dx, AimD::b);
            if (var12 >= 160 && var10 >= 12.5) {
               this.og(Td[2] + var12 + Td[3] + var10);
            }

            if (var13 >= 95 && var14 >= 95) {
               this.og(Td[4] + var13 + Td[5] + var14);
            }

            if (var14 >= 125 && var12 <= 5 && !var23) {
               this.og(Td[6] + var14 + Td[7] + var12);
            }

            if (var14 == 0 && var13 == 0 && var12 <= 10) {
               this.og(Td[8] + var14 + Td[9] + var12);
            }

            if (l.yM(var10)) {
               this.og(Td[10].concat(String.valueOf(var10)));
            }

            if (var15 >= 20) {
               this.og(Td[11].concat(String.valueOf(var15)));
            }
         }

         if (this.Aa.h5()) {
            double var25 = l.Zh(this.Aa);
            int var29 = l.AC(this.Aa);
            int var33 = l.dx(this.Aa, AimD::Fh);
            int var34 = l.dx(this.Aa, AimD::h5);
            int var38 = l.dx(this.Aa, AimD::Zh);
            if (var29 >= 160 && var25 >= 12.5) {
               this.og(Td[12] + var29 + Td[13] + var25);
            }

            if (var33 >= 95 && var34 >= 95) {
               this.og(Td[14] + var33 + Td[15] + var34);
            }

            if (var34 >= 125 && var29 <= 5 && !var23) {
               this.og(Td[16] + var34 + Td[17] + var29);
            }

            if (var34 == 0 && var33 == 0 && var29 <= 5) {
               this.og(Td[18] + var34 + Td[19] + var29);
            }

            if (l.yM(var25)) {
               this.og(Td[20].concat(String.valueOf(var25)));
            }

            if (var38 >= 20) {
               this.og(Td[21].concat(String.valueOf(var38)));
            }
         }

         if (this.og.h5()) {
            double var26 = l.Zh(this.og);
            double var30 = l.sB(this.og);
            int var35 = l.AC(this.og);
            int var39 = l.dx(this.og, AimD::dC);
            int var16 = l.dx(this.og, AimD::sB);
            int var17 = l.dx(this.og, AimD::AC);
            if (var35 >= 160 && var26 >= 6.25) {
               this.og(Td[22] + var35 + Td[23] + var26);
            }

            if (var39 >= 85 && var16 >= 85) {
               this.og(Td[24] + var39 + Td[25] + var16);
            }

            if (var16 >= 125 && var35 <= 5 && !var23) {
               this.og(Td[26] + var16 + Td[27] + var35);
            }

            if (var26 >= 45.0 && var35 <= 2) {
               this.og(Td[28].concat(String.valueOf(var26)));
            }

            if (var16 == 0 && var39 == 0 && var35 <= 5) {
               this.og(Td[29] + var16 + Td[30] + var35);
            }

            if (var26 >= 5.0 && var30 <= 1.25) {
               this.og(Td[31] + var26 + Td[32] + var30);
            }

            if (var26 <= 0.5 && var30 >= 5.0) {
               this.og(Td[33] + var26 + Td[34] + var30);
            }

            if (var30 > 85.0) {
               this.og(Td[35].concat(String.valueOf(var30)));
            }

            if (l.yM(var26)) {
               this.og(Td[36].concat(String.valueOf(var26)));
            }

            if (var17 >= 20) {
               this.og(Td[37].concat(String.valueOf(var17)));
            }
         }

         if (this.AC.h5()) {
            double var27 = l.Zh(this.AC);
            double var31 = l.sB(this.AC);
            int var36 = l.AC(this.AC);
            int var40 = l.dx(this.AC, AimD::og);
            int var41 = l.dx(this.AC, AimD::Aa);
            int var43 = l.dx(this.AC, AimD::dx);
            if (var36 >= 160 && var27 >= 6.25) {
               this.og(Td[38] + var36 + Td[39] + var27);
            }

            if (var40 >= 85 && var41 >= 85) {
               this.og(Td[40] + var40 + Td[41] + var41);
            }

            if (var41 >= 125 && var36 <= 5 && !var23) {
               this.og(Td[42] + var41 + Td[43] + var36);
            }

            if (var27 >= 50.0 && var36 <= 2) {
               this.og(Td[44].concat(String.valueOf(var27)));
            }

            if (var41 == 0 && var40 == 0 && var36 <= 5) {
               this.og(Td[45] + var41 + Td[46] + var36);
            }

            if (var27 >= 5.0 && var31 <= 0.75) {
               this.og(Td[47] + var27 + Td[48] + var31);
            }

            if (var27 <= 0.5 && var31 >= 7.5) {
               this.og(Td[49] + var27 + Td[50] + var31);
            }

            if (var31 > 85.0) {
               this.og(Td[51].concat(String.valueOf(var31)));
            }

            if (l.yM(var27)) {
               this.og(Td[52].concat(String.valueOf(var27)));
            }

            if (var43 >= 20) {
               this.og(Td[53].concat(String.valueOf(var43)));
            }
         }

         double var28 = 0.0;
         double var32 = 0.0;
         double var37 = 0.0;
         double var42 = 0.0;
         if (this.dx.size() >= 100) {
            var28 = l.Zh(this.dx);
         }

         if (this.Aa.size() >= 100) {
            var32 = l.Zh(this.Aa);
         }

         if (this.og.size() >= 100) {
            var37 = l.Zh(this.og);
         }

         if (this.AC.size() >= 100) {
            var42 = l.Zh(this.AC);
         }

         if (var28 != 0.0 && var32 != 0.0 && var37 != 0.0 && var42 != 0.0 && !this.og) {
            this.og = true;
            if (var42 >= 5.0 && var32 >= 5.0 && var42 > var32 * 4.0) {
               this.og(Td[54] + var42 + Td[55] + var32);
            }

            if (var37 >= 5.0 && var28 >= 5.0 && var37 > var28 * 4.0) {
               this.og(Td[56] + var37 + Td[57] + var28);
            }

            if (var28 > 5.0 && var37 <= 0.25 && !var23) {
               this.og(Td[58] + var28 + Td[59] + var37);
            }

            if (var32 > 2.5 && var42 <= 0.25 && !var23) {
               this.og(Td[60] + var32 + Td[61] + var42);
            }
         }

         if (var37 != 0.0 && var42 != 0.0 && !this.AC) {
            this.AC = true;
            double var18 = l.sB(this.og);
            double var20 = l.sB(this.AC);
            if (var18 < 0.15 && var20 > 0.5 && !var23) {
               this.og(Td[62] + var18 + Td[63] + var20);
            }

            if (var18 > 50.0 && var20 > 50.0) {
               this.og(Td[64] + var18 + Td[65] + var20);
            }
         }

         if (var28 != 0.0 && var32 != 0.0 && var37 != 0.0 && var42 != 0.0 && !this.sB) {
            this.sB = true;
            if (var37 < 0.1 && var28 >= 3.0) {
               this.og(Td[66] + var37 + Td[67] + var28);
            }

            if (var42 < 0.1 && var32 >= 3.0) {
               this.og(Td[68] + var42 + Td[69] + var32);
            }
         }
      }
   }

   private static boolean b(Number var0) {
      return var0.floatValue() == (float)FastMath.round(var0.floatValue());
   }

   private static boolean Fh(Number var0) {
      return var0.floatValue() > 7.5F;
   }

   private static boolean AC(Number var0) {
      return var0.floatValue() == (float)FastMath.round(var0.floatValue());
   }

   private static boolean dC(Number var0) {
      return var0.floatValue() > 7.5F;
   }
}
