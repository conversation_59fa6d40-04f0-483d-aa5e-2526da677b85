package me.nik.alice.checks.aim;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.l;
import me.nik.alice.qn;

public class AimE extends PacketCheck {
   private static String[] uo;

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM() && !(this.Aa.dx().gz() <= 0.125)) {
         qn var7;
         float var2 = (var7 = this.Aa.dx()).b();
         float var3 = var7.UH();
         float var4 = var7.sX();
         float var8 = var7.Vm();
         boolean var5 = var2 >= 5.0F && l.yM(var4) && var4 > 0.0F;
         boolean var6 = var3 >= 5.0F && l.yM(var8) && var8 > 0.0F;
         if (!var5 && !var6) {
            this.Aa(0.125);
         } else {
            this.Aa(uo[2] + var2 + uo[3] + var4 + uo[4] + var3 + uo[5] + var8);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         }
      }
   }

   public AimE(UC var1) {
      super(var1, CheckType.AIM, uo[0], Category.COMBAT, 4.0F);
   }

   @Override
   public String sB() {
      return uo[1];
   }

   private static void qH() {
      uo = new String[]{"E", "Checks for irregular aim randomization", "Dy: ", " Ya: ", " Dp: ", " Pa: "};
   }

   static {
      qH();
   }
}
