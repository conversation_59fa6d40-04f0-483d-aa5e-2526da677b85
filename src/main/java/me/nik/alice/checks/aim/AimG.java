package me.nik.alice.checks.aim;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.l;
import me.nik.alice.qn;

public class AimG extends PacketCheck {
   private static String[] v1;

   static {
      Yu();
   }

   @Override
   public String sB() {
      return v1[1];
   }

   private static void Yu() {
      v1 = new String[]{"G", "Checks for non linear rotations", "Dy: ", " Dp: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         qn var5;
         float var2 = (var5 = this.Aa.dx()).UH();
         float var6 = var5.b();
         boolean var3 = var2 > 0.0F && l.yM(var2) && var6 > 0.5F;
         boolean var4 = var6 > 0.0F && l.yM(var6) && var2 > 0.5F;
         if (!var3 && !var4) {
            this.Aa(0.25);
         } else {
            this.Aa(v1[2] + var6 + v1[3] + var2);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         }
      }
   }

   public AimG(UC var1) {
      super(var1, CheckType.AIM, v1[0], Category.COMBAT, 5.0F);
   }
}
