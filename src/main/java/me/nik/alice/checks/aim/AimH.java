package me.nik.alice.checks.aim;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.ZQ;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.qn;

public class AimH extends PacketCheck {
   private static String[] ly;

   private static void _k/* $VF was: 2k*/() {
      ly = new String[]{"H", "Checks for invalid sensitivity", "Div: ", " dp= "};
   }

   public AimH(UC var1) {
      super(var1, CheckType.AIM, ly[0], Category.COMBAT, 10.0F);
   }

   static {
      2k();
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         qn var12;
         float var2 = (var12 = this.Aa.dx()).b();
         float var3 = var12.UH();
         if (!(var2 <= 0.0F) && !(var3 <= 0.0F) && !(var2 > 30.0F) && !(var3 > 30.0F)) {
            long var6;
            ZQ var13;
            if ((var6 = (var13 = var12.dx()).sX()) != -1L) {
               long var8 = var13.UH();
               long var10 = var6 >= 30L && !var12.dx().pO() ? (var6 <= 40L ? 20000L : 131072L) : 15000L;
               if (var8 < var10 && var12.sX() < 10.0F && var12.Vm() < 10.0F) {
                  this.Aa(ly[2] + var8 + ly[3] + var3);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.5);
               }
            }
         }
      }
   }

   @Override
   public String sB() {
      return ly[1];
   }
}
