package me.nik.alice.checks.aim;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.l;
import me.nik.alice.qn;

public class AimI extends PacketCheck {
   private static String[] 5x;

   private static void WT() {
      5x = new String[]{"I", "Checks for flaws in randomization", "Dy: ", " delta accel: "};
   }

   @Override
   public void dx(DH var1) {
      boolean var9 = NMmq;
      if (var1.R() && !this.Aa.dx().yM() && !(this.Aa.dx().gz() < 0.125)) {
         qn var10;
         float var2 = (var10 = this.Aa.dx()).b();
         float var3;
         double var5 = (double)Math.abs((var3 = var10.sX()) - var10.WB());
         long var7;
         float var10000;
         if ((var7 = var10.dx().sX()) >= 120L) {
            if (var7 >= 170L) {
               var10000 = 3.0F;
               if (var9) {
                  throw null;
               }
            } else {
               var10000 = 1.5F;
               if (var9) {
                  throw null;
               }
            }
         } else {
            var10000 = 1.0F;
         }

         float var4 = var10000;
         boolean var11;
         if (var3 * 5.0F < var2 && (double)var3 != 0.0 && var2 > var4 && var2 < var10.VL() && l.yM(var5)) {
            var11 = true;
            if (var9) {
               throw null;
            }
         } else {
            var11 = false;
         }

         if (var11) {
            this.Aa(5x[2] + var2 + 5x[3] + var5);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.125);
         }
      }
   }

   public AimI(UC var1) {
      super(var1, CheckType.AIM, 5x[0], Category.COMBAT, 3.0F);
   }

   static {
      WT();
   }

   @Override
   public String sB() {
      return 5x[1];
   }
}
