package me.nik.alice.checks.aim;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.l;
import me.nik.alice.qn;

public class AimJ extends PacketCheck {
   private static String[] ma;

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         qn var7;
         float var2 = (var7 = this.Aa.dx()).b();
         float var3 = var7.UH();
         float var4 = var7.sX();
         float var5 = var7.Vm();
         float var8 = var7.dx().sX() < 150L ? 5.0F : 7.5F;
         boolean var9 = var3 >= var8 && var2 >= var8;
         boolean var6 = var5 > 0.0F && l.yM(var5) && var4 > 1.0F && var9;
         boolean var10 = var4 > 0.0F && l.yM(var4) && var5 > 1.0F && var9;
         if (!var6 && !var10) {
            this.Aa(0.125);
         } else {
            this.Aa(ma[2] + var3 + ma[3] + var2 + ma[4] + var4 + ma[5] + var5);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         }
      }
   }

   private static void LZ() {
      ma = new String[]{"J", "Checks for poor randomization", "Dp: ", " Dy: ", " Ya: ", " Pa: "};
   }

   static {
      LZ();
   }

   @Override
   public String sB() {
      return ma[1];
   }

   public AimJ(UC var1) {
      super(var1, CheckType.AIM, ma[0], Category.COMBAT, 2.0F);
   }
}
