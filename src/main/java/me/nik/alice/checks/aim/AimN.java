package me.nik.alice.checks.aim;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.ZQ;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.qn;
import me.nik.fastmath.FastMath;

public class AimN extends PacketCheck {
   private static String[] a8;

   public AimN(UC var1) {
      super(var1, CheckType.AIM, a8[0], Category.COMBAT, 5.0F);
   }

   private static void yv() {
      a8 = new String[]{"N", "Checks for invalid rotation constants", "Modulo yaw: ", " modulo pitch: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         qn var32;
         float var2 = (var32 = this.Aa.dx()).b();
         float var3 = var32.VL();
         float var4 = var32.UH();
         float var5 = var32.yM();
         if (!(var2 <= 0.0F) && !(var4 <= 0.0F) && !(var2 > 30.0F) && !(var4 > 30.0F)) {
            ZQ var6;
            long var8;
            if ((var8 = (var6 = var32.dx()).sX()) != -1L) {
               double var10 = var6.nZ();
               double var12 = var6.Eu();
               double var14 = (double)var2 / var10;
               double var16 = (double)var4 / var12;
               double var18 = (double)var3 / var10;
               double var20 = (double)var5 / var12;
               double var22 = var14 % var18;
               double var24 = var16 % var20;
               double var26 = Math.abs(FastMath.floor(var22) - var22);
               double var28 = Math.abs(FastMath.floor(var24) - var24);
               double var30 = var8 > 60L && !var32.dx().pO() ? 0.1F : 0.99F;
               boolean var33 = var22 > 90.0 && var26 > var30;
               boolean var34 = var24 > 90.0 && var28 > var30;
               if (var33 && var34 || Double.isNaN(var26) && Double.isNaN(var28)) {
                  this.Aa(a8[2] + var22 + a8[3] + var24);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.25);
               }
            }
         }
      }
   }

   static {
      yv();
   }

   @Override
   public String sB() {
      return a8[1];
   }
}
