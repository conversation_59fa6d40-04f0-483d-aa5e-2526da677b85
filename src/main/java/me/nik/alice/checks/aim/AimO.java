package me.nik.alice.checks.aim;

import me.nik.alice.*;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

@h5
public class AimO extends PacketCheck {
   private static String[] Gn;

   private static void Xz() {
      Gn = new String[]{"O", "Checks if the player's sensitivities are not linear or invalid", "X: ", " Y: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM()) {
         ZQ var7;
         if ((var7 = this.Aa.dx().dx()).h5()) {
            double var3 = (double)var7.tm();
            double var5 = (double)var7.x();
            if (l.dC(var3, var5) > 1.35 || Double.isNaN(var3) || Double.isNaN(var5)) {
               this.og(Gn[2] + var3 + Gn[3] + var5);
            }
         }
      }
   }

   static {
      Xz();
   }

   @Override
   public String sB() {
      return Gn[1];
   }

   public AimO(UC var1) {
      super(var1, CheckType.AIM, Gn[0], Category.COMBAT);
   }
}
