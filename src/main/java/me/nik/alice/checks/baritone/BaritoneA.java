package me.nik.alice.checks.baritone;

import com.comphenix.protocol.wrappers.EnumWrappers.PlayerDigType;
import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.l;
import me.nik.alice.qn;
import me.nik.alice.wrappers.WrapperPlayClientBlockDig;

public class BaritoneA extends PacketCheck {
   private static String[] yo;

   static {
      rq();
   }

   private static void rq() {
      yo = new String[]{"A", "Checks if the player is automatically mining using baritone", "Dy: ", " Dp: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.uD()) {
         if (new WrapperPlayClientBlockDig(var1.dx()).getStatus() == PlayerDigType.STOP_DESTROY_BLOCK) {
            qn var3;
            if (Math.abs((var3 = this.Aa.dx()).getPitch()) != 90.0F) {
               float var2 = var3.b();
               float var4 = var3.UH();
               if (var2 != 0.0F && var4 != 0.0F) {
                  if (l.yM(var2) || l.yM(var4)) {
                     this.Aa(yo[2] + var2 + yo[3] + var4);
                     if (this.Aa() > this.dx()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.1);
                  }
               }
            }
         }
      }
   }

   public BaritoneA(UC var1) {
      super(var1, CheckType.BARITONE, yo[0], Category.WORLD, 3.0F);
   }

   @Override
   public String sB() {
      return yo[1];
   }
}
