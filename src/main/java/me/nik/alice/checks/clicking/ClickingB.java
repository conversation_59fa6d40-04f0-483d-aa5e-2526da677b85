package me.nik.alice.checks.clicking;

import me.nik.alice.*;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class ClickingB extends PacketCheck {
   private static String[] hO;

   public ClickingB(UC var1) {
      super(var1, CheckType.CLICKING, hO[0], Category.COMBAT, 10.0F);
   }

   private static void i9() {
      hO = new String[]{"B", "Checks for high amounts of clicks per second", "CPS: "};
   }

   @Override
   public String sB() {
      return hO[1];
   }

   static {
      i9();
   }

   @Override
   public void dx(DH var1) {
      if (var1.i2() && !this.Aa.dx().UH() && this.Aa.dx().Fh() >= 5L) {
         long var2;
         if ((var2 = this.Aa.dx().sB()) >= (long)me.nik.alice.Ww.dx.o4.Aa()) {
            this.Aa(hO[2].concat(String.valueOf(var2)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.25);
         }
      }
   }
}
