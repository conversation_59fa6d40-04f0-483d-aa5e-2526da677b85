package me.nik.alice.checks.clicking;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class ClickingF extends PacketCheck {
   private static String[] yB;
   private long dC;

   private static void _z/* $VF was: 5z*/() {
      yB = new String[]{"F", "Checks for repeated cps", "Repeated CPS, "};
   }

   @Override
   public String sB() {
      return yB[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.i2() && !this.Aa.dx().UH()) {
         long var2 = this.Aa.dx().sB();
         long var4 = this.dC;
         this.dC = var2;
         if (var2 > 12L && var2 == var4) {
            this.Aa(yB[2].concat(String.valueOf(var2)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(2.5);
         }
      }
   }

   public ClickingF(UC var1) {
      super(var1, CheckType.CLICKING, yB[0], Category.COMBAT, 10.0F);
   }

   static {
      5z();
   }
}
