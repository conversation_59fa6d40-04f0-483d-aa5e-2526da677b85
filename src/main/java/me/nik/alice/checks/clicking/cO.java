package me.nik.alice;

import java.util.ArrayList;
import java.util.Collections;

public class cO extends PacketCheck {
   private static String[] Cv;
   private final V0 dC = new V0(20);
   private long og;
   private long AC;

   @Override
   public String sB() {
      return Cv[1];
   }

   private static void MC() {
      Cv = new String[]{"C", "Checks for repeated click patterns", "CPS: ", " range delta: "};
   }

   static {
      MC();
   }

   public cO(UC var1) {
      super(var1, VL.Aa, Cv[0], b.dx, 2.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.i2() && !this.Aa.dx().UH()) {
         long var2;
         long var4 = (var2 = var1.getTimeStamp()) - this.og;
         this.og = var2;
         if (var4 > 0L) {
            if (var4 > 10000L) {
               this.dC.clear();
            }

            this.dC.add(var4);
            if (this.dC.h5()) {
               ArrayList var15;
               Collections.sort(var15 = new ArrayList(this.dC));
               long var7 = Math.abs((Long)var15.get(0) - (Long)var15.get(var15.size() - 1));
               long var9 = this.AC;
               this.AC = var7;
               long var11 = this.Aa.dx().sB();
               long var13 = Math.abs(var7 - var9);
               if (var11 > 7L && var13 < 3L) {
                  this.Aa(Cv[2] + var11 + Cv[3] + var13);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.5);
               }
            }
         }
      }
   }
}
