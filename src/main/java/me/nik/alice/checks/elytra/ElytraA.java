package me.nik.alice.checks.elytra;

import me.nik.alice.DH;
import me.nik.alice.ES;
import me.nik.alice.UC;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.l;

public class ElytraA extends PacketCheck {
   private static String[] Ok;

   private static void ZG() {
      Ok = new String[]{"A", "Checks for abnormal elytra speed", "Delta XZ: ", " delta Y: "};
   }

   @Override
   public String sB() {
      return Ok[1];
   }

   public ElytraA(UC var1) {
      super(var1, CheckType.ELYTRA, Ok[0], Category.MOVE, 5.0F);
   }

   static {
      ZG();
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().sX()) {
         ES var7;
         double var3 = (var7 = this.Aa.dx()).F7();
         double var5 = var7.Ch();
         if (var3 > 6.0 || var5 > 6.0 || var3 > 2.0 && (l.yM(var5) && var5 > 0.0 || Math.abs(var5) == 0.0) || var5 > 2.0 && var3 <= 0.0) {
            this.Aa(Ok[2] + var3 + Ok[3] + var5);
            if (this.AC() > this.dx() / 2.0F && me.nik.alice.Ww.dx.ZU.dC()) {
               this.Aa.getPlayer().setGliding(false);
            }

            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.og();
         }
      }
   }
}
