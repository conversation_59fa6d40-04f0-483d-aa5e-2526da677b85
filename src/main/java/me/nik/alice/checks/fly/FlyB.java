package me.nik.alice.checks.fly;

import me.nik.alice.*;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class FlyB extends PacketCheck {
   private static String[] 8l;
   private int iv;
   private long og;
   private long gz = -500L;

   private static void Gg() {
      8l = new String[]{"B", "Checks for fast flying packet sequence", "B: ", " Delta: "};
   }

   public FlyB(UC var1) {
      super(var1, CheckType.TIMER, 8l[0], Category.WORLD, 2.0F);
   }

   @Override
   public String sB() {
      return 8l[1];
   }

   @Override
   public void dx(DH var1) {
      if (!var1.M3()) {
         if (var1.qa()) {
            this.gz -= 50L;
         }
      } else if (Ku.Vm() >= 5000L && this.Aa.AC() >= 12500L && this.Aa.dx().Vm() >= 40 && this.Aa.dx().Zp() >= 80 && this.Aa.dx().WB() >= 40) {
         long var3 = var1.getTimeStamp();
         long var5 = this.og;
         this.og = var3;
         if (var5 != 0L) {
            long var7 = var3 - var5;
            this.iv = var7 < 5L ? this.iv + 1 : (this.iv > 0 ? this.iv - 1 : 0);
            this.gz += 50L;
            this.gz -= var7;
            if (this.gz > 25L) {
               if (this.iv > 10) {
                  this.gz -= 25L;
                  this.iv = 0;
               }

               if (this.gz > 50L) {
                  this.Aa(8l[2] + this.gz + 8l[3] + var7);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     this.og();
                  }

                  this.Aa(0.125);
                  this.gz = 0L;
                  return;
               }

               this.Aa(0.125);
            }
         }
      }
   }

   static {
      Gg();
   }
}
