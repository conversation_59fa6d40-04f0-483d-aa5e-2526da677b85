package me.nik.alice.checks.hitbox;

import java.util.List;
import java.util.stream.Collectors;

import me.nik.alice.*;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class HitBoxA extends PacketCheck {
   private static String[] 1t;

   @Override
   public void dx(DH var1) {
      if (var1.Tq()) {
         Fc var26;
         Entity var2 = (var26 = this.Aa.dx()).Aa();
         Sk var3 = this.Aa.dx();
         if (var26.dx().h5() && var2 instanceof Player && var2 == var26.og() && this.Aa.getPlayer().getGameMode() != GameMode.CREATIVE) {
            long var6 = (long)FastMath.max(25, var3.Zm());
            List var27 = (List)var26.dx().dx(var6, me.nik.alice.Ww.dx.oN.Aa()).stream().map(bV::dx).collect(Collectors.toList());
            Location var29;
            Vector var4 = (var29 = this.Aa.getPlayer().getEyeLocation()).toVector();
            Vector var30 = var29.getDirection();
            qn var5 = this.Aa.dx();
            double var13 = me.nik.alice.Ww.dx.fT.dx();
            double var15 = me.nik.alice.Ww.dx.C3.dx();
            boolean var8;
            double var18 = (var8 = this.Aa.dx().F7() < 0.1) ? var15 : var15 + 0.25;
            int var28;
            boolean var31 = (var28 = (int)var27.stream().filter(HitBoxA::broadCast).count()) <= me.nik.alice.Ww.dx.vg.Aa() && var5.sX() < 10.0F && var5.Vm() < 10.0F;
            long var22 = var3.Fh();
            long var24 = var3.h5();
            float var32 = var22 >= 25L && var24 >= 5000L && var6 <= 800L ? 1.0F : 0.25F;
            float var33 = var8 ? 2.5F : 1.0F;
            if (var31) {
               this.Aa(1t[2] + var28 + 1t[3] + var22 + 1t[4] + var24 + 1t[5]);
               if (this.broadCast((double)var32) > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa((double)var33);
            }
         }
      }
   }

   private static boolean dx(double var0, Vector var2, Vector var3, double var4, Location var6) {
      gW var7;
      (var7 = gW.dx(var6)).dx(var0, var0, var0);
      return var7.dx(var2, var3, var4).Vm() != -1.0;
   }

   public HitBoxA(UC var1) {
      super(var1, CheckType.HITBOX, 1t[0], Category.COMBAT, 9.0F);
   }

   @Override
   public String sB() {
      return 1t[1];
   }

   static {
      gY();
   }

   private static void gY() {
      1t = new String[]{"A", "Checks for hitbox expansion", "Count: ", " FM: ", "ms LL: ", " ms"};
   }
}
