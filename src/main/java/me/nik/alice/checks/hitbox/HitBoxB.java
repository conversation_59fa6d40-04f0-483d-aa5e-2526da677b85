package me.nik.alice.checks.hitbox;

import me.nik.alice.*;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;

public class HitBoxB extends PacketCheck {
   private static String[] qx;

   public HitBoxB(UC var1) {
      super(var1, CheckType.HITBOX, qx[0], Category.COMBAT, 9.0F);
   }

   @Override
   public String sB() {
      return qx[1];
   }

   static {
      AD();
   }

   private static void AD() {
      qx = new String[]{"B", "Checks for invalid hitbox offset", "Offset: ", " compensation: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.Tq()) {
         Entity var16;
         if ((var16 = this.Aa.dx().Aa()) instanceof Player && this.Aa.getPlayer().getGameMode() != GameMode.CREATIVE) {
            Location var2 = this.Aa.dx().Aa();
            Location var17 = var16.getLocation();
            double var7 = l.Aa(var2, var17);
            Sk var3;
            double var10 = (double)(var3 = this.Aa.dx()).jA() * me.nik.alice.Ww.dx.A.dx();
            if (var3.h5() < 10000L) {
               var10 += 75.0;
            }

            double var12 = l.dx(var2, var17);
            double var14 = gE.h5(var7) + var10;
            boolean var18 = var12 > var14;
            if (var3.Fh() < 25L) {
               this.Aa(0.5);
            }

            if (var18) {
               this.Aa(qx[2] + var12 + qx[3] + var10);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.75);
            }
         }
      }
   }
}
