package me.nik.alice.checks.velocity;

import me.nik.alice.*;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

@h5
public class VelocityA extends PacketCheck {
   private static String[] cp;

   private static void XI() {
      cp = new String[]{"A", "Checks for low vertical velocity", "Ratio: ", " fly ticks: ", " delta Y: "};
   }

   public VelocityA(UC var1) {
      super(var1, CheckType.VELOCITY, cp[0], Category.COMBAT, 1.0F);
   }

   @Override
   public String sB() {
      return cp[1];
   }

   static {
      XI();
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().b()) {
         Qk var13;
         if ((var13 = this.Aa.dx()).tU() == 1) {
            ES var2;
            int var3 = (var2 = this.Aa.dx()).u();
            double var5 = var2.Ch();
            double var7;
            if (!((var7 = var13.getVelocityY()) <= 0.0)) {
               double var9 = l.Aa(var5, var7);
               if (var3 <= 5 || !(var9 <= 0.0)) {
                  double var11 = var3 == 1 && var5 == 0.42F ? 85.0 : 99.9999;
                  if (var9 < var11) {
                     this.Aa(cp[2] + var9 + cp[3] + var3 + cp[4] + var5);
                     if (this.Aa() > this.dx()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.25);
                  }
               }
            }
         }
      }
   }
}
