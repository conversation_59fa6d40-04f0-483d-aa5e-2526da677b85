package me.nik.alice.checks.velocity;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.PacketCheck;

public class VelocityB extends PacketCheck {
   private static String[] m3;

   static {
      T5();
   }

   public r(UC var1) {
      super(var1, CheckType.VELOCITY, m3[0], Category.COMBAT);
   }

   @Override
   public void dx(DH var1) {
   }

   private static void T5() {
      m3 = new String[]{"B", "Checks for low vertical velocity ratio"};
   }

   @Override
   public String sB() {
      return m3[1];
   }
}
