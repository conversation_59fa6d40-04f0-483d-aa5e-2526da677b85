package me.nik.alice.checks.velocity;

import me.nik.alice.DH;
import me.nik.alice.UC;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.Category;
import me.nik.alice.checks.PacketCheck;

public class VelocityC extends PacketCheck {
   private static String[] 2i;

   static {
      Bb();
   }

   private static void Bb() {
      2i = new String[]{"C", "Checks for low horizontal velocity ratio"};
   }

   public VelocityC(UC var1) {
      super(var1, CheckType.VELOCITY, 2i[0], Category.COMBAT, 1.0F);
   }

   @Override
   public String sB() {
      return 2i[1];
   }

   @Override
   public void dx(DH var1) {
   }
}
