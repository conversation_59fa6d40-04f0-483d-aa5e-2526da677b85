package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import org.bukkit.GameMode;

public class d extends PacketCheck {
   private static String[] eP;

   @Override
   public void dx(DH var1) {
      if (var1.GE() && this.Aa.getPlayer().getGameMode() != GameMode.SPECTATOR) {
         this.og(eP[2]);
      }
   }

   public d(UC var1) {
      super(var1, CheckType.PACKET, eP[0], Category.WORLD);
   }

   static {
      RR();
   }

   private static void RR() {
      eP = new String[]{"Z", "Checks for invalid spectate packets", "Sent spectate packet while not in spectator"};
   }

   @Override
   public String sB() {
      return eP[1];
   }
}
