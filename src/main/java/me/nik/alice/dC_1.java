package me.nik.alice;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.util.ArrayList;

public final class dC {
   private static long dx;
   private static String[] tC;

   private static void dx(UC var0) {
      var0.dx().jD();
   }

   private dC() {
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private static void yM() {
      label79: {
         if (!"20488".isEmpty()) {
            String var10000 = "20488";

            try {
               var10000.getClass();
               break label79;
            } catch (Exception var11) {
            }
         }

         UH();
      }

      ArrayList var1 = new ArrayList();
      String var2 = Zh.Aa(tC[0]);

      label82: {
         try {
            var12 = new URL(var2);
         } catch (IOException var10) {
            boolean var10001 = false;
            break label82;
         }

         try {
            var13 = new BufferedReader(new InputStreamReader(var12.openStream()));
         } catch (IOException var9) {
            boolean var16 = false;
            break label82;
         }

         while (true) {
            String var3;
            label62: {
               try {
                  String var14 = var3 = var13.readLine();

                  try {
                     var14.hashCode();
                     break label62;
                  } catch (Exception var7) {
                  }
               } catch (IOException var8) {
                  boolean var17 = false;
                  break;
               }

               try {
                  var13.close();
               } catch (IOException var4) {
                  boolean var18 = false;
               }
               break;
            }

            ArrayList var15;
            String var20;
            try {
               var15 = var1;
               var20 = Zh.Aa(var3);
            } catch (IOException var6) {
               boolean var19 = false;
               break;
            }

            try {
               var15.add(var20);
            } catch (IOException var5) {
               boolean var21 = false;
               break;
            }
         }
      }

      if (var1.contains("20488") || var1.contains("20488")) {
         UH();
      }
   }

   public static boolean og() {
      long var0 = l.og(dx);
      dx = System.currentTimeMillis();
      if (var0 <= 90000L) {
         return true;
      } else if (!Alice.dx().dx().Aa() && Alice.dx().dx().dx()) {
         wf.Aa(dC::yM);
         return !"20488".isEmpty();
      } else {
         return false;
      }
   }

   private static void UH() {
      Alice.dx().dx().dx().values().forEach(dC::dx);
   }

   static {
      0C();
   }

   private static void _C/* $VF was: 0C*/() {
      tC = new String[]{"mJ0FWIjPl5C1cJsRZT7Qsd7pyYKG3Ee8wHTN4SncRMpyFddde62AO/x4ij9DHdQhY5zzCSuKbc1p6PxjX1nNULkTRgdcLkBqXxpxx08AFH8="};
   }
}
