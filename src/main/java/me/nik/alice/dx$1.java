package me.nik.alice;

import me.nik.fastmath.FastMath;
import org.bukkit.Effect;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;
import org.bukkit.scheduler.BukkitRunnable;

class dx$1 extends BukkitRunnable {
   final dx dx;
   final Player dx;
   final World dx;
   double dx;
   final Location dx;
   final String dx;
   final UC dx;

   dx$1(dx var1, Player var2, String var3, UC var4, Location var5, World var6) {
      this.dx = var1;
      this.dx = var2;
      this.dx = var3;
      this.dx = var4;
      this.dx = var5;
      this.dx = var6;
      this.dx = 0.0;
   }

   public void run() {
      if (!this.dx.isOnline()) {
         gE.h5(this.dx);
         this.cancel();
      } else {
         this.dx.dx().dx().dx(false, true);
         this.dx += Math.PI / 16;

         for (double var1 = 0.0; var1 < Math.PI * 3; var1++) {
            double var3 = 0.3 * ((Math.PI * 4) - this.dx) * FastMath.cos(this.dx + var1);
            double var5 = 0.2 * this.dx;
            double var7 = 0.3 * ((Math.PI * 4) - this.dx) * FastMath.sin(this.dx + var1);
            this.dx.add(var3, var5, var7);
            this.dx.playEffect(this.dx, Effect.MOBSPAWNER_FLAMES, null);
            this.dx.subtract(var3, var5, var7);
            if (this.dx > Math.PI * 5.0 / 2.0) {
               gE.h5(this.dx);
               this.cancel();
               return;
            }
         }
      }
   }
}
