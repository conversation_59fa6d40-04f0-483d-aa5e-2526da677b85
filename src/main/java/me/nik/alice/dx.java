package me.nik.alice;

import me.nik.alice.api.PunishAnimation;
import me.nik.alice.dx.1;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.entity.Player;

public class dx extends PunishAnimation {
   private static String[] Ye;

   public dx() {
      super(Ye[0]);
   }

   static {
      Wg();
   }

   @Override
   public void execute(Player var1, String var2) {
      Location var3 = var1.getLocation();
      World var4 = var1.getWorld();
      UC var5;
      UC var10000 = var5 = Alice.dx().dx().dx(var1);

      try {
         var10000.equals(null);
      } catch (Exception var6) {
         gE.h5(var2);
         return;
      }

      new 1(this, var1, var2, var5, var3, var4).runTaskTimerAsynchronously(Alice.dx(), 0L, 1L);
   }

   private static void Wg() {
      Ye = new String[]{"BURN"};
   }
}
