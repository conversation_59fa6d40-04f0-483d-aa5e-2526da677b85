package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class f7 extends PacketCheck {
   private static String[] F8;

   static {
      uJ();
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().VL()) {
         ES var2;
         if ((var2 = this.Aa.dx()).M3() >= 5 && !var2.Qt() && this.Aa.dx().h5() >= 120 && var2.QJ() >= 5) {
            if (var2.cO()) {
               this.Aa(F8[2]);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.25);
            }
         }
      }
   }

   private static void uJ() {
      F8 = new String[]{"D", "Checks for invalid ground while swimming", "Spoofed ground while swimming"};
   }

   public f7(UC var1) {
      super(var1, CheckType.JESUS, F8[0], Category.MOVE, 5.0F);
   }

   @Override
   public String sB() {
      return F8[1];
   }
}
