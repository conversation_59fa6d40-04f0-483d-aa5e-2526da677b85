package me.nik.alice;

import java.util.LinkedList;
import java.util.List;
import org.bukkit.Location;
import org.bukkit.World;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;

public final class fD {
   private static String[] aP;

   public static boolean sB(Location var0) {
      return !var0.getWorld().isChunkLoaded(var0.getBlockX() >> 4, var0.getBlockZ() >> 4);
   }

   public static Block dx(World var0, int var1, int var2, int var3) {
      return var0.isChunkLoaded(var1 >> 4, var3 >> 4) ? var0.getBlockAt(var1, var2, var3) : null;
   }

   public static boolean Aa(Location var0) {
      Location var1 = var0.clone();
      double var4 = var0.getX();
      double var6 = var0.getY();
      double var8 = var0.getZ();
      double var10 = -0.299999;

      while (var10 <= 0.299999) {
         for (double var12 = -0.299999; var12 <= 0.299999; var10 += 0.299999) {
            var1.setX(var4 + var10);
            var1.setZ(var8 + var12);
            var1.setY(var6);
            Block var16 = dx(var1);
            var1.setY(var6 - 1.0);
            Block var2 = dx(var1);
            Block var10000 = var16;

            try {
               var10000.hashCode();
            } catch (Exception var15) {
               continue;
            }

            var10000 = var2;

            try {
               var10000.toString();
            } catch (Exception var14) {
               continue;
            }

            if (var16.getType().isSolid() || var2.getType().isSolid()) {
               return true;
            }

            var12 += 0.299999;
         }

         return true;
      }

      return false;
   }

   private fD() {
   }

   public static Block dx(Location var0) {
      return var0.getWorld().isChunkLoaded(var0.getBlockX() >> 4, var0.getBlockZ() >> 4) ? var0.getBlock() : null;
   }

   public static boolean AC(Location var0) {
      Block var1;
      Block var10000 = var1 = dx(var0);

      try {
         var10000.equals(null);
      } catch (Exception var3) {
         return false;
      }

      String var2 = var1.getType().name();
      return !var1.isEmpty()
         && !var2.contains(aP[0])
         && !var2.contains(aP[1])
         && !var2.contains(aP[2])
         && !var2.contains(aP[3])
         && !var2.contains(aP[4])
         && !var2.contains(aP[5])
         && !var1.isLiquid()
         && var0.getY() % 1.0 != 0.0;
   }

   private static void GQ() {
      aP = new String[]{"LADDER", "VINE", "ROSE", "FLOWER", "GRASS", "PLATE"};
   }

   static {
      GQ();
   }

   public static List dx(Location var0, int var1) {
      LinkedList var2 = new LinkedList();
      World var3;
      World var10000 = var3 = var0.getWorld();

      try {
         var10000.toString();
      } catch (Exception var10) {
         return var2;
      }

      int var4 = var0.getBlockX();
      int var5 = var0.getBlockY();
      int var12 = var0.getBlockZ();

      for (int var6 = var4 - var1; var6 <= var4 + var1; var6++) {
         for (int var7 = var5 - var1; var7 <= var5 + var1 + 1; var7++) {
            for (int var8 = var12 - var1; var8 <= var12 + var1; var8++) {
               Block var9;
               Block var13 = var9 = dx(var3, var6, var7, var8);

               try {
                  var13.equals(null);
               } catch (Exception var11) {
                  break;
               }

               var2.add(var9);
            }
         }
      }

      return var2;
   }

   public static List dx(Location var0) {
      boolean var15 = VwrV;
      LinkedList var1 = new LinkedList();
      Location var19;
      double var3 = (var19 = var0.clone()).getX();
      double var5 = var19.getY();
      double var7 = var19.getZ();
      double var11 = -1.0;

      while (var11 <= 1.0) {
         double var13 = -1.0;

         while (var13 <= 1.0) {
            var19.setX(var3 + var11);
            var19.setZ(var7 + var13);
            var19.setY(var5 + 1.0);
            Block var2 = dx(var19);
            var19.setY(var5);
            Block var9 = dx(var19);
            var19.setY(var5 - 1.0);
            Block var10 = dx(var19);
            Block var10000 = var2;

            try {
               var10000.equals(null);
            } catch (Exception var18) {
               break;
            }

            var10000 = var9;

            try {
               var10000.toString();
            } catch (Exception var17) {
               break;
            }

            var10000 = var10;

            try {
               var10000.toString();
            } catch (Exception var16) {
               break;
            }

            if (!var1.contains(var2)) {
               var1.add(var2);
            }

            if (!var1.contains(var9)) {
               var1.add(var9);
            }

            if (!var1.contains(var10)) {
               var1.add(var10);
            }

            var13++;
            if (var15) {
               throw null;
            }
         }

         var11++;
         if (var15) {
            throw null;
         }
      }

      return var1;
   }

   public static List Aa(Location var0) {
      LinkedList var1 = new LinkedList();
      Location var2 = var0.clone();
      double var3 = var0.getX();
      double var5 = var0.getY();
      double var7 = var0.getZ();

      for (double var11 = -0.6; var11 <= 0.6; var11 += 0.6) {
         for (double var13 = -0.6; var13 <= 0.6; var13 += 0.6) {
            var2.setX(var3 + var11);
            var2.setZ(var7 + var13);
            var2.setY(var5);
            Block var17 = dx(var2);
            var2.setY(var5 - 1.0);
            Block var9 = dx(var2);
            Block var10000 = var17;

            try {
               var10000.getClass();
            } catch (Exception var16) {
               break;
            }

            var10000 = var9;

            try {
               var10000.getClass();
            } catch (Exception var15) {
               break;
            }

            if (!var17.isEmpty()) {
               var1.add(var17);
            }

            if (!var9.isEmpty()) {
               var1.add(var9);
            }
         }
      }

      return var1;
   }

   public static boolean dx(Location var0) {
      double var1 = Math.abs(var0.getX() % 1.0);
      double var3 = Math.abs(var0.getZ() % 1.0);
      return var1 > 0.6999999 && var1 < 0.7
         || var3 > 0.6999999 && var3 < 0.7
         || var1 > 0.300000011 && var1 < 0.300000012
         || var3 > 0.300000011 && var3 < 0.300000012;
   }

   public static boolean og(Location var0) {
      Location var1 = var0.clone();
      double var4 = var0.getX();
      double var6 = var0.getY();
      double var8 = var0.getZ();
      var1.setY(var6 + 0.5001);

      for (double var10 = -0.299999; var10 <= 0.299999; var10 += 0.299999) {
         for (double var12 = -0.299999; var12 <= 0.299999; var12 += 0.299999) {
            var1.setX(var4 + var10);
            var1.setZ(var8 + var12);
            Block var15;
            Block var10000 = var15 = dx(var1);

            try {
               var10000.getClass();
            } catch (Exception var14) {
               break;
            }

            Block var16;
            if (!(var16 = var15.getRelative(BlockFace.UP, 2)).isEmpty() && !var16.isLiquid()) {
               return true;
            }
         }
      }

      return false;
   }
}
