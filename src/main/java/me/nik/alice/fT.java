package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import me.nik.fastmath.FastMath;
import org.bukkit.block.Block;
import org.bukkit.event.Event;
import org.bukkit.event.block.BlockPlaceEvent;

@h5
public class fT extends EventCheck {
   private static String[] 2d;
   private long og;
   private int gz;

   static {
      d6();
   }

   @Override
   public String sB() {
      return 2d[4];
   }

   @Override
   public void on(Event var1) {
      if (var1 instanceof BlockPlaceEvent) {
         BlockPlaceEvent var15;
         if (!(var15 = (BlockPlaceEvent)var1).getPlayer().getAllowFlight()) {
            Block var16;
            if ((var16 = var15.getBlockPlaced()).getType().isSolid()) {
               int var17 = var16.getY();
               int var2 = this.gz;
               this.gz = var17;
               if (var17 == var2) {
                  long var6 = l.og(this.og);
                  this.og = this.on();
                  ES var18;
                  double var9 = (var18 = this.Aa.dx()).DB();
                  double var11 = var18.F7();
                  double var13 = 0.21 + (double)(FastMath.max(0.2F, this.Aa.getPlayer().getWalkSpeed()) - 0.2F);
                  int var19;
                  if ((var19 = var18.dx().sB(1000L)) > 0) {
                     var13 += (double)((float)var19 * 0.079F);
                  }

                  if (this.Aa.dx().VL() < 2 && var11 > var13 && var9 < 0.1 && var6 < 300L) {
                     this.Aa(2d[1] + var11 + 2d[2] + var9 + 2d[3] + var6);
                     if (this.Aa() > this.on()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.25);
                  }
               }
            }
         }
      }
   }

   private static void d6() {
      2d = new String[]{"G", "Delta XZ: ", " accel: ", " delta: ", "Checks for impossible rotations when bridging"};
   }

   public fT(UC var1) {
      super(var1, CheckType.SCAFFOLD, "G", Category.WORLD, 3.0F);
   }
}
