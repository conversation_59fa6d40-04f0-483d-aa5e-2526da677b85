package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffectType;

public class fa extends PacketCheck {
   private static String[] iz;

   static {
      Jw();
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().VL()) {
         ES var9;
         if (!(var9 = this.Aa.dx()).Qt() && var9.QJ() >= 5 && var9.M3() >= 5) {
            Player var2 = this.Aa.getPlayer();
            double var4 = var9.F7();
            float var3 = FastMath.max(0.2F, this.Aa.getPlayer().getWalkSpeed());
            double var7 = this.Aa.dx().Aa(RH.sX) ? (double)var3 * 2.0 : (double)var3 * 1.15;
            var4 -= var4 / 100.0 * (double)JA.dx(var2, PotionEffectType.SPEED) * 20.0;
            var7 += Math.abs(var2.getVelocity().getY()) * 0.32;
            int var11;
            if ((var11 = JA.Aa(this.Aa.getPlayer())) > 0) {
               var7 += 1.5 * (double)var11;
            }

            int var10;
            if (!w.v() && (var10 = var9.dx().AC(2000L)) > 0) {
               var7 += 2.5 * (double)var10;
            }

            if (var4 > var7) {
               this.Aa(iz[2].concat(String.valueOf(var4)));
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.25);
            }
         }
      }
   }

   @Override
   public String sB() {
      return iz[1];
   }

   private static void Jw() {
      iz = new String[]{"B", "Checks for high swimming speed", "Acceleration: "};
   }

   public fa(UC var1) {
      super(var1, CheckType.JESUS, iz[0], Category.MOVE, 5.0F);
   }
}
