package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientUseEntity;

public class fq extends PacketCheck {
   private WU dx;
   private static String[] Yj;

   public fq(UC var1) {
      super(var1, CheckType.KILLAURA, Yj[0], Category.COMBAT);
   }

   @Override
   public String sB() {
      return Yj[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.Tq()) {
         int var2;
         if ((var2 = me.nik.alice.Ww.dx.AC.Aa()) == 0 || Alice.getAPI().getCombatViolations(this.Aa.getPlayer()) >= var2) {
            WU var10000 = this.dx;

            try {
               var10000.toString();
            } catch (Exception var3) {
               this.dx = new WU(this.Aa.getPlayer());
            }

            if (!this.dx.tm()) {
               this.dx.a();
            }

            if (new WrapperPlayClientUseEntity(var1.dx()).getTargetID() == this.dx.dC()) {
               this.og(Yj[2]);
            }
         }
      }
   }

   private static void f0() {
      Yj = new String[]{"A", "Checks for some types of killauras by spawning an npc near the player", "Hit the NPC"};
   }

   static {
      f0();
   }
}
