package me.nik.alice;

import org.bukkit.block.BlockFace;

class gE$1 {
   static final int[] sB = new int[BlockFace.values().length];

   static {
      int[] var10000 = sB;

      label54: {
         int var10001;
         byte var10002;
         try {
            var10001 = BlockFace.UP.ordinal();
            var10002 = 1;
         } catch (NoSuchFieldError var5) {
            break label54;
         }

         var10000[var10001] = var10002;
      }

      try {
         sB[BlockFace.DOWN.ordinal()] = 2;
      } catch (NoSuchFieldError var2) {
      }

      try {
         sB[BlockFace.EAST.ordinal()] = 3;
      } catch (NoSuchFieldError var1) {
      }

      label49: {
         int var9;
         byte var12;
         try {
            var10000 = sB;
            var9 = BlockFace.SOUTH.ordinal();
            var12 = 4;
         } catch (NoSuchFieldError var4) {
            break label49;
         }

         var10000[var9] = var12;
      }

      label45: {
         int var10;
         byte var13;
         try {
            var10000 = sB;
            var10 = BlockFace.WEST.ordinal();
            var13 = 5;
         } catch (NoSuchFieldError var3) {
            break label45;
         }

         var10000[var10] = var13;
      }

      var10000 = sB;

      int var11;
      byte var14;
      try {
         var11 = BlockFace.NORTH.ordinal();
         var14 = 6;
      } catch (NoSuchFieldError var0) {
         return;
      }

      var10000[var11] = var14;
   }
}
