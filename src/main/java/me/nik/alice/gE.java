package me.nik.alice;

import com.google.common.base.Charsets;
import java.io.File;
import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.util.Collection;
import java.util.Iterator;
import java.util.List;
import java.util.Random;
import me.nik.alice.gE.1;
import org.bukkit.Bukkit;
import org.bukkit.Location;
import org.bukkit.block.BlockFace;
import org.bukkit.configuration.InvalidConfigurationException;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.inventory.PlayerInventory;

public final class gE {
   private static String[] kq;

   static {
      zn();
   }

   public static void h5(String var0) {
      if (!var0.isEmpty() && !Alice.dx().dx().Aa() && Alice.dx().dx().dx()) {
         if (Bukkit.isPrimaryThread()) {
            Bukkit.dispatchCommand(Bukkit.getConsoleSender(), var0);
         } else {
            wf.dx(gE::Fh);
         }
      }
   }

   public static double h5(double var0) {
      if (var0 > 1.5 && var0 < 2.0) {
         return 30.0;
      } else if (var0 > 2.0 && var0 < 2.5) {
         return 25.0;
      } else if (var0 > 2.5 && var0 < 3.0) {
         return 17.0;
      } else {
         return var0 > 3.0 ? 15.0 : Double.POSITIVE_INFINITY;
      }
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   public static YamlConfiguration dx(File var0) {
      YamlConfiguration var1 = new YamlConfiguration();

      IOException var10000;
      label27: {
         try {
            var4 = new FileInputStream(var0);
         } catch (InvalidConfigurationException | IOException var3) {
            var10000 = var3;
            boolean var10001 = false;
            break label27;
         }

         YamlConfiguration var5 = var1;
         InputStreamReader var6 = new InputStreamReader;

         try {
            var6.<init>(var4, Charsets.UTF_8);
            var5.load(var6);
            return var1;
         } catch (InvalidConfigurationException | IOException var2) {
            var10000 = var2;
            boolean var7 = false;
         }
      }

      var10000.printStackTrace();
      return var1;
   }

   public static String Aa(double var0, double var2) {
      StringBuilder var4 = new StringBuilder(F.Aa.getMessage());
      int var5 = (int)(100.0 / (var2 / var0));
      String var1 = F.AC.getMessage();

      for (int var7 = 0; (double)var7 <= (double)var5 / 5.5 - 1.0; var7++) {
         var4.append(var1);
      }

      String var8 = F.sB.getMessage();

      for (int var6 = 0; (double)var6 <= (double)(100 - var5) / 5.5 - 1.0; var6++) {
         var4.append(var8);
      }

      var4.append(F.og.getMessage());
      return var4.toString();
   }

   private gE() {
   }

   private static void Fh(String var0) {
      Bukkit.dispatchCommand(Bukkit.getConsoleSender(), var0);
   }

   public static Object dx(Collection var0) {
      if (var0.size() == 0) {
         return null;
      } else {
         int var1 = new Random().nextInt(var0.size());
         if (var0 instanceof List) {
            return ((List)var0).get(var1);
         } else {
            Iterator var3 = var0.iterator();

            for (int var2 = 0; var2 < var1; var2++) {
               var3.next();
            }

            return var3.next();
         }
      }
   }

   // $VF: Unable to simplify switch on enum
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   public static Location dx(BlockFace var0, Location var1) {
      switch (1.sB[var0.ordinal()]) {
         case 1:
            return var1.clone().add(0.0, -1.0, 0.0);
         case 2:
            return var1.clone().add(0.0, 1.0, 0.0);
         case 3:
         case 4:
            return var1;
         case 5:
            return var1.clone().add(1.0, 0.0, 0.0);
         case 6:
            return var1.clone().add(0.0, 0.0, 1.0);
         default:
            return null;
      }
   }

   public static String dx(double var0, double var2) {
      return l.Aa(l.og(var0, var2), 2) + kq[0];
   }

   // $VF: Unable to simplify switch on enum
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   public static boolean dx(Location var0, Location var1, BlockFace var2) {
      switch (1.sB[var2.ordinal()]) {
         case 1:
            if (var1.getY() > var0.getY()) {
               return true;
            }

            return false;
         case 2:
            if (var1.getY() < var0.getY()) {
               return true;
            }

            return false;
         case 3:
            if (var1.getX() > var0.getX()) {
               return true;
            }

            return false;
         case 4:
            if (var1.getZ() > var0.getZ()) {
               return true;
            }

            return false;
         case 5:
            if (var1.getX() < var0.getX()) {
               return true;
            }

            return false;
         case 6:
            if (var1.getZ() < var0.getZ()) {
               return true;
            }

            return false;
         default:
            return true;
      }
   }

   public static void h5(Player var0) {
      int var1;
      PlayerInventory var2;
      if ((var1 = (var2 = var0.getInventory()).getHeldItemSlot()) == 0) {
         var1++;
      } else {
         var1--;
      }

      var2.setHeldItemSlot(var1);
   }

   private static void zn() {
      kq = new String[]{"%"};
   }
}
