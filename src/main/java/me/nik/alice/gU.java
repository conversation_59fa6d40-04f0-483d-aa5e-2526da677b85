package me.nik.alice;

import me.nik.alice.checks.Check;
import org.bukkit.event.Event;

public abstract class gU extends Check {
   private static String[] xi;

   private static void lK() {
      xi = new String[]{"", ""};
   }

   public gU(UC var1, VL var2, b var3) {
      super(var1, var2, xi[1], var3, 0.0F);
   }

   public gU(UC var1, VL var2, b var3, float var4) {
      super(var1, var2, xi[0], var3, var4);
   }

   public gU(UC var1, VL var2, String var3, b var4, float var5) {
      super(var1, var2, var3, var4, var5);
   }

   public gU(UC var1, VL var2, String var3, b var4) {
      super(var1, var2, var3, var4, 0.0F);
   }

   static {
      lK();
   }

   public abstract void dx(Event var1);
}
