package me.nik.alice;

import me.nik.fastmath.FastMath;
import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Entity;
import org.bukkit.util.BoundingBox;
import org.bukkit.util.Vector;

public class gW {
   private static String[] gk;
   private double hW;
   private double tm;
   private double Ch;
   private double zP;
   private double x;
   private double gz;

   public gW dx() {
      try {
         return (gW)super.clone();
      } catch (CloneNotSupportedException var2) {
         throw new Error(var2);
      }
   }

   public Vector Aa() {
      return new Vector(this.og(), this.AC(), this.sB());
   }

   public gW dx(gW var1) {
      return this.dx(var1.og(), var1.AC(), var1.sB(), var1.dC(), var1.Zh(), var1.h5());
   }

   public gW dx(double var1) {
      return this.Aa(var1, var1, var1, var1, var1, var1);
   }

   public double Fh() {
      return this.dC() - this.og();
   }

   public gW dx(BlockFace var1, double var2) {
      return var1 == BlockFace.SELF ? this : this.dx(var1.getDirection(), var2);
   }

   public double Zh() {
      return this.hW;
   }

   public boolean dx(gW var1) {
      return this.dx(var1.og(), var1.AC(), var1.sB(), var1.dC(), var1.Zh(), var1.h5());
   }

   public gW AC(double var1, double var3, double var5) {
      return var1 == 0.0 && var3 == 0.0 && var5 == 0.0
         ? this
         : this.dx(this.og() + var1, this.AC() + var3, this.sB() + var5, this.dC() + var1, this.Zh() + var3, this.h5() + var5);
   }

   private static void fD() {
      gk = new String[]{"NmsBoundingBox [minX=", ", minY=", ", minZ=", ", maxX=", ", maxY=", ", maxZ=", "]"};
   }

   public double WB() {
      return this.sB() + this.b() * 0.5;
   }

   public gW dx(double var1, double var3, double var5, double var7) {
      if (var7 == 0.0) {
         return this;
      } else if (var1 == 0.0 && var3 == 0.0 && var5 == 0.0) {
         return this;
      } else {
         double var9 = var1 < 0.0 ? -var1 * var7 : 0.0;
         double var11 = var3 < 0.0 ? -var3 * var7 : 0.0;
         double var13 = var5 < 0.0 ? -var5 * var7 : 0.0;
         double var15 = var1 > 0.0 ? var1 * var7 : 0.0;
         double var17 = var3 > 0.0 ? var3 * var7 : 0.0;
         double var19 = var5 > 0.0 ? var5 * var7 : 0.0;
         return this.Aa(var9, var11, var13, var15, var17, var19);
      }
   }

   public double UH() {
      return this.VL() * this.Fh() * this.b();
   }

   public Object clone() {
      return this.dx();
   }

   public double og() {
      return this.gz;
   }

   public double sX() {
      return this.AC() + this.VL() * 0.5;
   }

   public gW sB(Vector var1) {
      return this.AC(var1.getX(), var1.getY(), var1.getZ());
   }

   public static gW dx(Vector var0) {
      double var1 = var0.getX();
      double var3 = var0.getY();
      double var5 = var0.getZ();
      double var7 = var1 - 0.3;
      double var9 = var5 - 0.3;
      double var11 = var1 + 0.3;
      double var13 = var3 + 1.8;
      double var15 = var5 + 0.3;
      return new gW(var7, var3, var9, var11, var13, var15);
   }

   public gW Aa(double var1, double var3, double var5) {
      return this.dx(var1, var3, var5, 1.0);
   }

   public double sB() {
      return this.x;
   }

   private boolean dx(double var1, double var3, double var5, double var7, double var9, double var11) {
      return this.og() < var7 && this.dC() > var1 && this.AC() < var9 && this.Zh() > var3 && this.sB() < var11 && this.h5() > var5;
   }

   public gW dx(double var1, double var3, double var5, double var7, double var9, double var11) {
      this.gz = FastMath.min(var1, var7);
      this.tm = FastMath.min(var3, var9);
      this.x = FastMath.min(var5, var11);
      this.zP = FastMath.max(var1, var7);
      this.hW = FastMath.max(var3, var9);
      this.Ch = FastMath.max(var5, var11);
      return this;
   }

   public boolean Aa(gW var1) {
      return this.Aa(var1.og(), var1.AC(), var1.sB(), var1.dC(), var1.Zh(), var1.h5());
   }

   public double h5() {
      return this.Ch;
   }

   public boolean Aa(Vector var1, Vector var2) {
      double var3 = var1.getX();
      double var5 = var1.getY();
      double var7 = var1.getZ();
      double var9 = var2.getX();
      double var11 = var2.getY();
      double var13 = var2.getZ();
      return this.Aa(var3, var5, var7, var9, var11, var13);
   }

   public static gW dx(Location var0, double var1, double var3, double var5) {
      return new gW(var0.getX() - var1, var0.getY() - var3, var0.getZ() - var5, var0.getX() + var1, var0.getY() + var3, var0.getZ() + var5);
   }

   public double VL() {
      return this.Zh() - this.AC();
   }

   public double dC() {
      return this.zP;
   }

   public boolean equals(Object var1) {
      if (this == var1) {
         return true;
      } else if (!(var1 instanceof gW)) {
         return false;
      } else {
         var1 = var1;
         return Double.doubleToLongBits(this.dC()) == Double.doubleToLongBits(var1.dC())
            && Double.doubleToLongBits(this.Zh()) == Double.doubleToLongBits(var1.Zh())
            && Double.doubleToLongBits(this.h5()) == Double.doubleToLongBits(var1.h5())
            && Double.doubleToLongBits(this.og()) == Double.doubleToLongBits(var1.og())
            && Double.doubleToLongBits(this.AC()) == Double.doubleToLongBits(var1.AC())
            && Double.doubleToLongBits(this.sB()) == Double.doubleToLongBits(var1.sB());
      }
   }

   public Vector AC() {
      return new Vector(this.yM(), this.sX(), this.WB());
   }

   public gW og(gW var1) {
      double var2 = FastMath.max(this.og(), var1.og());
      double var4 = FastMath.max(this.AC(), var1.AC());
      double var6 = FastMath.max(this.sB(), var1.sB());
      double var8 = FastMath.min(this.dC(), var1.dC());
      double var10 = FastMath.min(this.Zh(), var1.Zh());
      double var12 = FastMath.min(this.h5(), var1.h5());
      return this.dx(var2, var4, var6, var8, var10, var12);
   }

   public gW og(double var1, double var3, double var5) {
      double var7 = FastMath.min(this.og(), var1);
      double var9 = FastMath.min(this.AC(), var3);
      double var11 = FastMath.min(this.sB(), var5);
      double var13 = FastMath.max(this.dC(), var1);
      double var15 = FastMath.max(this.Zh(), var3);
      double var17 = FastMath.max(this.h5(), var5);
      return var7 == this.og() && var9 == this.AC() && var11 == this.sB() && var13 == this.dC() && var15 == this.Zh() && var17 == this.h5()
         ? this
         : this.dx(var7, var9, var11, var13, var15, var17);
   }

   public boolean dx(double var1, double var3, double var5) {
      return var1 >= this.og() && var1 < this.dC() && var3 >= this.AC() && var3 < this.Zh() && var5 >= this.sB() && var5 < this.h5();
   }

   public static gW dx(Vector var0, Vector var1) {
      return new gW(var0.getX(), var0.getY(), var0.getZ(), var1.getX(), var1.getY(), var1.getZ());
   }

   public static gW dx(Entity var0) {
      BoundingBox var1 = var0.getBoundingBox();
      return new gW(var1.getMinX(), var1.getMinY(), var1.getMinZ(), var1.getMaxX(), var1.getMaxY(), var1.getMaxZ());
   }

   public static gW dx(Location var0, Location var1) {
      return new gW(var0.getX(), var0.getY(), var0.getZ(), var1.getX(), var1.getY(), var1.getZ());
   }

   public gW Aa(Vector var1) {
      return this.dx(var1.getX(), var1.getY(), var1.getZ());
   }

   public gW og(Location var1) {
      return this.AC(var1.getX(), var1.getY(), var1.getZ());
   }

   public double b() {
      return this.h5() - this.sB();
   }

   public gW(double var1, double var3, double var5, double var7, double var9, double var11) {
      this.dx(var1, var3, var5, var7, var9, var11);
   }

   public double AC() {
      return this.tm;
   }

   public boolean dx(Vector var1, Vector var2) {
      double var3 = var1.getX();
      double var5 = var1.getY();
      double var7 = var1.getZ();
      double var9 = var2.getX();
      double var11 = var2.getY();
      double var13 = var2.getZ();
      return this.dx(var3, var5, var7, var9, var11, var13);
   }

   public gW og(Vector var1) {
      return this.dx(var1.getX(), var1.getY(), var1.getZ(), 1.0);
   }

   public gW dx(Vector var1, double var2) {
      return this.dx(var1.getX(), var1.getY(), var1.getZ(), var2);
   }

   static {
      fD();
   }

   public gW Aa(double var1, double var3, double var5, double var7, double var9, double var11) {
      if (var1 == 0.0 && var3 == 0.0 && var5 == 0.0 && var7 == 0.0 && var9 == 0.0 && var11 == 0.0) {
         return this;
      } else {
         double var13 = this.og() - var1;
         double var15 = this.AC() - var3;
         double var17 = this.sB() - var5;
         double var19 = this.dC() + var7;
         double var21 = this.Zh() + var9;
         double var23 = this.h5() + var11;
         if (var13 > var19) {
            double var25 = this.yM();
            if (var19 >= var25) {
               var13 = var19;
            } else if (var13 <= var25) {
               var19 = var13;
            } else {
               var13 = var25;
               var19 = var25;
            }
         }

         if (var15 > var21) {
            double var27 = this.sX();
            if (var21 >= var27) {
               var15 = var21;
            } else if (var15 <= var27) {
               var21 = var15;
            } else {
               var15 = var27;
               var21 = var27;
            }
         }

         if (var17 > var23) {
            double var28 = this.WB();
            if (var23 >= var28) {
               var17 = var23;
            } else if (var17 <= var28) {
               var23 = var17;
            } else {
               var17 = var28;
               var23 = var28;
            }
         }

         return this.dx(var13, var15, var17, var19, var21, var23);
      }
   }

   public String toString() {
      return gk[0] + this.og() + gk[1] + this.AC() + gk[2] + this.sB() + gk[3] + this.dC() + gk[4] + this.Zh() + gk[5] + this.h5() + gk[6];
   }

   public static gW dx(Block var0) {
      return new gW(
         (double)var0.getX(), (double)var0.getY(), (double)var0.getZ(), (double)(var0.getX() + 1), (double)(var0.getY() + 1), (double)(var0.getZ() + 1)
      );
   }

   public Vector og() {
      return new Vector(this.dC(), this.Zh(), this.h5());
   }

   public static gW dx(Location var0) {
      double var1 = var0.getX();
      double var3 = var0.getY();
      double var5 = var0.getZ();
      double var7 = var1 - 0.3;
      double var9 = var5 - 0.3;
      double var11 = var1 + 0.3;
      double var13 = var3 + 1.8;
      double var15 = var5 + 0.3;
      return new gW(var7, var3, var9, var11, var13, var15);
   }

   public boolean dx(Vector var1) {
      return this.dx(var1.getX(), var1.getY(), var1.getZ());
   }

   public gW Aa(gW var1) {
      if (this.Aa(var1)) {
         return this;
      } else {
         double var2 = FastMath.min(this.og(), var1.og());
         double var4 = FastMath.min(this.AC(), var1.AC());
         double var6 = FastMath.min(this.sB(), var1.sB());
         double var8 = FastMath.max(this.dC(), var1.dC());
         double var10 = FastMath.max(this.Zh(), var1.Zh());
         double var12 = FastMath.max(this.h5(), var1.h5());
         return this.dx(var2, var4, var6, var8, var10, var12);
      }
   }

   public static gW dx(Block var0, Block var1) {
      int var2 = var0.getX();
      int var3 = var0.getY();
      int var9 = var0.getZ();
      int var4 = var1.getX();
      int var5 = var1.getY();
      int var11 = var1.getZ();
      int var6 = FastMath.min(var2, var4);
      int var7 = FastMath.min(var3, var5);
      int var8 = FastMath.min(var9, var11);
      var2 = FastMath.max(var2, var4) + 1;
      var3 = FastMath.max(var3, var5) + 1;
      int var10 = FastMath.max(var9, var11) + 1;
      return new gW((double)var6, (double)var7, (double)var8, (double)var2, (double)var3, (double)var10);
   }

   public int hashCode() {
      long var2 = Double.doubleToLongBits(this.dC());
      int var1 = 31 + (int)(var2 ^ var2 >>> 32);
      var2 = Double.doubleToLongBits(this.Zh());
      var1 = var1 * 31 + (int)(var2 ^ var2 >>> 32);
      var2 = Double.doubleToLongBits(this.h5());
      var1 = var1 * 31 + (int)(var2 ^ var2 >>> 32);
      var2 = Double.doubleToLongBits(this.og());
      var1 = var1 * 31 + (int)(var2 ^ var2 >>> 32);
      var2 = Double.doubleToLongBits(this.AC());
      var1 = var1 * 31 + (int)(var2 ^ var2 >>> 32);
      var2 = Double.doubleToLongBits(this.sB());
      return var1 * 31 + (int)(var2 ^ var2 >>> 32);
   }

   public gW AC(Vector var1) {
      return this.og(var1.getX(), var1.getY(), var1.getZ());
   }

   public static gW dx(Vector var0, double var1, double var3, double var5) {
      return new gW(var0.getX() - var1, var0.getY() - var3, var0.getZ() - var5, var0.getX() + var1, var0.getY() + var3, var0.getZ() + var5);
   }

   public gW dx(double var1, double var3, double var5) {
      return this.Aa(var1, var3, var5, var1, var3, var5);
   }

   public double yM() {
      return this.og() + this.Fh() * 0.5;
   }

   public gW Aa(Location var1) {
      return this.og(var1.getX(), var1.getY(), var1.getZ());
   }

   public s6 dx(Vector var1, Vector var2, double var3) {
      double var5 = var1.getX();
      double var7 = var1.getY();
      double var9 = var1.getZ();
      Vector var45;
      Vector var10000 = var45 = var2.clone();
      var10000.setX(var10000.getX() == 0.0 ? 0.0 : var45.getX());
      var45.setY(var45.getY() == 0.0 ? 0.0 : var45.getY());
      var45.setZ(var45.getZ() == 0.0 ? 0.0 : var45.getZ());
      double var12 = var45.getX();
      double var14 = var45.getY();
      double var16 = var45.getZ();
      double var18 = 1.0 / var12;
      double var20 = 1.0 / var14;
      double var22 = 1.0 / var16;
      BlockFace var6;
      double var24;
      double var26;
      BlockFace var47;
      if (var12 >= 0.0) {
         var24 = (this.og() - var5) * var18;
         var26 = (this.dC() - var5) * var18;
         var47 = BlockFace.WEST;
         var6 = BlockFace.EAST;
      } else {
         var24 = (this.dC() - var5) * var18;
         var26 = (this.og() - var5) * var18;
         var47 = BlockFace.EAST;
         var6 = BlockFace.WEST;
      }

      BlockFace var8;
      double var30;
      double var32;
      BlockFace var48;
      if (var14 >= 0.0) {
         var30 = (this.AC() - var7) * var20;
         var32 = (this.Zh() - var7) * var20;
         var48 = BlockFace.DOWN;
         var8 = BlockFace.UP;
      } else {
         var30 = (this.Zh() - var7) * var20;
         var32 = (this.AC() - var7) * var20;
         var48 = BlockFace.UP;
         var8 = BlockFace.DOWN;
      }

      if (var24 <= var32 && var26 >= var30) {
         if (var30 > var24) {
            var24 = var30;
            var47 = var48;
         }

         if (var32 < var26) {
            var26 = var32;
            var6 = var8;
         }

         double var36;
         double var38;
         BlockFace var49;
         if (var16 >= 0.0) {
            var36 = (this.sB() - var9) * var22;
            var38 = (this.h5() - var9) * var22;
            var49 = BlockFace.NORTH;
            var8 = BlockFace.SOUTH;
         } else {
            var36 = (this.h5() - var9) * var22;
            var38 = (this.sB() - var9) * var22;
            var49 = BlockFace.SOUTH;
            var8 = BlockFace.NORTH;
         }

         if (var24 <= var38 && var26 >= var36) {
            if (var36 > var24) {
               var24 = var36;
               var47 = var49;
            }

            if (var38 < var26) {
               var26 = var38;
               var6 = var8;
            }

            if (!(var26 < 0.0) && !(var24 >= var3)) {
               double var42;
               BlockFace var46;
               if (var24 < 0.0) {
                  var42 = var26;
                  var46 = var6;
               } else {
                  var42 = var24;
                  var46 = var47;
               }

               var1 = var45.multiply(var42).add(var1);
               return new s6().dx(var1).dx(var46).dx(var24);
            }

            return new s6().dx(-1.0);
         }
      }

      return new s6().dx(-1.0);
   }

   private boolean Aa(double var1, double var3, double var5, double var7, double var9, double var11) {
      return this.og() <= var1 && this.dC() >= var7 && this.AC() <= var3 && this.Zh() >= var9 && this.sB() <= var5 && this.h5() >= var11;
   }
}
