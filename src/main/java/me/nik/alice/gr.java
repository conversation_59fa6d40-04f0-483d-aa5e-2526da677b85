package me.nik.alice;

import java.util.ArrayList;
import java.util.Iterator;
import java.util.List;
import java.util.stream.Collectors;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.Check;
.1;
import org.apache.commons.lang.WordUtils;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

public class gr extends EJ {
   private static final List og;
   private static String[] sT;

   @Override
   protected void Ch() {
      this.F7();
      ArrayList var1 = new ArrayList();
      Iterator var2 = og.iterator();

      while (var2.hasNext()) {
         Check var3;
         String var4 = (var3 = (Check)var2.next()).dC().isEmpty() ? sT[2] + var3.AC() : sT[3] + var3.AC() + sT[4] + var3.dC() + sT[5];
         ArrayList var5;
         (var5 = new ArrayList()).add(sT[6]);
         var5.add(sT[7] + this.dx(var3));
         var5.add(sT[8]);
         var5.add(sT[9]);

         for (String var8 : var10 = WordUtils.wrap(var3.sB(), 35).split(System.lineSeparator())) {
            var5.add(sT[10].concat(String.valueOf(var8)));
         }

         var5.add(sT[11]);
         var5.add(sT[12]);
         var1.add(this.dx(Material.PAPER, 1, var4, var5));
      }

      if (!var1.isEmpty()) {
         for (int var9 = 0; var9 < super.jD; var9++) {
            this.index = super.jD * this.DB + var9;
            if (this.index >= var1.size()) {
               break;
            }

            if (var1.get(this.index) != null) {
               this.dx.addItem(new ItemStack[]{(ItemStack)var1.get(this.index)});
            }
         }
      }
   }

   @Override
   protected int og() {
      return 54;
   }

   // $VF: Unable to simplify switch on enum
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   @Override
   public void dx(InventoryClickEvent var1) {
      Player var2 = (Player)var1.getWhoClicked();
      ItemStack var3;
      if ((var3 = var1.getCurrentItem()).getItemMeta().hasDisplayName()) {
         switch (1.Aa[var3.getType().ordinal()]) {
            case 1:
               if (!var2.hasPermission(ZM.tm.h5())) {
                  var2.sendMessage(F.dx.getMessage() + sT[1]);
                  return;
               }

               this.dx(var3);
               this.getInventory().clear();
               this.Ch();
               return;
            case 2:
               var2.closeInventory();
               new AH(this.dx, this.plugin).iv();
               return;
            case 3:
               String var4 = ChatColor.stripColor(var3.getItemMeta().getDisplayName());
               byte var5 = -1;
               switch (var4.hashCode()) {
                  case -1133036644:
                     if (Integer.valueOf(-1133036644).equals(var4.hashCode())) {
                        var5 = 1;
                     }
                     break;
                  case 473267736:
                     if (Integer.valueOf(473267736).equals(var4.hashCode())) {
                        var5 = 0;
                     }
               }

               switch (var5) {
                  case 0:
                     if (this.DB != 0) {
                        this.DB--;
                        super.iv();
                        return;
                     }
                     break;
                  case 1:
                     this.DB++;
                     super.iv();
               }
         }
      }
   }

   public gr(QZ var1, Alice var2) {
      super(var1, var2);
   }

   static {
      nM();
      GK var0;
      (var0 = new GK(null)).DB();
      og = (List)var0.dC().stream().filter(gr::dx).collect(Collectors.toList());
   }

   private static void nM() {
      sT = new String[]{
         "&cMovement Checks",
         "You do not have permission to enable or disable checks!",
         "&6",
         "&6",
         " (",
         ")",
         "",
         "&8\u00bb &7Enabled: ",
         "",
         "&8\u00bb &7Description:",
         "&f",
         "",
         "&fClick to toggle this check"
      };
   }

   @Override
   protected String yM() {
      return Dq.sB(sT[0]);
   }

   private static boolean dx(Check var0) {
      return var0.dx() == Category.MOVE;
   }
}
