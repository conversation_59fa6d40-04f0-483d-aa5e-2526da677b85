package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class gz extends PacketCheck {
   private long Aa;
   private static String[] 7W;

   @Override
   public String sB() {
      return 7W[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().yM() && !(this.Aa.dx().gz() <= 0.125)) {
         qn var11;
         float var2 = (var11 = this.Aa.dx()).UH();
         float var3 = var11.b();
         long var5 = var11.dx().UH();
         long var7 = this.Aa;
         this.Aa = var5;
         long var9 = Math.abs(var5 - var7);
         float var4 = var11.sX();
         float var12 = var11.Vm();
         boolean var17 = var4 > 0.0F && var4 < 0.05F || var12 > 0.0F && var12 < 0.05F;
         boolean var13 = !l.yM(var4) && !l.yM(var12);
         boolean var15 = var5 < 131072L && var9 < 2000L;
         boolean var14 = var17 && var15 && var13;
         boolean var16 = var3 > 3.0F || var2 > 3.0F || var3 > 0.5F && var2 == 0.0F || var2 > 0.5F && var3 == 0.0F;
         if (var14 && var16) {
            this.Aa(7W[2] + var3 + 7W[3] + var2 + 7W[4] + var9);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.075);
         }
      }
   }

   static {
      zh();
   }

   private static void zh() {
      7W = new String[]{"C", "Checks if the player is trying to spoof cinematic rotations", "Dy: ", " Dp: ", " gcd delta: "};
   }

   public gz(UC var1) {
      super(var1, CheckType.AIM, 7W[0], Category.COMBAT, 3.0F);
   }
}
