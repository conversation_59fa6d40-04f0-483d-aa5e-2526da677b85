package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientSteerVehicle;

public class hB extends PacketCheck {
   private static String[] 4H;

   static {
      iI();
   }

   @Override
   public void dx(DH var1) {
      if (var1.Ej()) {
         WrapperPlayClientSteerVehicle var2 = new WrapperPlayClientSteerVehicle(var1.dx());
         if (this.Aa.dx().Vm() > 100 && !this.Aa.dx().Eu() && !var2.isUnmount()) {
            this.Aa(4H[2]);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Vm();
         }
      }
   }

   public hB(UC var1) {
      super(var1, CheckType.PACKET, 4H[0], Category.WORLD, 10.0F);
   }

   private static void iI() {
      4H = new String[]{"O", "Checks for invalid steer vehicle packets used by disablers", "Invalid steer vehicle packet"};
   }

   @Override
   public String sB() {
      return 4H[1];
   }
}
