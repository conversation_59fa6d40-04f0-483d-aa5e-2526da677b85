package me.nik.alice;

import java.text.SimpleDateFormat;
import java.util.Date;

public class hL {
   private final String x;
   private final String player;
   private final String zP;
   private final String information;
   private final String hW;
   private static String[] Ao;
   private final String check;

   public String gz() {
      return this.x;
   }

   public String tm() {
      return this.zP;
   }

   private static void wq() {
      Ao = new String[]{"dd.MM.yyyy HH:mm", ",", ",", ",", ",", ",", "", ","};
   }

   public String getInformation() {
      return this.information;
   }

   public String getPlayer() {
      return this.player;
   }

   static {
      wq();
   }

   public hL(String var1, String var2, String var3, String var4, String var5) {
      this.x = var1;
      this.player = var2;
      this.zP = var3;
      this.check = var4;
      this.information = var5.length() > 50 ? var5.substring(0, 50) : var5;
      this.hW = new SimpleDateFormat(Ao[0]).format(new Date());
   }

   public String toString() {
      return this.x + Ao[1] + this.player + Ao[2] + this.zP + Ao[3] + this.check + Ao[4] + this.information.replace(Ao[5], Ao[6]) + Ao[7] + this.hW;
   }

   public String getCheck() {
      return this.check;
   }

   public hL(String var1, String var2, String var3, String var4, String var5, String var6) {
      this.x = var1;
      this.player = var2;
      this.zP = var3;
      this.check = var4;
      this.information = var5;
      this.hW = var6;
   }

   public String x() {
      return this.hW;
   }
}
