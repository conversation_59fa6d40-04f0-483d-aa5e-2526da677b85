package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class iK extends PacketCheck {
   private static String[] zc;
   private long UH;

   @Override
   public void dx(DH var1) {
      if (!var1.Tq()) {
         if (var1.CS()) {
            this.UH = var1.getTimeStamp();
         }
      } else {
         long var2 = l.og(this.UH);
         long var4;
         boolean var6 = (var4 = this.Aa.dx().Fh()) < 40L || var4 > 100L;
         if (var2 > 0L && var2 < 50L && !var6) {
            this.Aa(zc[2].concat(String.valueOf(var2)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.25);
         }
      }
   }

   private static void _K/* $VF was: 9K*/() {
      zc = new String[]{"Q", "Checks for attacks while placing", "Attack packet while placing, Elapsed: "};
   }

   @Override
   public String sB() {
      return zc[1];
   }

   public iK(UC var1) {
      super(var1, CheckType.PACKET, zc[0], Category.WORLD, 3.0F);
   }

   static {
      9K();
   }
}
