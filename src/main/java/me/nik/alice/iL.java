package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

import java.util.Map;

public class iL extends PacketCheck {
   private static String[] LA;

   private static void Py() {
      LA = new String[]{"I", "Checks for invalid jumping motions", "Fly ticks: ", " delta Y: ", " expected: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && this.Aa.dx().R2() >= 20
         && this.Aa.dx().cO() >= 30
         && !this.Aa.dx().DB()
         && !this.Aa.dx().dx().DP()
         && !this.Aa.dx().tr()
         && !this.Aa.dx().jA()
         && !this.Aa.dx().Q()
         && !this.Aa.dx().cO()
         && !this.Aa.dx().tk()
         && this.Aa.dx().yk() >= 20
         && this.Aa.dx().GE() >= 20
         && this.Aa.dx().dx().og(1500L) <= 0
         && this.Aa.dx().yV() >= 20) {
         ES var5;
         float var2 = (float)(var5 = this.Aa.dx()).Ch();
         int var3 = var5.u();
         Map var4;
         if ((var4 = Rk.og()).containsKey(var3) && !(var2 < 0.0F) && !(var5.F7() < 0.1)) {
            float var6 = (Float)var4.get(var3);
            if (var2 != var6) {
               this.Aa(LA[2] + var3 + LA[3] + var2 + LA[4] + var6);
               if (this.Aa() > this.dx()) {
                  this.sX();
               }
            }
         }
      }
   }

   public iL(UC var1) {
      super(var1, CheckType.MOTION, LA[0], Category.MOVE, 1.0F);
   }

   static {
      Py();
   }

   @Override
   public String sB() {
      return LA[1];
   }
}
