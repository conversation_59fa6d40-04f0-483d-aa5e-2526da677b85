package me.nik.alice;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.bukkit.Bukkit;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerQuitEvent;

public class ia implements Listener {
   private final Alice plugin;
   private static final int Qt = Runtime.getRuntime().availableProcessors();
   private int rc;

   public ia(Alice var1) {
      this.plugin = var1;
      Bukkit.getPluginManager().registerEvents(this, var1);
   }

   @EventHandler
   public void og(PlayerQuitEvent var1) {
      this.rc = Math.max(0, this.rc - 1);
   }

   public ExecutorService Aa() {
      if (this.rc < Qt) {
         this.rc++;
         return Executors.newSingleThreadExecutor();
      } else {
         UC var1;
         UC var10000 = var1 = (UC)gE.dx(this.plugin.dx().dx().values());

         try {
            var10000.toString();
         } catch (Exception var2) {
            return Executors.newSingleThreadExecutor();
         }

         return var1.og();
      }
   }
}
