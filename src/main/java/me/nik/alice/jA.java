package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

@h5
public class jA extends PacketCheck {
   private static String[] uH;
   private final V0 sB = new V0(50);

   public jA(UC var1) {
      super(var1, CheckType.AIM, uH[0], Category.COMBAT, 1.0F);
   }

   @Override
   public String sB() {
      return uH[1];
   }

   static {
      Kt();
   }

   @Override
   public void dx(DH var1) {
      boolean var21 = cXk4;
      if (var1.R() && !this.Aa.dx().yM() && !this.Aa.dx().dx().pO()) {
         ZQ var2;
         float var3;
         qn var22;
         if ((var3 = (var2 = (var22 = this.Aa.dx()).dx()).zP()) != -1.0F) {
            float var28;
            float var10000 = var28 = var3 * 0.6F + 0.2F;
            var3 = var10000 * var10000 * var28 * 1.2F;
            if (!(var22.b() < var3 * 5.0F) && !(var22.UH() < var3 * 5.0F)) {
               float var4 = var22.h5();
               float var5 = var22.Fh();
               float var6 = var22.getYaw();
               float var23 = var22.getPitch();
               float var7 = var6 - var4 - (var6 - var4) % var3;
               var7 = var4 + var7;
               var4 = var23 - var5 - (var23 - var5) % var3;
               float var8 = var5 + var4;
               var3 = Math.abs(var7 - var6);
               float var24 = Math.abs(var8 - var23);
               float var25 = Math.abs(var3 - var24);
               this.sB.add(var25);
               if (this.sB.h5()) {
                  int var26 = l.dx(this.sB);
                  double var19 = l.Zh(this.sB);
                  int var33;
                  if (var2.sX() < 100L) {
                     var33 = this.sB.AC();
                     if (var21) {
                        throw null;
                     }
                  } else {
                     var33 = 45;
                  }

                  int var27 = var33;
                  boolean var34;
                  if (var26 >= var27 && var19 < 0.02) {
                     var34 = true;
                     if (var21) {
                        throw null;
                     }
                  } else {
                     var34 = false;
                  }

                  if (var34) {
                     this.Aa(uH[2] + var26 + uH[3] + var19);
                     if (this.Aa() > this.dx()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.25);
                  }
               }
            }
         }
      }
   }

   private static void Kt() {
      uH = new String[]{"Q", "Checks for gcd mistakes", "Distinct: ", " average: "};
   }
}
