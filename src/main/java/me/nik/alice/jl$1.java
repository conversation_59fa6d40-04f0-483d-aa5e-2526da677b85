package me.nik.alice;

import org.bukkit.Material;

class jl$1 {
   static final int[] Aa = new int[Material.values().length];

   static {
      try {
         Aa[Material.PAPER.ordinal()] = 1;
      } catch (NoSuchFieldError var1) {
      }

      int[] var10000 = Aa;

      label27: {
         int var10001;
         try {
            var10001 = Material.BARRIER.ordinal();
         } catch (NoSuchFieldError var2) {
            break label27;
         }

         var10000[var10001] = 2;
      }

      try {
         Aa[Material.BOOK.ordinal()] = 3;
      } catch (NoSuchFieldError var0) {
      }
   }
}
