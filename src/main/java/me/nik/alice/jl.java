package me.nik.alice;

import java.util.ArrayList;
import me.nik.alice.jl.1;
import org.bukkit.ChatColor;
import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

public class jl extends EJ {
   private static String[] uf;

   @Override
   protected int og() {
      return 54;
   }

   @Override
   protected String yM() {
      return Dq.sB(uf[0]);
   }

   public jl(QZ var1, Alice var2) {
      super(var1, var2);
   }

   static {
      F4();
   }

   private void AC(String var1) {
      this.plugin.dx().Zh(var1);
      D8 var2;
      (var2 = this.plugin.dx()).set(uf[11], var1);
      var2.hW();
      var2.zP();
      F.AC();
   }

   // $VF: Unable to simplify switch on enum
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   @Override
   public void dx(InventoryClickEvent var1) {
      Player var2 = (Player)var1.getWhoClicked();
      ItemStack var3;
      if ((var3 = var1.getCurrentItem()).getItemMeta().hasDisplayName()) {
         switch (1.Aa[var3.getType().ordinal()]) {
            case 1:
               if (!var2.hasPermission(ZM.x.h5())) {
                  var2.sendMessage(F.dx.getMessage() + uf[1]);
                  return;
               }

               this.AC(ChatColor.stripColor(var3.getItemMeta().getDisplayName()));
               this.getInventory().clear();
               this.Ch();
               return;
            case 2:
               var2.closeInventory();
               new pl(this.dx, this.plugin).iv();
               return;
            case 3:
               String var4 = ChatColor.stripColor(var3.getItemMeta().getDisplayName());
               byte var5 = -1;
               switch (var4.hashCode()) {
                  case -1133036644:
                     if (Integer.valueOf(-1133036644).equals(var4.hashCode())) {
                        var5 = 1;
                     }
                     break;
                  case 473267736:
                     if (Integer.valueOf(473267736).equals(var4.hashCode())) {
                        var5 = 0;
                     }
               }

               switch (var5) {
                  case 0:
                     if (this.DB != 0) {
                        this.DB--;
                        super.iv();
                        return;
                     }
                     break;
                  case 1:
                     this.DB++;
                     super.iv();
               }
         }
      }
   }

   @Override
   protected void Ch() {
      this.F7();
      ArrayList var1 = new ArrayList();

      for (nz var3 : this.plugin.dx().VL()) {
         String var4 = uf[2] + var3.iv();
         ArrayList var5;
         (var5 = new ArrayList()).add(uf[3]);
         var5.add(uf[4] + var3.Ch());
         var5.add(uf[5]);
         var5.add(uf[6] + var3.getAuthor());
         var5.add(uf[7]);
         var5.add(uf[8]);
         var5.add(uf[9]);
         var5.add(uf[10] + this.plugin.dx().dx().iv());
         var1.add(this.dx(Material.PAPER, 1, var4, var5));
      }

      if (!var1.isEmpty()) {
         for (int var7 = 0; var7 < super.jD; var7++) {
            this.index = super.jD * this.DB + var7;
            if (this.index >= var1.size()) {
               break;
            }

            Object var10000 = var1.get(this.index);

            try {
               var10000.hashCode();
            } catch (Exception var6) {
               continue;
            }

            this.dx.addItem(new ItemStack[]{(ItemStack)var1.get(this.index)});
         }
      }
   }

   private static void F4() {
      uf = new String[]{
         "&cThemes",
         "You do not have permission to change themes!",
         "&6",
         "",
         "&8\u00bb &7Prefix: &r",
         "",
         "&8\u00bb &7Author: &f",
         "",
         "&fClick to set this theme",
         "",
         "&7Current theme: &r",
         "theme"
      };
   }
}
