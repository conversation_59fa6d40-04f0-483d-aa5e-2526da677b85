package me.nik.alice;

public class jx {
   private Object Aa;
   private static String[] 1a;
   private Object og;

   public jx(Object var1, Object var2) {
      this.og = var1;
      this.Aa = var2;
   }

   public Object dx() {
      return this.og;
   }

   private static void G9() {
      1a = new String[]{"Pair{key=", ", value="};
   }

   public void Aa(Object var1) {
      this.og = var1;
   }

   public Object Aa() {
      return this.Aa;
   }

   public String toString() {
      return 1a[0] + this.og + 1a[1] + this.Aa + '}';
   }

   static {
      G9();
   }

   public void og(Object var1) {
      this.Aa = var1;
   }

   public jx(jx var1) {
      this.og = var1.og;
      this.Aa = var1.Aa;
   }
}
