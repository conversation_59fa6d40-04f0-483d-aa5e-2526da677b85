package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class k0 extends PacketCheck {
   private static String[] jg;

   private static void pO() {
      jg = new String[]{"A", "Checks for irregular swimming motions", "Delta Y: ", " accel Y diff: ", " accel Y: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().VL()) {
         ES var9;
         if ((var9 = this.Aa.dx()).M3() >= 5 && !var9.Qt() && var9.QJ() != 0) {
            if (!this.Aa.dx().Aa(RH.sX)) {
               double var3 = var9.Ch();
               double var5 = var9.Qt();
               double var7;
               boolean var10 = (var7 = Math.abs(var9.Qt() - var9.rc())) == 0.0 && Math.abs(var3) <= 0.025 || var5 > 0.1 && var3 > 0.0;
               if (!l.yM(Math.abs(var3)) && !var10) {
                  this.Aa(0.125);
               } else {
                  this.Aa(jg[2] + var3 + jg[3] + var7 + jg[4] + var5);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               }
            }
         }
      }
   }

   static {
      pO();
   }

   @Override
   public String sB() {
      return jg[1];
   }

   public k0(UC var1) {
      super(var1, CheckType.JESUS, jg[0], Category.MOVE, 10.0F);
   }
}
