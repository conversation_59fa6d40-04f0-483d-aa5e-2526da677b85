package me.nik.alice;

import com.comphenix.protocol.ProtocolLibrary;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;
import protocolsupport.api.ProtocolSupportAPI;
import us.myles.ViaVersion.api.Via;

public final class kG {
   private static final boolean DP = Bukkit.getPluginManager().isPluginEnabled(kG.G0[1]);
   private static final boolean p6 = Bukkit.getPluginManager().isPluginEnabled(kG.G0[2]);
   private static final boolean pO = Bukkit.getPluginManager().isPluginEnabled(kG.G0[0]);
   private static String[] G0;

   public static RH dx(Player var0) {
      Player var10000 = var0;

      try {
         var10000.equals(null);
      } catch (Exception var1) {
         return RH.Zm;
      }

      if (pO) {
         return RH.dx(Via.getAPI().getPlayerVersion(var0));
      } else if (DP) {
         return RH.dx(ProtocolSupportAPI.getProtocolVersion(var0).getId());
      } else {
         return p6 ? RH.dx(ProtocolLibrary.getProtocolManager().getProtocolVersion(var0)) : RH.Zm;
      }
   }

   private static void dR() {
      G0 = new String[]{"ViaVersion", "ProtocolSupport", "ProtocolLib"};
   }

   private kG() {
   }

   static {
      dR();
   }
}
