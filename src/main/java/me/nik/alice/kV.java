package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class kV extends PacketCheck {
   private static String[] Mq;

   private static void _Z/* $VF was: 7Z*/() {
      Mq = new String[]{"C", "Checks for repeated motions", "Fly ticks: ", " delta Y: ", " last: "};
   }

   public kV(UC var1) {
      super(var1, CheckType.MOTION, Mq[0], Category.MOVE, 2.0F);
   }

   @Override
   public String sB() {
      return Mq[1];
   }

   static {
      7Z();
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && this.Aa.dx().a() != 0
         && !this.Aa.dx().Fh()
         && this.Aa.dx().R2() >= 20
         && !this.Aa.dx().jD()
         && !this.Aa.dx().jA()
         && !this.Aa.dx().Q()
         && this.Aa.dx().GE() >= 20
         && this.Aa.dx().yV() >= 20
         && this.Aa.dx().cO() >= 20
         && !(this.Aa.dx().Aa().getY() < 4.0)
         && this.Aa.dx().yk() >= 20) {
         ES var4;
         float var2 = Math.abs((float)(var4 = this.Aa.dx()).Ch());
         float var3 = Math.abs((float)var4.iv());
         int var5 = var4.u();
         if (var2 > 0.0F && var2 == var3) {
            this.Aa(Mq[2] + var5 + Mq[3] + var2 + Mq[4] + var3);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.05);
         }
      }
   }
}
