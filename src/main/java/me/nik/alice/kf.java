package me.nik.alice;

public class kf {
   private int kV;
   private int J;
   private final UC Aa;

   public void R() {
      this.kV = Ku.iK();
   }

   public void P() {
      this.J = Ku.iK();
   }

   public boolean p6() {
      Sk var1 = this.Aa.dx();
      int var2 = 20 + l.dC((long)var1.jA() << 1) + (var1.Aa(3) ? 40 : 0);
      return l.Aa(this.J) <= var2;
   }

   public boolean DP() {
      ES var1;
      if (!((var1 = this.Aa.dx()).Ch() > 0.7 + (double)var1.dx().og(1500L) * 0.11) && !(var1.F7() > (double)JA.dx(this.Aa) * 2.0)) {
         Sk var2 = this.Aa.dx();
         int var3 = 20 + l.dC((long)var2.jA() << 1) + (var2.Aa(3) ? 40 : 0);
         return l.Aa(this.kV) <= var3;
      } else {
         return false;
      }
   }

   public kf(UC var1) {
      this.Aa = var1;
   }

   public void yk() {
      if (me.nik.alice.UN.dx.tV.dC() && this.Aa.dx().dC().size() != 0 && this.Aa.dx().sX() >= 20 && !this.Aa.dx().Fh()) {
         ES var1;
         if ((var1 = this.Aa.dx()).a() == 0 && var1.M3() > 5 && !this.DP() && !this.Aa.dx().tr() && !this.Aa.dx().DB() && var1.Aa().getY() > 4.0) {
            var1.dx().dx(false, false);
         }
      }
   }
}
