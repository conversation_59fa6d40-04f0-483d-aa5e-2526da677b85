package me.nik.alice;

import com.google.common.util.concurrent.AtomicDouble;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.Collection;
import java.util.HashSet;
import java.util.Iterator;
import java.util.LinkedList;
import java.util.List;
import java.util.function.Predicate;
import me.nik.fastmath.FastMath;
import org.bukkit.Location;

public final class l {
   public static final long E = 131072L;
   public static final double Wx = 0.015625;
   public static final double hB = 0.98F;
   private static final double M3 = 2.409919865102884E-181;
   public static final double uX = 0.42F;
   public static final float Zp = 0.91F;
   private static final int DC = 1023;
   private static final double xx = 180.0 / Math.PI;
   private static final double R = Math.PI / 180.0;
   private static final int iL = 53;
   private static final double tV = 4.149515568880993E180;
   private static final double GE = Double.POSITIVE_INFINITY;
   public static final double tU = 1.6777216E7;
   public static final float F7 = 90.0F;
   private static final long M3 = 9218868437227405312L;
   static final boolean QJ = !l.class.desiredAssertionStatus();

   public static double sB(double var0, double var2) {
      if (var0 < var2) {
         return sB(var2, var0);
      } else {
         return Math.abs(var2) < 0.001 ? var0 : sB(var2, var0 - FastMath.floor(var0 / var2) * var2);
      }
   }

   public static int dx(Collection var0) {
      return var0.isEmpty() ? 0 : new HashSet(var0).size();
   }

   public static double og(Location var0, Location var1) {
      double var2 = (var1.getX() - var0.getX()) * (var1.getX() - var0.getX());
      double var4 = (var1.getY() - var0.getY()) * (var1.getY() - var0.getY());
      double var6 = (var1.getZ() - var0.getZ()) * (var1.getZ() - var0.getZ());
      return Math.abs(FastMath.sqrt(var2 + var4 + var6));
   }

   public static int Aa(int var0) {
      return Ku.iK() - var0;
   }

   private static double dx(int var0) {
      if (QJ || var0 >= -1022 && var0 <= 1023) {
         return Double.longBitsToDouble((long)var0 + 1023L << 52 & 9218868437227405312L);
      } else {
         throw new AssertionError();
      }
   }

   public static long Aa(long var0) {
      return var0 / 1000000L;
   }

   public static long dx(float var0, float var1) {
      long var2 = (long)((double)var0 * 1.6777216E7);
      long var4 = (long)((double)var1 * 1.6777216E7);
      return dx(var2, var4);
   }

   public static double og(Collection var0) {
      if (var0.isEmpty()) {
         return 0.0;
      } else {
         double var1 = Double.MIN_VALUE;
         Iterator var6 = var0.iterator();

         while (var6.hasNext()) {
            double var4;
            if ((var4 = (Double)var6.next()) > var1) {
               var1 = var4;
            }
         }

         return var1;
      }
   }

   public static double AC(Collection var0) {
      boolean var6 = wBm9;
      if (var0.isEmpty()) {
         return 0.0;
      } else {
         double var1 = Double.MAX_VALUE;
         Iterator var7 = var0.iterator();

         while (var7.hasNext()) {
            double var4;
            if ((var4 = (Double)var7.next()) < var1) {
               var1 = var4;
            }

            if (var6) {
               throw null;
            }
         }

         return var1;
      }
   }

   public static double AC(double var0, double var2) {
      return Math.abs(FastMath.sqrt((var0 - var2) * (var0 - var2)));
   }

   public static double toRadians(double var0) {
      return var0 * (Math.PI / 180.0);
   }

   public static int AC(Collection var0) {
      return var0.size() - dx(var0);
   }

   public static double toDegrees(double var0) {
      return var0 * (180.0 / Math.PI);
   }

   public static double Aa(double var0, double var2) {
      return var0 / var2 * 100.0;
   }

   public static long og(long var0) {
      return System.currentTimeMillis() - var0;
   }

   public static double dC(double var0, double var2) {
      return Math.abs(Math.abs(var0) - Math.abs(var2));
   }

   public static boolean yM(Number var0) {
      return var0.doubleValue() < 0.001;
   }

   private static int dx(double var0) {
      return (int)(Double.doubleToRawLongBits(var0) >> 32);
   }

   public static double Aa(Location var0, Location var1) {
      double var2 = (var1.getX() - var0.getX()) * (var1.getX() - var0.getX());
      double var4 = (var1.getZ() - var0.getZ()) * (var1.getZ() - var0.getZ());
      return Math.abs(FastMath.sqrt(var2 + var4));
   }

   public static long dx(Collection var0) {
      if (var0.isEmpty()) {
         return 0L;
      } else {
         long var1 = Long.MIN_VALUE;
         Iterator var6 = var0.iterator();

         while (var6.hasNext()) {
            long var4;
            if ((var4 = (Long)var6.next()) > var1) {
               var1 = var4;
            }
         }

         return var1;
      }
   }

   public static double AC(Location var0, Location var1) {
      return Math.abs(FastMath.sqrt((var1.getY() - var0.getY()) * (var1.getY() - var0.getY())));
   }

   public static double Aa(double var0, int var2) {
      return BigDecimal.valueOf(var0).setScale(var2, RoundingMode.HALF_EVEN).doubleValue();
   }

   public static strictfp double hypot(double var0, double var2) {
      double var4 = Math.abs(var0);
      double var6 = Math.abs(var2);
      if (Double.isFinite(var4) && Double.isFinite(var6)) {
         if (var6 > var4) {
            double var8 = var4;
            var4 = var6;
            var6 = var8;
         }

         if (!QJ && !(var4 >= var6)) {
            throw new AssertionError();
         } else {
            int var23 = dx(var4);
            int var9 = dx(var6);
            if (var23 - var9 > 62914560) {
               return var4 + var6;
            } else {
               short var21 = 0;
               if (var4 > 3.2733937296446915E150) {
                  var23 -= 629145600;
                  var9 -= 629145600;
                  var4 *= 2.409919865102884E-181;
                  var6 *= 2.409919865102884E-181;
                  var21 += 600;
               }

               if (var6 < 3.054936363499605E-151) {
                  if (var6 < Double.MIN_NORMAL) {
                     if (var6 == 0.0) {
                        return var4;
                     }

                     var6 *= 4.49423283715579E307;
                     var4 *= 4.49423283715579E307;
                     var21 -= 1022;
                  } else {
                     var23 += 629145600;
                     var9 += 629145600;
                     var4 *= 4.149515568880993E180;
                     var6 *= 4.149515568880993E180;
                     var21 -= 600;
                  }
               }

               double var15;
               if ((var15 = var4 - var6) > var6) {
                  double var11 = dx(0.0, var23);
                  double var13 = var4 - var11;
                  var15 = Math.sqrt(var11 * var11 - (var6 * -var6 - var13 * (var4 + var11)));
               } else {
                  var4 += var4;
                  double var17 = dx(0.0, var9);
                  double var19 = var6 - var17;
                  double var24 = dx(0.0, var23 + 1048576);
                  double var25 = var4 - var24;
                  var15 = Math.sqrt(var24 * var17 - (var15 * -var15 - (var24 * var19 + var25 * var6)));
               }

               return var21 != 0 ? dx(var21) * var15 : var15;
            }
         }
      } else {
         return var4 != Double.POSITIVE_INFINITY && var6 != Double.POSITIVE_INFINITY ? var4 + var6 : Double.POSITIVE_INFINITY;
      }
   }

   public static List dx(Collection var0, Predicate var1) {
      LinkedList var2 = new LinkedList();
      Predicate var10000 = var1;

      try {
         var10000.getClass();
      } catch (Exception var4) {
         return var2;
      }

      if (!var0.isEmpty()) {
         for (Object var3 : var0) {
            if (var1.test(var3)) {
               var2.add(var3);
            }
         }

         return var2;
      } else {
         return var2;
      }
   }

   private l() {
   }

   public static double dx(Collection var0) {
      return 20.0 / Zh(var0) * 50.0;
   }

   public static double og(double var0, double var2) {
      return var0 * 100.0 / var2;
   }

   public static boolean dx(Collection var0, Predicate var1) {
      boolean var3 = wBm9;
      Predicate var10000 = var1;

      try {
         var10000.equals(null);
      } catch (Exception var4) {
         return false;
      }

      for (Object var2 : var0) {
         if (!var1.test(var2)) {
            return false;
         }

         if (var3) {
            throw null;
         }
      }

      return true;
   }

   public static double dC(Collection var0) {
      if (var0.isEmpty()) {
         return 0.0;
      } else {
         int var1 = 0;
         double var2 = 0.0;
         double var4 = 0.0;

         for (Number var9 : var0) {
            var2 += var9.doubleValue();
            var1++;
         }

         double var6 = var2 / (double)var1;

         for (Number var11 : var0) {
            var4 += FastMath.pow(var11.doubleValue() - var6, 2.0);
         }

         return var4;
      }
   }

   public static long Aa(Collection var0) {
      if (var0.isEmpty()) {
         return 0L;
      } else {
         long var1 = Long.MAX_VALUE;
         Iterator var6 = var0.iterator();

         while (var6.hasNext()) {
            long var4;
            if ((var4 = (Long)var6.next()) < var1) {
               var1 = var4;
            }
         }

         return var1;
      }
   }

   public static int og(Collection var0) {
      if (var0.isEmpty()) {
         return 0;
      } else {
         int var1 = Integer.MAX_VALUE;
         Iterator var3 = var0.iterator();

         while (var3.hasNext()) {
            int var2;
            if ((var2 = (Integer)var3.next()) < var1) {
               var1 = var2;
            }
         }

         return var1;
      }
   }

   public static int dx(Collection var0, Predicate var1) {
      boolean var4 = wBm9;
      Predicate var10000 = var1;

      try {
         var10000.toString();
      } catch (Exception var5) {
         return 0;
      }

      if (!var0.isEmpty()) {
         int var2 = 0;

         for (Number var3 : var0) {
            if (var1.test(var3)) {
               var2++;
            }

            if (var4) {
               throw null;
            }
         }

         return var2;
      } else {
         return 0;
      }
   }

   public static double dx(Location var0, Location var1) {
      double var2 = var1.getX() - var0.getX();
      float var4;
      if ((var4 = Math.abs((float)(FastMath.atan2(var1.getZ() - var0.getZ(), var2) * 180.0 / Math.PI) - 90.0F - var0.getYaw()) % 360.0F) > 180.0F) {
         var4 = 360.0F - var4;
      }

      return (double)var4;
   }

   public static float dx(Collection var0) {
      boolean var3 = wBm9;
      if (var0.isEmpty()) {
         return 0.0F;
      } else {
         float var1 = Float.MIN_VALUE;
         Iterator var4 = var0.iterator();

         while (var4.hasNext()) {
            float var2;
            if ((var2 = (Float)var4.next()) > var1) {
               var1 = var2;
            }

            if (var3) {
               throw null;
            }
         }

         return var1;
      }
   }

   public static double sB(Collection var0) {
      return var0.isEmpty() ? 0.0 : FastMath.sqrt(dC(var0) / (double)(var0.size() - 1));
   }

   public static double dx(double var0, double var2) {
      return var0 * (var2 / 100.0);
   }

   public static int dC(long var0) {
      return (int)FastMath.ceil((double)var0 / 50.0);
   }

   private static double dx(double var0, int var2) {
      return Double.longBitsToDouble(Double.doubleToRawLongBits(var0) & 4294967295L | (long)var2 << 32);
   }

   public static float Aa(Collection var0) {
      if (var0.isEmpty()) {
         return 0.0F;
      } else {
         float var1 = Float.MAX_VALUE;
         Iterator var3 = var0.iterator();

         while (var3.hasNext()) {
            float var2;
            if ((var2 = (Float)var3.next()) < var1) {
               var1 = var2;
            }
         }

         return var1;
      }
   }

   public static boolean dx(List var0, Predicate var1) {
      Predicate var10000 = var1;

      try {
         var10000.hashCode();
      } catch (Exception var3) {
         return false;
      }

      for (Object var2 : var0) {
         if (var1.test(var2)) {
            return true;
         }
      }

      return false;
   }

   private static long dx(long var0, long var2) {
      return var2 <= 16384L ? var0 : dx(var2, var0 % var2);
   }

   public static double Zh(Collection var0) {
      if (var0.isEmpty()) {
         return 0.0;
      } else {
         double var1 = 0.0;

         for (Number var4 : var0) {
            var1 += var4.doubleValue();
         }

         return var0.isEmpty() ? 0.0 : var1 / (double)var0.size();
      }
   }

   public static double dx(double var0, double var2, double var4, double var6) {
      double var8 = (var2 - var0) * (var2 - var0);
      double var10 = (var6 - var4) * (var6 - var4);
      return Math.abs(FastMath.sqrt(var8 + var10));
   }

   public static double Aa(Collection var0) {
      if (var0.isEmpty()) {
         return 0.0;
      } else {
         double var1;
         if ((var1 = (double)var0.size()) < 3.0) {
            return Double.NaN;
         } else {
            double var3 = Zh(var0);
            double var5 = sB(var0);
            AtomicDouble var7 = new AtomicDouble(0.0);

            for (Number var8 : var0) {
               var7.getAndAdd(FastMath.pow(var8.doubleValue() - var3, 4.0));
            }

            return var1 * (var1 + 1.0) / ((var1 - 1.0) * (var1 - 2.0) * (var1 - 3.0)) * (var7.get() / FastMath.pow(var5, 4.0))
               - 3.0 * FastMath.pow(var1 - 1.0, 2.0) / ((var1 - 2.0) * (var1 - 3.0));
         }
      }
   }

   public static int Aa(Collection var0) {
      if (var0.isEmpty()) {
         return 0;
      } else {
         int var1 = Integer.MIN_VALUE;
         Iterator var3 = var0.iterator();

         while (var3.hasNext()) {
            int var2;
            if ((var2 = (Integer)var3.next()) > var1) {
               var1 = var2;
            }
         }

         return var1;
      }
   }
}
