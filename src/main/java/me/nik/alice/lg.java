package me.nik.alice;

import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;
import java.util.stream.Collectors;

import me.nik.alice.checks.CheckType;
import me.nik.alice.commands.Command;
import org.bukkit.command.CommandSender;
import org.bukkit.entity.Player;

public class lg extends Command {
   private final Alice plugin;
   private static String[] D0;

   public lg(Alice var1) {
      this.plugin = var1;
   }

   @Override
   protected int dx() {
      return 2;
   }

   static {
      2U();
   }

   @Override
   protected boolean AC() {
      return false;
   }

   @Override
   protected String getDescription() {
      return D0[1];
   }

   @Override
   protected List dx(CommandSender var1, String[] var2) {
      return var2.length < 2 ? null : (List)Arrays.stream(CheckType.values()).map(CheckType::getCheckName).collect(Collectors.toList());
   }

   private static void _U/* $VF was: 2U*/() {
      D0 = new String[]{
         "debug",
         "Debug a specific type of checks",
         "/alice debug <checktype>",
         "You're no longer debugging",
         "You need to provide a check type",
         "The check type ",
         " could not be found",
         "You're now debugging "
      };
   }

   @Override
   protected void dx(CommandSender var1, String[] var2) {
      UC var3;
      UC var10000 = var3 = this.plugin.dx().dx((Player)var1);

      try {
         var10000.hashCode();
      } catch (Exception var8) {
         return;
      }

      CheckType[] var10 = var3.dx();

      try {
         var10.hashCode();
      } catch (Exception var9) {
         if (var2[1].isEmpty()) {
            var1.sendMessage(F.dx.getMessage() + D0[4]);
            return;
         }

         LinkedList var4 = new LinkedList();
         if (!Integer.valueOf(64897).equals(var2[1].toUpperCase().hashCode())) {
            for (int var5 = 1; var5 < var2.length; var5++) {
               CheckType var6;
               try {
                  var6 = CheckType.valueOf(var2[var5].toUpperCase());
               } catch (IllegalArgumentException var7) {
                  var1.sendMessage(F.dx.getMessage() + D0[5] + var2[var5] + D0[6]);
                  return;
               }

               var4.add(var6);
            }
         } else {
            var4.addAll(Arrays.asList(CheckType.values()));
         }

         var3.dx((CheckType[])var4.toArray(new CheckType[0]));
         var1.sendMessage(F.dx.getMessage() + D0[7] + var4);
         return;
      }

      var3.dx(null);
      var1.sendMessage(F.dx.getMessage() + D0[3]);
   }

   @Override
   protected String Zh() {
      return D0[2];
   }

   @Override
   protected String getName() {
      return D0[0];
   }

   @Override
   protected String h5() {
      return ZM.Zh.h5();
   }
}
