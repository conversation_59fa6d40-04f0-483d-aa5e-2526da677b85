package me.nik.alice;

public abstract class lk implements org.bukkit.inventory.InventoryHolder
{
    protected static final me.nik.alice.GK dx;
    protected final me.nik.alice.Alice plugin;
    protected me.nik.alice.QZ dx;
    protected org.bukkit.inventory.Inventory dx;
    private static java.lang.String[] Ua;
    
    public void iv() {
        this.dx = org.bukkit.Bukkit.createInventory((org.bukkit.inventory.InventoryHolder)this, this.og(), this.yM());
        this.Ch();
        this.dx.dx().openInventory(this.dx);
    }
    
    protected abstract java.lang.String yM();
    
    static {
        d5();
        (dx = new me.nik.alice.GK(null)).DB();
    }
    
    public abstract void dx(final org.bukkit.event.inventory.InventoryClickEvent p0);
    
    public lk(final me.nik.alice.QZ dx, final me.nik.alice.Alice plugin) {
        this.dx = dx;
        this.plugin = plugin;
    }
    
    protected java.lang.String dx(final me.nik.alice.ts ts) {
        final me.nik.alice.D8 aa = this.plugin.Aa();
        final java.lang.String lowerCase = ts.AC().toLowerCase();
        final java.lang.String replace = ts.dC().toLowerCase().replace(me.nik.alice.lk.Ua[18], me.nik.alice.lk.Ua[19]);
        boolean b;
        if (ts.dC().isEmpty()) {
            b = aa.getBoolean(lowerCase + me.nik.alice.lk.Ua[20]);
        }
        else {
            b = aa.getBoolean(lowerCase + me.nik.alice.lk.Ua[21] + replace + me.nik.alice.lk.Ua[22], aa.getBoolean(lowerCase + me.nik.alice.lk.Ua[23] + replace));
        }
        if (b) {
            return me.nik.alice.lk.Ua[24];
        }
        return me.nik.alice.lk.Ua[25];
    }
    
    protected abstract void Ch();
    
    protected abstract int og();
    
    public org.bukkit.inventory.Inventory getInventory() {
        return this.dx;
    }
    
    private static void d5() {
        me.nik.alice.lk.Ua = new java.lang.String[] { "", "(", ")", " (", "(", ")", " ", "_", ".enabled", ".enabled", ".", ".enabled", ".", ".enabled", ".", ".enabled", ".", ".", " ", "_", ".enabled", ".", ".enabled", ".", "&a&l\u2714", "&c&l\u2715" };
    }
    
    protected void dx(final org.bukkit.inventory.ItemStack itemStack) {
        final java.lang.String stripColor = org.bukkit.ChatColor.stripColor(itemStack.getItemMeta().getDisplayName());
        java.lang.String substring = me.nik.alice.lk.Ua[0];
        java.lang.String substring2;
        if (stripColor.contains(me.nik.alice.lk.Ua[1]) && stripColor.endsWith(me.nik.alice.lk.Ua[2])) {
            substring2 = stripColor.substring(0, stripColor.indexOf(me.nik.alice.lk.Ua[3]));
            final java.lang.String s = stripColor;
            substring = s.substring(s.indexOf(me.nik.alice.lk.Ua[4]) + 1, stripColor.lastIndexOf(me.nik.alice.lk.Ua[5]));
        }
        else {
            substring2 = stripColor;
        }
        final me.nik.alice.D8 aa = this.plugin.Aa();
        final java.lang.String lowerCase = substring2.toLowerCase();
        final java.lang.String replace = substring.toLowerCase().replace(me.nik.alice.lk.Ua[6], me.nik.alice.lk.Ua[7]);
        if (substring.isEmpty()) {
            aa.set(lowerCase + me.nik.alice.lk.Ua[8], !aa.getBoolean(lowerCase + me.nik.alice.lk.Ua[9]));
        }
        else if (aa.isSet(lowerCase + me.nik.alice.lk.Ua[10] + replace + me.nik.alice.lk.Ua[11])) {
            aa.set(lowerCase + me.nik.alice.lk.Ua[12] + replace + me.nik.alice.lk.Ua[13], !aa.getBoolean(lowerCase + me.nik.alice.lk.Ua[14] + replace + me.nik.alice.lk.Ua[15]));
        }
        else {
            aa.set(lowerCase + me.nik.alice.lk.Ua[16] + replace, !aa.getBoolean(lowerCase + me.nik.alice.lk.Ua[17] + replace));
        }
        aa.hW();
        aa.zP();
        this.plugin.sB();
    }
    
    protected org.bukkit.inventory.ItemStack dx(org.bukkit.Material material, int n, final java.lang.String s, final java.util.List list) {
        ((org.bukkit.inventory.meta.ItemMeta)(itemMeta = (int)((org.bukkit.inventory.ItemStack)(material = (org.bukkit.Material)new org.bukkit.inventory.ItemStack(material, n))).getItemMeta())).setDisplayName(me.nik.alice.Dq.sB(s));
        Label_0099: {
            java.util.ArrayList lore;
            try {
                list.toString();
                lore = new java.util.ArrayList();
                final java.util.Iterator iterator = list.iterator();
                while (iterator.hasNext()) {
                    lore.add(me.nik.alice.Dq.sB((java.lang.String)iterator.next()));
                }
            }
            catch (final java.lang.Exception ex) {
                break Label_0099;
            }
            ((org.bukkit.inventory.meta.ItemMeta)itemMeta).setLore((java.util.List)lore);
        }
        ((org.bukkit.inventory.ItemStack)material).setItemMeta((org.bukkit.inventory.meta.ItemMeta)itemMeta);
        return (org.bukkit.inventory.ItemStack)material;
    }
}
