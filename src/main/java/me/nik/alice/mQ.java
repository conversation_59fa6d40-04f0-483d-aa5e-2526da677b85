package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class mQ extends PacketCheck {
   private static String[] Vs;

   static {
      Ws();
   }

   public mQ(UC var1) {
      super(var1, CheckType.MOTION, Vs[0], Category.MOVE);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && this.Aa.dx().Wx() >= 100
         && this.Aa.dx().yk() >= 20
         && this.Aa.dx().yV() >= 20
         && !this.Aa.dx().Q()
         && this.Aa.dx().GE() >= 20) {
         ES var22;
         double var3 = (var22 = this.Aa.dx()).Ch();
         double var5 = var22.Qt();
         int var2 = var22.tk();
         int var7 = var22.a();
         int var8 = var22.dx().og(1500L);
         int var9 = var22.dx().sB(1500L);
         Qk var10 = this.Aa.dx();
         double var12 = 0.9;
         double var14 = 0.6;
         if (var8 > 0) {
            double var16 = (double)((float)var8 * 0.11F);
            var12 = 0.9 + var16;
            var14 = 0.6 + var16;
         }

         if (var10.tr()) {
            double var23 = Math.abs(var10.getVelocityY());
            double var18 = Math.abs(var10.cO());
            double var20 = var23 + var18;
            var12 += var20;
            var14 += var20;
         }

         if (var22.R2() < 60) {
            var12 += 1.15 * Math.abs(var3);
         }

         if (var9 > 0) {
            var14 += 1.15 * (double)var9;
         }

         if (var2 >= 6 && (var3 > var14 || var5 > var14 * 1.5)) {
            this.og(Vs[2] + var3 + Vs[3] + var5);
         }

         if (var7 >= 6 && (var3 > var12 || var5 > var12 || var3 > var12 && var5 <= var3 / 3.0)) {
            this.og(Vs[4] + var3 + Vs[5] + var5);
         }
      }
   }

   private static void Ws() {
      Vs = new String[]{
         "G", "Checks for impossible motions", "Impossible ground motion, Delta Y: ", " accel: ", "Impossible air motion, Delta Y: ", " accel: "
      };
   }

   @Override
   public String sB() {
      return Vs[1];
   }
}
