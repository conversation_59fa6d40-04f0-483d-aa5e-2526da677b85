package me.nik.alice.metrics;

import com.google.gson.JsonArray;
import com.google.gson.JsonObject;
import com.google.gson.JsonParser;
import java.io.BufferedReader;
import java.io.ByteArrayOutputStream;
import java.io.DataOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.Collection;
import java.util.Timer;
import java.util.TimerTask;
import java.util.UUID;
import java.util.logging.Level;
import java.util.zip.GZIPOutputStream;
import javax.net.ssl.HttpsURLConnection;
import org.bukkit.Bukkit;
import org.bukkit.configuration.file.YamlConfiguration;
import org.bukkit.entity.Player;
import org.bukkit.plugin.Plugin;
import org.bukkit.plugin.RegisteredServiceProvider;
import org.bukkit.plugin.ServicePriority;

public class MetricsLite {
   public static final int B_STATS_VERSION = 1;
   private static final String URL = "https://bStats.org/submitData/bukkit";
   private static boolean logFailedRequests;
   private static boolean logSentData;
   private static boolean logResponseStatusText;
   private static String serverUUID;
   private final boolean enabled;
   private final Plugin plugin;
   private final int pluginId;

   public MetricsLite(Plugin var1, int var2) {
      if (var1 == null) {
         throw new IllegalArgumentException("Plugin cannot be null!");
      } else {
         this.plugin = var1;
         this.pluginId = var2;
         File var7 = new File(var1.getDataFolder().getParentFile(), "bStats");
         YamlConfiguration var3;
         File var8;
         if (!(var3 = YamlConfiguration.loadConfiguration(var8 = new File(var7, "config.yml"))).isSet("serverUuid")) {
            var3.addDefault("enabled", Boolean.TRUE);
            var3.addDefault("serverUuid", UUID.randomUUID().toString());
            var3.addDefault("logFailedRequests", Boolean.FALSE);
            var3.addDefault("logSentData", Boolean.FALSE);
            var3.addDefault("logResponseStatusText", Boolean.FALSE);
            var3.options()
               .header(
                  "bStats collects some data for plugin authors like how many servers are using their plugins.\nTo honor their work, you should not disable it.\nThis has nearly no effect on the server performance!\nCheck out https://bStats.org/ to learn more :)"
               )
               .copyDefaults(true);

            try {
               var3.save(var8);
            } catch (IOException var5) {
            }
         }

         serverUUID = var3.getString("serverUuid");
         logFailedRequests = var3.getBoolean("logFailedRequests", false);
         this.enabled = var3.getBoolean("enabled", true);
         logSentData = var3.getBoolean("logSentData", false);
         logResponseStatusText = var3.getBoolean("logResponseStatusText", false);
         if (this.enabled) {
            boolean var9 = false;

            for (Class var4 : Bukkit.getServicesManager().getKnownServices()) {
               try {
                  var4.getField("B_STATS_VERSION");
                  var9 = true;
                  break;
               } catch (NoSuchFieldException var6) {
               }
            }

            Bukkit.getServicesManager().register(MetricsLite.class, this, var1, ServicePriority.Normal);
            if (!var9) {
               this.startSubmitting();
            }
         }
      }
   }

   private static void sendData(Plugin var0, JsonObject var1) {
      if (var1 == null) {
         throw new IllegalArgumentException("Data cannot be null!");
      } else if (Bukkit.isPrimaryThread()) {
         throw new IllegalAccessException("This method must not be called from the main thread!");
      } else {
         if (logSentData) {
            var0.getLogger().info("Sending data to bStats: ".concat(String.valueOf(var1)));
         }

         HttpsURLConnection var2 = (HttpsURLConnection)new URL("https://bStats.org/submitData/bukkit").openConnection();
         byte[] var8 = compress(var1.toString());
         var2.setRequestMethod("POST");
         var2.addRequestProperty("Accept", "application/json");
         var2.addRequestProperty("Connection", "close");
         var2.addRequestProperty("Content-Encoding", "gzip");
         var2.addRequestProperty("Content-Length", String.valueOf(var8.length));
         var2.setRequestProperty("Content-Type", "application/json");
         var2.setRequestProperty("User-Agent", "MC-Server/1");
         var2.setDoOutput(true);
         DataOutputStream var3 = new DataOutputStream(var2.getOutputStream());

         try {
            var3.write(var8);
         } catch (Throwable var6) {
            try {
               var3.close();
            } catch (Throwable var5) {
               var6.addSuppressed(var5);
            }

            throw var6;
         }

         var3.close();
         StringBuilder var11 = new StringBuilder();
         BufferedReader var9 = new BufferedReader(new InputStreamReader(var2.getInputStream()));

         try {
            while ((var10 = var9.readLine()) != null) {
               var11.append(var10);
            }
         } catch (Throwable var7) {
            try {
               var9.close();
            } catch (Throwable var4) {
               var7.addSuppressed(var4);
            }

            throw var7;
         }

         var9.close();
         if (logResponseStatusText) {
            var0.getLogger().info("Sent data to bStats and received response: ".concat(String.valueOf(var11)));
         }
      }
   }

   private static byte[] compress(String var0) {
      if (var0 == null) {
         return null;
      } else {
         ByteArrayOutputStream var1 = new ByteArrayOutputStream();
         GZIPOutputStream var2 = new GZIPOutputStream(var1);

         try {
            var2.write(var0.getBytes(StandardCharsets.UTF_8));
         } catch (Throwable var4) {
            try {
               var2.close();
            } catch (Throwable var3) {
               var4.addSuppressed(var3);
            }

            throw var4;
         }

         var2.close();
         return var1.toByteArray();
      }
   }

   public boolean isEnabled() {
      return this.enabled;
   }

   private void startSubmitting() {
      Timer var1;
      (var1 = new Timer(true)).scheduleAtFixedRate(new TimerTask(this, var1) {
         final Timer dx;
         final MetricsLite dx;

         {
            this.dx = var1;
            this.dx = var2;
         }

         public void run() {
            if (!MetricsLite.access$000(this.dx).isEnabled()) {
               this.dx.cancel();
            } else {
               Bukkit.getScheduler().runTask(MetricsLite.access$000(this.dx), this::tk);
            }
         }

         private void tk() {
            MetricsLite.access$100(this.dx);
         }
      }, 300000L, 1800000L);
   }

   public JsonObject getPluginData() {
      JsonObject var1 = new JsonObject();
      String var2 = this.plugin.getDescription().getName();
      String var3 = this.plugin.getDescription().getVersion();
      var1.addProperty("pluginName", var2);
      var1.addProperty("id", this.pluginId);
      var1.addProperty("pluginVersion", var3);
      var1.add("customCharts", new JsonArray());
      return var1;
   }

   private JsonObject getServerData() {
      int var1;
      try {
         Method var2;
         var1 = (var2 = Class.forName("org.bukkit.Server").getMethod("getOnlinePlayers")).getReturnType().equals(Collection.class)
            ? ((Collection)var2.invoke(Bukkit.getServer())).size()
            : ((Player[])var2.invoke(Bukkit.getServer())).length;
      } catch (Exception var11) {
         var1 = Bukkit.getOnlinePlayers().size();
      }

      int var12 = Bukkit.getOnlineMode() ? 1 : 0;
      String var3 = Bukkit.getVersion();
      String var4 = Bukkit.getName();
      String var5 = System.getProperty("java.version");
      String var6 = System.getProperty("os.name");
      String var7 = System.getProperty("os.arch");
      String var8 = System.getProperty("os.version");
      int var9 = Runtime.getRuntime().availableProcessors();
      JsonObject var10;
      (var10 = new JsonObject()).addProperty("serverUUID", serverUUID);
      var10.addProperty("playerAmount", var1);
      var10.addProperty("onlineMode", var12);
      var10.addProperty("bukkitVersion", var3);
      var10.addProperty("bukkitName", var4);
      var10.addProperty("javaVersion", var5);
      var10.addProperty("osName", var6);
      var10.addProperty("osArch", var7);
      var10.addProperty("osVersion", var8);
      var10.addProperty("coreCount", var9);
      return var10;
   }

   private void submitData() {
      JsonObject var1 = this.getServerData();
      JsonArray var2 = new JsonArray();

      for (Class var4 : Bukkit.getServicesManager().getKnownServices()) {
         try {
            var4.getField("B_STATS_VERSION");

            for (RegisteredServiceProvider var5 : Bukkit.getServicesManager().getRegistrations(var4)) {
               try {
                  if ((var5 = (RegisteredServiceProvider)var5.getService().getMethod("getPluginData").invoke(var5.getProvider())) instanceof JsonObject) {
                     var2.add((JsonObject)var5);
                  } else {
                     try {
                        Class var6 = Class.forName("org.json.simple.JSONObject");
                        if (var5.getClass().isAssignableFrom(var6)) {
                           Method var14;
                           (var14 = var6.getDeclaredMethod("toJSONString")).setAccessible(true);
                           String var12 = (String)var14.invoke(var5);
                           JsonObject var13 = new JsonParser().parse(var12).getAsJsonObject();
                           var2.add(var13);
                        }
                     } catch (ClassNotFoundException var7) {
                        if (logFailedRequests) {
                           this.plugin.getLogger().log(Level.SEVERE, "Encountered unexpected exception ", var7);
                        }
                     }
                  }
               } catch (NoSuchMethodException | IllegalAccessException | InvocationTargetException | NullPointerException var8) {
               }
            }
         } catch (NoSuchFieldException var9) {
         }
      }

      var1.add("plugins", var2);
      new Thread(this::lambda$submitData$0).start();
   }

   private void lambda$submitData$0(JsonObject var1) {
      try {
         sendData(this.plugin, var1);
      } catch (Exception var2) {
         if (logFailedRequests) {
            this.plugin.getLogger().log(Level.WARNING, "Could not submit plugin stats of " + this.plugin.getName(), var2);
         }
      }
   }

   static Plugin access$000(MetricsLite var0) {
      return var0.plugin;
   }

   static void access$100(MetricsLite var0) {
      var0.submitData();
   }

   static {
      if (System.getProperty("bstats.relocatecheck") == null || !System.getProperty("bstats.relocatecheck").equals("false")) {
         String var0 = new String(new byte[]{111, 114, 103, 46, 98, 115, 116, 97, 116, 115, 46, 98, 117, 107, 107, 105, 116});
         String var1 = new String(new byte[]{121, 111, 117, 114, 46, 112, 97, 99, 107, 97, 103, 101});
         if (MetricsLite.class.getPackage().getName().equals(var0) || MetricsLite.class.getPackage().getName().equals(var1)) {
            throw new IllegalStateException("bStats Metrics class has not been relocated correctly!");
         }
      }
   }
}
