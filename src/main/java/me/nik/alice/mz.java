package me.nik.alice;

public class mz {
   private final long Zm;
   private final double Zp;
   private final double F7;
   private final long rc;
   private final long a;
   private final long bI;
   private static String[] nX;
   private final long jA;
   private final long Qt;
   private final long yV;

   public mz(long var1, double var3, double var5, long var7, long var9, long var11, long var13, long var15, long var17) {
      this.Qt = var1;
      this.F7 = var3;
      this.Zp = var5;
      this.rc = var7;
      this.Zm = var9;
      this.jA = var11;
      this.bI = var13;
      this.yV = var15;
      this.a = var17;
   }

   public String toString() {
      return F.Qt
         .getMessage()
         .replace(nX[0], F.dx.getMessage())
         .replace(nX[1], String.valueOf(this.Qt))
         .replace(nX[2], this.F7 + nX[3])
         .replace(nX[4], String.valueOf(this.Zp))
         .replace(nX[5], String.valueOf(this.rc))
         .replace(nX[6], String.valueOf(this.Zm))
         .replace(nX[7], String.valueOf(this.jA))
         .replace(nX[8], String.valueOf(this.bI))
         .replace(nX[9], String.valueOf(this.yV))
         .replace(nX[10], String.valueOf(this.a));
   }

   static {
      sW();
   }

   private static void sW() {
      nX = new String[]{
         "%prefix%",
         "%samples%",
         "%averagecpu%",
         "%",
         "%averagetps%",
         "%averagetick%",
         "%averagepacketdata%",
         "%averagepacketchecks%",
         "%averagebukkitdata%",
         "%averagebukkitchecks%",
         "%total%"
      };
   }
}
