package me.nik.alice;

import com.comphenix.protocol.wrappers.EnumWrappers.PlayerDigType;
import java.util.Iterator;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientBlockDig;
import org.bukkit.GameMode;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.entity.Player;

public class nA extends PacketCheck {
   private static String[] vv;

   static {
      8c();
   }

   public nA(UC var1) {
      super(var1, CheckType.INTERACT, vv[0], Category.WORLD);
   }

   @Override
   public String sB() {
      return vv[1];
   }

   private static void _c/* $VF was: 8c*/() {
      vv = new String[]{"GhostHand", "Checks for invalid block interactions", "GLASS", "BED", "Broke through block, type: ", " target: "};
   }

   @Override
   public void dx(DH var1) {
      boolean var10 = KtHS;
      if (var1.uD()) {
         Sk var2;
         if ((var2 = this.Aa.dx()).jA() <= 850 && !var2.Aa(1)) {
            WrapperPlayClientBlockDig var12;
            if ((var12 = new WrapperPlayClientBlockDig(var1.dx())).getStatus() == PlayerDigType.STOP_DESTROY_BLOCK) {
               Player var15;
               if ((var15 = this.Aa.getPlayer()).getGameMode() == GameMode.SURVIVAL) {
                  Block var13;
                  Block var10000 = var13 = fD.dx(var12.getLocation().toLocation(var15.getWorld()));

                  try {
                     var10000.toString();
                  } catch (Exception var11) {
                     return;
                  }

                  Material var3;
                  String var4 = (var3 = var13.getType()).toString();
                  if (me.nik.alice.Ww.dx.EK.Aa().contains(var4)) {
                     Block var16;
                     Material var5 = (var16 = var15.getTargetBlock(null, 6)).getType();
                     if (var16 != var13 && var5.isSolid() && (var5.isOccluding() || var5.toString().contains(vv[2]))) {
                        boolean var6 = false;
                        int var7 = 0;
                        boolean var8 = var4.contains(vv[3]);
                        if (var5 != var3) {
                           Iterator var14 = fD.dx(var13.getLocation()).iterator();

                           while (var14.hasNext()) {
                              Block var9;
                              if ((var9 = (Block)var14.next()).getType() == var3) {
                                 var7++;
                              }

                              if (var7 > 0 && !var8) {
                                 break;
                              }

                              if (var16.equals(var9)) {
                                 var6 = true;
                                 if (var10) {
                                    throw null;
                                 }
                                 break;
                              }

                              if (var10) {
                                 throw null;
                              }
                           }
                        }

                        if (var6) {
                           this.og(vv[4] + var4 + vv[5] + var5);
                        }
                     }
                  }
               }
            }
         }
      }
   }
}
