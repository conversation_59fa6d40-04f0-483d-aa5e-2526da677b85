package me.nik.alice;

import java.io.File;
import org.bukkit.configuration.file.FileConfiguration;

public class nz {
   private FileConfiguration dx;
   private final String name;
   private static String[] YQ;
   private static final String F7 = "+----------------------------------------------------------------------------------------------+\n|                                                                                              |\n|                                              Alice                                           |\n|                                                                                              |\n|                               Discord: https://discord.gg/m7j2Y9H                            |\n|                                                                                              |\n|                                           Author: Nik                                        |\n|                                                                                              |\n+----------------------------------------------------------------------------------------------+\n";
   private final File file;

   public nz(File var1) {
      this.file = var1;
      this.name = var1.getName().replace(YQ[0], YQ[1]);
      this.AC();
   }

   public String Ch() {
      return this.dx.getString(YQ[4]);
   }

   public File getFile() {
      return this.file;
   }

   public void AC() {
      try {
         this.dx = gE.dx(this.file);
         this.dx.options().header(YQ[2]);
         this.dx.save(this.file);
      } catch (Exception var1) {
         var1.printStackTrace();
      }
   }

   private static void OD() {
      YQ = new String[]{
         ".yml",
         "",
         "+----------------------------------------------------------------------------------------------+\n|                                                                                              |\n|                                              Alice                                           |\n|                                                                                              |\n|                               Discord: https://discord.gg/m7j2Y9H                            |\n|                                                                                              |\n|                                           Author: Nik                                        |\n|                                                                                              |\n+----------------------------------------------------------------------------------------------+\n",
         "theme_author",
         "prefix"
      };
   }

   public FileConfiguration dx() {
      return this.dx;
   }

   static {
      OD();
   }

   public String iv() {
      return this.name;
   }

   public String getAuthor() {
      return this.dx.getString(YQ[3]);
   }
}
