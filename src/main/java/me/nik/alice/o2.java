package me.nik.alice;

import java.util.ArrayList;
import java.util.List;
import org.bukkit.Location;

public class o2 {
   private long Zp;
   private int tk;
   private static String[] yh;
   private final List Zh = new ArrayList();

   static {
      44();
   }

   public void Aa(Location var1) {
      this.rc();
      if (!this.Zh.contains(var1)) {
         this.Zh.add(var1);
         this.tk++;
      }
   }

   private static void _4/* $VF was: 44*/() {
      yh = new String[]{"reset"};
   }

   public void og(Location var1) {
      this.Zh.add(var1);
   }

   public void reset() {
      this.Zh.clear();
      this.tk = 0;
      this.Zp = System.currentTimeMillis();
      System.out.println(yh[0]);
   }

   public void rc() {
      long var1 = System.currentTimeMillis();
      long var3 = this.Zp / 1000L + me.nik.alice.Ww.dx.nA.Aa() - var1 / 1000L;
      System.out.println(var3);
      if (var3 <= 0L || this.Zp == 0L) {
         this.reset();
      }
   }

   public int sB() {
      return this.tk;
   }
}
