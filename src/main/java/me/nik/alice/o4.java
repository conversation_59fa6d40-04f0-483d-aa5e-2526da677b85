package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class o4 extends PacketCheck {
   private static String[] cc;
   private long Fh;
   private String sB;

   public o4(UC var1) {
      super(var1, CheckType.PACKET, cc[1], Category.WORLD, 5.0F);
   }

   private static void B7() {
      cc = new String[]{"Checks for post packets", "A", "Delta: ", " packet: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.M3()) {
         long var8 = var1.getTimeStamp();
         long var4 = this.Fh;
         this.Fh = var8;
         if (this.sB != null) {
            long var6;
            if ((var6 = var8 - var4) > 40L && var6 < 100L) {
               this.Aa(cc[2] + var6 + cc[3] + this.sB);
               if (this.Aa() > this.dx()) {
                  this.sX();
               }
            } else {
               this.Aa(0.75);
            }
         }

         this.sB = null;
      } else {
         long var2;
         if ((var1.Tq() || var1.J4() || var1.CS() || var1.d()) && (var2 = var1.getTimeStamp() - this.Fh) > 0L && var2 < 10L) {
            this.sB = var1.dx().name();
         }
      }
   }

   @Override
   public String sB() {
      return cc[0];
   }

   static {
      B7();
   }
}
