package me.nik.alice;

import java.util.Queue;
import java.util.concurrent.ConcurrentLinkedQueue;

public class oG {
   private final TQ dx;
   private final Queue Aa = new ConcurrentLinkedQueue();
   private boolean x;

   public Queue Aa() {
      return this.Aa;
   }

   public void gz() {
      this.Aa.clear();
      this.dx.gz();
   }

   public void og(boolean var1) {
      this.x = var1;
   }

   public oG(Alice var1) {
      String var2 = me.nik.alice.UN.dx.u.b().toLowerCase();
      byte var3 = -1;
      switch (var2.hashCode()) {
         case -894935028:
            if (Integer.valueOf(-894935028).equals(var2.hashCode())) {
               var3 = 1;
            }
            break;
         case 104382626:
            if (Integer.valueOf(104382626).equals(var2.hashCode())) {
               var3 = 0;
            }
      }

      switch (var3) {
         case 0:
            this.dx = new Iy(var1);
            break;
         case 1:
            this.dx = new Tw(var1);
            break;
         default:
            this.dx = new Uk(var1);
      }

      this.dx.dC();
   }

   public TQ dx() {
      return this.dx;
   }

   public void bI() {
      this.Aa.clear();
   }

   public boolean Vm() {
      return this.x;
   }

   public void Aa(hL var1) {
      this.Aa.add(var1);
   }
}
