package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.potion.PotionEffectType;

public class oN extends PacketCheck {
   private static String[] hK;

   static {
      mp();
   }

   public oN(UC var1) {
      super(var1, CheckType.SPEED, hK[1], Category.MOVE, 2.0F);
   }

   @Override
   public String sB() {
      return hK[0];
   }

   private static void mp() {
      hK = new String[]{
         "Checks for additional movement speed",
         "Expect",
         "Impossible acceleration, XZ: ",
         "Impossible movement, XZ: ",
         "Delta XZ: ",
         " expected: ",
         " ground ticks: ",
         "Delta XZ: ",
         " expected: ",
         " fly ticks: "
      };
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh()) {
         double var3;
         ES var23;
         if ((var3 = (var23 = this.Aa.dx()).F7()) != 0.0) {
            int var2 = var23.u();
            int var5 = var23.tk();
            boolean var6 = var23.cO();
            boolean var7 = var23.bI() < 20;
            boolean var8 = var23.R2() < 20;
            boolean var9 = var23.yk() < 40;
            Qk var10 = this.Aa.dx();
            Player var11 = this.Aa.getPlayer();
            double var13 = (double)JA.Aa(this.Aa);
            double var15 = (double)JA.dx(this.Aa);
            if (var3 % 0.1 == 0.0) {
               this.og(hK[2].concat(String.valueOf(var3)));
            }

            if (var23.Ch() >= 0.0 && var3 < 1.0E-6 && var23.DB() == 0.0) {
               this.og(hK[3].concat(String.valueOf(var3)));
            }

            if (var5 > 0 && var5 <= 11) {
               var13 += (double)(var5 == 2 ? 0.2F : 0.25F / (float)var5);
            }

            float var12 = (float)(0.42F + (double)((float)JA.dx(var11, PotionEffectType.JUMP) * 0.1F));
            if (Math.abs(var23.Ch() - (double)var12) < 1.0E-4 && var2 == 1) {
               float var18 = this.Aa.dx().getYaw() * (float) (Math.PI / 180.0);
               double var19 = var23.x() - FastMath.sin((double)var18) * 0.2F;
               double var21 = var23.hW() + FastMath.cos((double)var18) * 0.2F;
               var15 += FastMath.hypot(var19, var21);
            }

            if (var23.cO() <= 5) {
               var13 += 0.3;
               var15 += 0.3;
            }

            if (var23.b().size() > 0) {
               var13 += 0.09;
               var15 += 0.09;
            }

            if (var23.jA()) {
               var15 += 0.25;
               var13 += 0.5;
            }

            if (var23.Ch() < 0.0 && var2 > 0 && var2 < 3) {
               var15 += 0.03;
            }

            if (var10.tr()) {
               double var24 = Math.abs(var10.cO() + var10.getVelocityY() / 4.0);
               var13 += var24;
               var15 += var24;
            }

            if (this.Aa.dx().DB() || var23.dx().DP()) {
               var13 += 0.255;
               var15 += 0.255;
            }

            if (var23.yV() < 20) {
               var13 += 0.9;
               var15 += 0.9;
            }

            if (var8) {
               var15 += 0.1;
            }

            if (var23.Q()) {
               var15 += 0.075;
               var13 += 0.075;
            }

            if (var9) {
               var13 += 0.05;
               var15 += 0.35;
            }

            if (var23.p6() < 40) {
               var13 += var9 ? 0.38 : 0.2;
               var15 += var9 ? 0.39 : 0.19;
            }

            int var25;
            if ((var7 || var23.a()) && w.N7() && (var25 = JA.dx(var11.getInventory().getBoots(), Enchantment.SOUL_SPEED)) > 0) {
               double var27 = 0.15 * (double)var25;
               var13 += var27;
               var15 += var27;
            }

            if ((var25 = JA.dx(var11, PotionEffectType.SLOW)) > 0 && var25 < 10) {
               double var28 = l.dx(var13, 7.5) * (double)var25;
               var13 -= var28;
            }

            if (var6) {
               if (!(var3 > var13)) {
                  this.Aa(0.025);
                  return;
               }

               this.Aa(hK[4] + var3 + hK[5] + var13 + hK[6] + var5);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else if (var3 > var15) {
               this.Aa(hK[7] + var3 + hK[8] + var15 + hK[9] + var2);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.025);
            }
         }
      }
   }
}
