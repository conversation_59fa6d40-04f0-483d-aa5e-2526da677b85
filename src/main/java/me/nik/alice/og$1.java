package me.nik.alice;

class og$1 extends org.bukkit.scheduler.BukkitRunnable
{
    final me.nik.alice.og dx;
    final org.bukkit.entity.Player dx;
    final org.bukkit.Location dx;
    double Aa;
    final java.lang.String dx;
    final org.bukkit.World dx;
    final me.nik.alice.UC dx;
    
    og$1(final me.nik.alice.og dx, final org.bukkit.entity.Player dx2, final java.lang.String dx3, final me.nik.alice.UC dx4, final org.bukkit.World dx5, final org.bukkit.Location dx6) {
        this.dx = dx;
        this.dx = dx2;
        this.dx = dx3;
        this.dx = dx4;
        this.dx = dx5;
        this.dx = dx6;
        this.Aa = 16.0;
    }
    
    public void run() {
        if (!this.dx.isOnline()) {
            me.nik.alice.gE.h5(this.dx);
            this.cancel();
            return;
        }
        this.dx.dx().dx().dx(false, true);
        this.dx.playEffect(this.dx.clone().add(this.Aa, 0.0, 0.0), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, 0.0, 0.0), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(0.0, 0.0, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(0.0, 0.0, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, 0.0, -this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, 0.0, -this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(-this.Aa, 0.0, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(-this.Aa, 0.0, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, 0.0, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, 0.0, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, 0.0, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, 0.0, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, this.Aa, 0.0), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, this.Aa, 0.0), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(0.0, this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(0.0, this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, this.Aa, -this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, this.Aa, -this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(-this.Aa, this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(-this.Aa, this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, -this.Aa, 0.0), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, -this.Aa, 0.0), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(0.0, -this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(0.0, -this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, -this.Aa, -this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, -this.Aa, -this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(-this.Aa, -this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(-this.Aa, -this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, -this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, -this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().add(this.Aa, -this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        this.dx.playEffect(this.dx.clone().subtract(this.Aa, -this.Aa, this.Aa), org.bukkit.Effect.MOBSPAWNER_FLAMES, (java.lang.Object)null);
        --this.Aa;
        if (this.Aa <= 0.0) {
            me.nik.alice.gE.h5(this.dx);
            this.cancel();
        }
    }
}
