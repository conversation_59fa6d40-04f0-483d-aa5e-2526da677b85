package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class p6 extends PacketCheck {
   private static String[] ja;

   public p6(UC var1) {
      super(var1, CheckType.PACKET, ja[0], Category.WORLD);
   }

   private static void lC() {
      ja = new String[]{"C", "Checks for invalid pitch", "Pitch: "};
   }

   static {
      lC();
   }

   @Override
   public String sB() {
      return ja[1];
   }

   @Override
   public void dx(DH var1) {
      boolean var3 = tXtb;
      if (var1.R()) {
         float var4 = Math.abs(this.Aa.dx().getPitch());
         float var10000;
         if (this.Aa.dx().DP() > 0) {
            var10000 = 91.11F;
            if (var3) {
               throw null;
            }
         } else {
            var10000 = 90.0F;
         }

         float var2 = var10000;
         if (var4 > var2) {
            this.og(ja[2].concat(String.valueOf(var4)));
         }
      }
   }
}
