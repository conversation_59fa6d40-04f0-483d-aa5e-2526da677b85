package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class pO extends PacketCheck {
   private int VL;
   private int b;
   private static String[] DV;

   @Override
   public String sB() {
      return DV[1];
   }

   public pO(UC var1) {
      super(var1, CheckType.PACKET, DV[0], Category.WORLD, 3.0F);
   }

   private static void Aa() {
      DV = new String[]{"B", "Checks for attacks without arm swings", "NoSwing, Hits: ", " Swings: "};
   }

   @Override
   public void dx(DH var1) {
      if (var1.Tq()) {
         if (this.VL++ >= 3) {
            if (this.VL > this.b) {
               this.Aa(DV[2] + this.VL + DV[3] + this.b);
               if (this.Aa() > this.dx()) {
                  this.sX();
               }
            } else {
               this.Aa(0.5);
            }

            this.VL = this.b = 0;
            return;
         }
      } else if (var1.i2()) {
         this.b++;
      }
   }

   static {
      Aa();
   }
}
