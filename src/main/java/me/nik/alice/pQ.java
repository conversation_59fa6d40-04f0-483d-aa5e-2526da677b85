package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

@h5
public class pQ extends PacketCheck {
   private static String[] 7g;

   public pQ(UC var1) {
      super(var1, CheckType.PACKET, 7g[0], Category.WORLD);
   }

   private static void aU() {
      7g = new String[]{"DD", "Checks for void tp"};
   }

   @Override
   public void dx(DH var1) {
   }

   static {
      aU();
   }

   @Override
   public String sB() {
      return 7g[1];
   }
}
