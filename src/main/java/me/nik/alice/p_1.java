package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;
import org.bukkit.GameMode;
import org.bukkit.Location;
import org.bukkit.entity.Entity;
import org.bukkit.entity.LivingEntity;
import org.bukkit.entity.Player;

public class p extends PacketCheck {
   private static String[] Zx;

   private static void uF() {
      Zx = new String[]{"B", "Checks for blatant combat reach", "Distance: "};
   }

   @Override
   public String sB() {
      return Zx[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.Tq()) {
         Entity var16;
         Entity var10000 = var16 = this.Aa.dx().Aa();

         try {
            var10000.getClass();
         } catch (Exception var15) {
            return;
         }

         Player var2 = this.Aa.getPlayer();
         if (var16 instanceof LivingEntity
            && var2.getGameMode() != GameMode.CREATIVE
            && !this.Aa.dx().o4()
            && this.Aa.dx().Vm() >= 20
            && var16.getWorld() == var2.getWorld()) {
            Sk var17 = this.Aa.dx();
            Location var3 = this.Aa.dx().Aa();
            Location var4 = var16.getLocation();
            double var7 = var3.getX();
            double var9 = var3.getZ();
            double var11 = var4.getX();
            double var13 = var4.getZ();
            float var18;
            if ((var18 = cW.dx(var16)) != -1.0F) {
               float var19 = (float)FastMath.hypot(var7 - var11, var9 - var13) - var18 - (var16 instanceof Player ? 0.4F : 0.6F);
               UC var22;
               if (var16 instanceof Player && (var22 = Alice.dx().dx().dx((Player)var16)) != null && var22.dx().h5() < 2500L) {
                  var19--;
               }

               float var20 = (float)((double)var19 - Math.abs(var16.getVelocity().lengthSquared()) * 2.5);
               float var23;
               if ((var23 = this.Aa.dx().getPitch()) > -6.5F && var23 < -5.0F) {
                  var20 -= 0.15F;
               }

               if (this.Aa.dx().F7() > 0.15 && this.Aa.dx().Ch()) {
                  var20 -= 0.6F;
               }

               float var21 = var20 - (float)FastMath.max(25, var17.jA()) * 0.0064F;
               if (var17.Fh() < 25L) {
                  var21--;
               }

               if (var21 >= me.nik.alice.Ww.dx.YL.sB()) {
                  this.Aa(Zx[2].concat(String.valueOf(var21)));
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.75);
               }
            }
         }
      }
   }

   public p(UC var1) {
      super(var1, CheckType.REACH, Zx[0], Category.COMBAT, 10.0F);
   }

   static {
      uF();
   }
}
