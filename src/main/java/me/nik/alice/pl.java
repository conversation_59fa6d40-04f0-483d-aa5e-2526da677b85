package me.nik.alice;

import org.bukkit.Material;
import org.bukkit.entity.Player;
import org.bukkit.event.inventory.InventoryClickEvent;
import org.bukkit.inventory.ItemStack;

public class pl extends lk {
   private static String[] UM;

   public pl(QZ var1, Alice var2) {
      super(var1, var2);
   }

   @Override
   public void dx(InventoryClickEvent var1) {
      if (dC.og()) {
         Player var2 = (Player)var1.getWhoClicked();
         switch (var1.getSlot()) {
            case 11:
               var2.closeInventory();
               new AH(this.dx, this.plugin).iv();
               return;
            case 13:
               var2.closeInventory();
               new jl(this.dx, this.plugin).iv();
               return;
            case 15:
               var2.closeInventory();
               if (this.plugin.dx().Vm()) {
                  var2.sendMessage(F.dx.getMessage() + UM[1]);
                  return;
               }

               new Mx(this.dx, this.plugin).iv();
               break;
            case 31:
               var2.closeInventory();
               return;
         }
      }
   }

   private static void Yx() {
      UM = new String[]{"&cAlice Menu", "Please wait until the Log Queue task finishes", "&cExit", "&6Checks", "&6Themes", "&6Player Logs"};
   }

   @Override
   protected void Ch() {
      ItemStack var1 = this.dx(Material.BARRIER, 1, UM[2], null);
      this.dx.setItem(31, var1);
      var1 = this.dx(Material.BOOK, 1, UM[3], null);
      this.dx.setItem(11, var1);
      var1 = this.dx(Material.BOOK, 1, UM[4], null);
      this.dx.setItem(13, var1);
      var1 = this.dx(Material.BOOK, 1, UM[5], null);
      this.dx.setItem(15, var1);
   }

   @Override
   protected int og() {
      return 36;
   }

   @Override
   protected String yM() {
      return Dq.sB(UM[0]);
   }

   static {
      Yx();
   }
}
