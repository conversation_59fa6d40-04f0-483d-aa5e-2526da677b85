package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class pn extends PacketCheck {
   private double AC;
   private final V0 sB = new V0(50);
   private static String[] XA;

   @Override
   public String sB() {
      return XA[1];
   }

   @Override
   public void dx(DH var1) {
      if (!var1.M3()) {
         if (var1.qa()) {
            this.sB.add(System.currentTimeMillis() << 1);
         }
      } else {
         this.sB.add(var1.getTimeStamp());
         if (this.sB.h5()) {
            double var2;
            if (!((var2 = l.sB(this.sB)) < 50.0)) {
               double var4 = this.AC;
               this.AC = var2;
               double var6 = l.dC(var2, var4);
               boolean var8 = this.Aa.dx().Zp() < 80
                  || Ku.Vm() < 5000L
                  || this.Aa.AC() < 12500L
                  || this.Aa.getPlayer().isInsideVehicle()
                  || this.Aa.dx().WB() < 80;
               if (var2 <= 722.0 && var6 <= 5.0 && !var8) {
                  this.Aa(XA[2] + var2 + XA[3] + var6);
                  if (this.Aa() > this.dx()) {
                     this.sX();
                     return;
                  }
               } else {
                  this.Aa(0.25);
               }
            }
         }
      }
   }

   static {
      9s();
   }

   private static void _s/* $VF was: 9s*/() {
      XA = new String[]{"A", "Checks for fast game speed", "D: ", " Absolute delta: "};
   }

   public pn(UC var1) {
      super(var1, CheckType.TIMER, XA[0], Category.WORLD, 2.0F);
   }
}
