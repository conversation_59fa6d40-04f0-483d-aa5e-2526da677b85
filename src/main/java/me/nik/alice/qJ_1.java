package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import org.bukkit.Material;
import org.bukkit.block.Block;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.block.BlockPlaceEvent;

public class qJ extends EventCheck {
   private static String[] qa;

   @Override
   public String sB() {
      return qa[3];
   }

   @Override
   public void on(Event var1) {
      boolean var5 = nrgj;
      if (var1 instanceof BlockPlaceEvent && !Alice.dx().dx().Aa(this.Aa.getPlayer())) {
         Block var2;
         BlockPlaceEvent var6;
         if ((var2 = (var6 = (BlockPlaceEvent)var1).getBlockPlaced()).getType().isSolid()) {
            Block var3;
            Material var4;
            if ((var4 = (var3 = var6.getBlockAgainst()).getType()).isOccluding() && var4.isSolid()) {
               Player var7 = var6.getPlayer();
               BlockFace var8;
               boolean var10000;
               if (!gE.dx(gE.dx(var8 = var2.getFace(var3).getOppositeFace(), var2.getLocation()), var7.getEyeLocation(), var8)) {
                  var10000 = true;
                  if (var5) {
                     throw null;
                  }
               } else {
                  var10000 = false;
               }

               if (var10000) {
                  this.og(qa[1] + var8.name() + qa[2] + var4);
               }
            }
         }
      }
   }

   static {
      06();
   }

   private static void _6/* $VF was: 06*/() {
      qa = new String[]{"Invalid", "Face: ", " against: ", "Checks for invalid block placements"};
   }

   public qJ(UC var1) {
      super(var1, CheckType.INTERACT, qa[0], Category.WORLD);
   }
}
