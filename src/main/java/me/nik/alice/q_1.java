package me.nik.alice;

import java.io.BufferedReader;
import java.io.ByteArrayInputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.Reader;
import java.io.StringReader;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import org.bukkit.plugin.java.JavaPlugin;

public class q {
   private static String[] xM;
   private final JavaPlugin dx;

   private static void XU() {
      xM = new String[]{
         "#",
         "'",
         "''",
         "#",
         "_COMMENT_",
         ": '",
         "'",
         "\n",
         "\n",
         "#",
         "\n",
         "_COMMENT",
         "#",
         ":",
         "#'",
         "#'",
         "# ",
         "''",
         "'",
         "\n",
         "\n",
         "\n",
         "\n",
         "\n\n",
         "\n",
         "#",
         "#",
         "",
         "-"
      };
   }

   public D8 dx(File var1) {
      if (!this.dx.getDataFolder().exists()) {
         this.dx.getDataFolder().mkdir();
      }

      if (!var1.exists()) {
         try {
            var1.createNewFile();
         } catch (IOException var2) {
            var2.printStackTrace();
         }
      }

      return new D8(this.dx(var1), var1, this.dx(var1), this.dx);
   }

   public q(JavaPlugin var1) {
      this.dx = var1;
   }

   private String og(String var1) {
      boolean var2 = false;
      String[] var8 = var1.split(xM[10]);
      StringBuilder var3 = new StringBuilder();
      int var4 = var8.length;

      for (int var5 = 0; var5 < var4; var5++) {
         String var6;
         if ((var6 = var8[var5]).trim().startsWith(this.UH() + xM[11])) {
            int var7 = var6.indexOf(var6.trim());
            if ((var6 = var6.substring(0, var7) + xM[12] + var6.substring(var6.indexOf(xM[13]) + 3, var6.length() - 1)).trim().startsWith(xM[14])) {
               var6 = var6.substring(0, var6.length() - 1).replaceFirst(xM[15], xM[16]);
            }

            var6 = var6.replaceAll(xM[17], xM[18]);
            if (!var2) {
               var3.append(var6).append(xM[19]);
            } else {
               var3.append(xM[20]).append(var6).append(xM[21]);
            }

            var2 = false;
         } else {
            var3.append(var6).append(xM[22]);
            var2 = true;
         }
      }

      return var3.toString();
   }

   static {
      XU();
   }

   public String UH() {
      return this.dx.getDescription().getName();
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   public Reader dx(File var1) {
      boolean var6 = Go85;
      if (!var1.exists()) {
         return new InputStreamReader(new ByteArrayInputStream(new byte[0]));
      } else {
         int var2 = 0;

         IOException var10000;
         label159: {
            String var3;
            try {
               var3 = this.UH();
            } catch (IOException var26) {
               var10000 = var26;
               boolean var10001 = false;
               break label159;
            }

            StringBuilder var4;
            try {
               var4 = new StringBuilder();
            } catch (IOException var25) {
               var10000 = var25;
               boolean var38 = false;
               break label159;
            }

            try {
               var27 = Files.newBufferedReader(Paths.get(var1.getAbsolutePath()), StandardCharsets.UTF_8);
            } catch (IOException var24) {
               var10000 = var24;
               boolean var39 = false;
               break label159;
            }

            while (true) {
               String var41;
               try {
                  var31 = var27.readLine();
                  var41 = var31;
               } catch (IOException var20) {
                  var10000 = var20;
                  boolean var40 = false;
                  break;
               }

               String var5 = var41;

               label161: {
                  try {
                     try {
                        var31.equals(null);
                        break label161;
                     } catch (Exception var22) {
                     }
                  } catch (IOException var23) {
                     var10000 = var23;
                     boolean var42 = false;
                     break;
                  }

                  try {
                     var5 = var4.toString();
                  } catch (IOException var9) {
                     var10000 = var9;
                     boolean var43 = false;
                     break;
                  }

                  try {
                     var28 = new StringReader(var5);
                  } catch (IOException var8) {
                     var10000 = var8;
                     boolean var44 = false;
                     break;
                  }

                  try {
                     var27.close();
                  } catch (IOException var7) {
                     var10000 = var7;
                     boolean var45 = false;
                     break;
                  }

                  return var28;
               }

               try {
                  var32 = var5.trim();
               } catch (IOException var19) {
                  var10000 = var19;
                  boolean var46 = false;
                  break;
               }

               label162: {
                  try {
                     if (var32.startsWith(xM[0])) {
                        break label162;
                     }
                  } catch (IOException var21) {
                     var10000 = var21;
                     boolean var47 = false;
                     break;
                  }

                  byte var10002;
                  try {
                     var33 = var4.append(var5);
                     var41 = xM;
                     var10002 = 8;
                  } catch (IOException var18) {
                     var10000 = var18;
                     boolean var48 = false;
                     break;
                  }

                  try {
                     var33.append(((Object[])var41)[var10002]);
                  } catch (IOException var17) {
                     var10000 = var17;
                     boolean var50 = false;
                     break;
                  }

                  if (var6) {
                     throw null;
                  }
                  continue;
               }

               StringBuilder var34 = new StringBuilder;
               StringBuilder var51 = var34;

               try {
                  var51.<init>();
               } catch (IOException var16) {
                  var10000 = var16;
                  boolean var52 = false;
                  break;
               }

               var41 = var5;
               String[] var62 = xM;
               byte var10003 = 1;

               try {
                  var62 = Pattern.quote(var62[var10003]);
               } catch (IOException var15) {
                  var10000 = var15;
                  boolean var54 = false;
                  break;
               }

               String[] var10004;
               byte var10005;
               try {
                  var41 = var41.replaceAll(var62, Matcher.quoteReplacement(xM[2]));
                  var62 = xM[3];
                  var65 = new StringBuilder().append(var3);
                  var10004 = xM;
                  var10005 = 4;
               } catch (IOException var14) {
                  var10000 = var14;
                  boolean var55 = false;
                  break;
               }

               try {
                  var67 = var65.append(var10004[var10005]).append(var2++);
               } catch (IOException var13) {
                  var10000 = var13;
                  boolean var57 = false;
                  break;
               }

               var10004 = xM;
               var10005 = 5;

               try {
                  var35 = var34.append(var41.replaceFirst(var62, var67.append(var10004[var10005]).toString()));
                  var41 = xM[6];
               } catch (IOException var12) {
                  var10000 = var12;
                  boolean var58 = false;
                  break;
               }

               try {
                  var36 = var35.append(var41).toString();
               } catch (IOException var11) {
                  var10000 = var11;
                  boolean var60 = false;
                  break;
               }

               var5 = var36;
               StringBuilder var37 = var4;

               try {
                  var37.append(var5).append(xM[7]);
               } catch (IOException var10) {
                  var10000 = var10;
                  boolean var61 = false;
                  break;
               }

               if (var6) {
                  throw null;
               }
            }
         }

         var10000.printStackTrace();
         return null;
      }
   }

   public void dx(String param1, File param2, boolean param3) {
      // $VF: Couldn't be decompiled
      // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
      // java.lang.RuntimeException: parsing failure!
      //   at org.jetbrains.java.decompiler.modules.decompiler.decompose.DomHelper.parseGraph(DomHelper.java:211)
      //   at org.jetbrains.java.decompiler.main.rels.MethodProcessor.codeToJava(MethodProcessor.java:166)
      //
      // Bytecode:
      // 000: aload 0
      // 001: aload 1
      // 002: invokespecial me/nik/alice/q.og (Ljava/lang/String;)Ljava/lang/String;
      // 005: getstatic me/nik/alice/q.xM [Ljava/lang/String;
      // 008: bipush 23
      // 00a: aaload
      // 00b: getstatic me/nik/alice/q.xM [Ljava/lang/String;
      // 00e: bipush 24
      // 010: aaload
      // 011: invokevirtual java/lang/String.replaceAll (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
      // 014: astore 1
      // 015: new java/lang/StringBuilder
      // 018: dup
      // 019: invokespecial java/lang/StringBuilder.<init> ()V
      // 01c: astore 4
      // 01e: new java/util/Scanner
      // 021: dup
      // 022: aload 1
      // 023: invokespecial java/util/Scanner.<init> (Ljava/lang/String;)V
      // 026: astore 1
      // 027: bipush 0
      // 028: goto 02b
      // 02b: istore 5
      // 02d: goto 030
      // 030: bipush -1
      // 031: goto 034
      // 034: istore 6
      // 036: goto 039
      // 039: bipush -1
      // 03a: istore 7
      // 03c: goto 03f
      // 03f: bipush 0
      // 040: goto 043
      // 043: istore 8
      // 045: goto 048
      // 048: aload 1
      // 049: goto 04c
      // 04c: invokevirtual java/util/Scanner.hasNextLine ()Z
      // 04f: goto 052
      // 052: ifeq 1b3
      // 055: goto 058
      // 058: aload 1
      // 059: invokevirtual java/util/Scanner.nextLine ()Ljava/lang/String;
      // 05c: goto 05f
      // 05f: astore 9
      // 061: goto 064
      // 064: bipush 0
      // 065: istore 10
      // 067: goto 06a
      // 06a: bipush 0
      // 06b: goto 06e
      // 06e: istore 11
      // 070: goto 073
      // 073: bipush -1
      // 074: istore 12
      // 076: goto 079
      // 079: aload 9
      // 07b: dup
      // 07c: invokevirtual java/lang/String.trim ()Ljava/lang/String;
      // 07f: invokevirtual java/lang/String.indexOf (Ljava/lang/String;)I
      // 082: istore 13
      // 084: goto 087
      // 087: aload 9
      // 089: goto 08c
      // 08c: invokevirtual java/lang/String.trim ()Ljava/lang/String;
      // 08f: goto 092
      // 092: getstatic me/nik/alice/q.xM [Ljava/lang/String;
      // 095: goto 098
      // 098: bipush 25
      // 09a: aaload
      // 09b: invokevirtual java/lang/String.startsWith (Ljava/lang/String;)Z
      // 09e: ifeq 0d2
      // 0a1: goto 0a4
      // 0a4: bipush 1
      // 0a5: istore 11
      // 0a7: goto 0aa
      // 0aa: aload 9
      // 0ac: invokevirtual java/lang/String.trim ()Ljava/lang/String;
      // 0af: getstatic me/nik/alice/q.xM [Ljava/lang/String;
      // 0b2: bipush 26
      // 0b4: aaload
      // 0b5: getstatic me/nik/alice/q.xM [Ljava/lang/String;
      // 0b8: bipush 27
      // 0ba: aaload
      // 0bb: invokevirtual java/lang/String.replaceFirst (Ljava/lang/String;Ljava/lang/String;)Ljava/lang/String;
      // 0be: dup
      // 0bf: dup
      // 0c0: goto 0c3
      // 0c3: pop
      // 0c4: invokevirtual java/lang/String.trim ()Ljava/lang/String;
      // 0c7: invokevirtual java/lang/String.indexOf (Ljava/lang/String;)I
      // 0ca: istore 12
      // 0cc: goto 0cf
      // 0cf: goto 103
      // 0d2: aload 9
      // 0d4: invokevirtual java/lang/String.trim ()Ljava/lang/String;
      // 0d7: invokevirtual java/lang/String.isEmpty ()Z
      // 0da: ifne 103
      // 0dd: goto 0e0
      // 0e0: bipush 1
      // 0e1: istore 10
      // 0e3: goto 0e6
      // 0e6: aload 9
      // 0e8: invokevirtual java/lang/String.trim ()Ljava/lang/String;
      // 0eb: getstatic me/nik/alice/q.xM [Ljava/lang/String;
      // 0ee: bipush 28
      // 0f0: aaload
      // 0f1: goto 0f4
      // 0f4: invokevirtual java/lang/String.startsWith (Ljava/lang/String;)Z
      // 0f7: ifeq 103
      // 0fa: goto 0fd
      // 0fd: bipush 1
      // 0fe: istore 8
      // 100: goto 103
      // 103: iload 3
      // 104: ifne 182
      // 107: goto 10a
      // 10a: iload 8
      // 10c: ifne 182
      // 10f: goto 112
      // 112: iload 7
      // 114: bipush -1
      // 115: if_icmpeq 128
      // 118: goto 11b
      // 11b: iload 13
      // 11d: iload 7
      // 11f: goto 122
      // 122: if_icmpne 164
      // 125: goto 128
      // 128: iload 12
      // 12a: bipush -1
      // 12b: if_icmpeq 13e
      // 12e: goto 131
      // 131: iload 12
      // 133: iload 6
      // 135: goto 138
      // 138: if_icmplt 164
      // 13b: goto 13e
      // 13e: iload 5
      // 140: goto 143
      // 143: ifeq 151
      // 146: goto 149
      // 149: iload 10
      // 14b: ifne 164
      // 14e: goto 151
      // 151: iload 11
      // 153: ifeq 182
      // 156: goto 159
      // 159: iload 5
      // 15b: goto 15e
      // 15e: ifeq 182
      // 161: goto 164
      // 164: iload 5
      // 166: ifeq 174
      // 169: goto 16c
      // 16c: iload 11
      // 16e: ifeq 182
      // 171: goto 174
      // 174: aload 4
      // 176: bipush 10
      // 178: invokevirtual java/lang/StringBuilder.append (C)Ljava/lang/StringBuilder;
      // 17b: goto 17e
      // 17e: pop
      // 17f: goto 182
      // 182: aload 4
      // 184: aload 9
      // 186: invokevirtual java/lang/StringBuilder.append (Ljava/lang/String;)Ljava/lang/StringBuilder;
      // 189: bipush 10
      // 18b: goto 18e
      // 18e: invokevirtual java/lang/StringBuilder.append (C)Ljava/lang/StringBuilder;
      // 191: pop
      // 192: goto 195
      // 195: iload 10
      // 197: istore 5
      // 199: goto 19c
      // 19c: iload 12
      // 19e: istore 6
      // 1a0: goto 1a3
      // 1a3: iload 13
      // 1a5: istore 7
      // 1a7: goto 1aa
      // 1aa: bipush 0
      // 1ab: istore 8
      // 1ad: goto 1b0
      // 1b0: goto 048
      // 1b3: aload 1
      // 1b4: invokevirtual java/util/Scanner.close ()V
      // 1b7: goto 1db
      // 1ba: astore 5
      // 1bc: aload 1
      // 1bd: goto 1c0
      // 1c0: invokevirtual java/util/Scanner.close ()V
      // 1c3: goto 1c6
      // 1c6: goto 1c9
      // 1c9: goto 1d8
      // 1cc: astore 6
      // 1ce: aload 5
      // 1d0: aload 6
      // 1d2: invokevirtual java/lang/Throwable.addSuppressed (Ljava/lang/Throwable;)V
      // 1d5: goto 1d8
      // 1d8: aload 5
      // 1da: athrow
      // 1db: aload 2
      // 1dc: invokevirtual java/io/File.getAbsolutePath ()Ljava/lang/String;
      // 1df: goto 1e2
      // 1e2: bipush 0
      // 1e3: anewarray 12
      // 1e6: invokestatic java/nio/file/Paths.get (Ljava/lang/String;[Ljava/lang/String;)Ljava/nio/file/Path;
      // 1e9: getstatic java/nio/charset/StandardCharsets.UTF_8 Ljava/nio/charset/Charset;
      // 1ec: bipush 0
      // 1ed: anewarray 234
      // 1f0: invokestatic java/nio/file/Files.newBufferedWriter (Ljava/nio/file/Path;Ljava/nio/charset/Charset;[Ljava/nio/file/OpenOption;)Ljava/io/BufferedWriter;
      // 1f3: astore 1
      // 1f4: goto 1f7
      // 1f7: aload 1
      // 1f8: aload 4
      // 1fa: invokevirtual java/lang/StringBuilder.toString ()Ljava/lang/String;
      // 1fd: invokevirtual java/io/BufferedWriter.write (Ljava/lang/String;)V
      // 200: goto 203
      // 203: aload 1
      // 204: invokevirtual java/io/BufferedWriter.flush ()V
      // 207: goto 20a
      // 20a: aload 1
      // 20b: goto 20e
      // 20e: aconst_null
      // 20f: invokevirtual java/lang/Object.equals (Ljava/lang/Object;)Z
      // 212: pop
      // 213: goto 21a
      // 216: pop
      // 217: goto 25e
      // 21a: aload 1
      // 21b: goto 21e
      // 21e: invokevirtual java/io/BufferedWriter.close ()V
      // 221: goto 224
      // 224: return
      // 225: astore 5
      // 227: goto 22a
      // 22a: aload 1
      // 22b: goto 22e
      // 22e: invokevirtual java/lang/Object.hashCode ()I
      // 231: pop
      // 232: goto 239
      // 235: pop
      // 236: goto 25b
      // 239: goto 23c
      // 23c: aload 1
      // 23d: goto 240
      // 240: invokevirtual java/io/BufferedWriter.close ()V
      // 243: goto 246
      // 246: goto 25b
      // 249: astore 6
      // 24b: goto 24e
      // 24e: aload 5
      // 250: aload 6
      // 252: invokevirtual java/lang/Throwable.addSuppressed (Ljava/lang/Throwable;)V
      // 255: goto 258
      // 258: goto 25b
      // 25b: aload 5
      // 25d: athrow
      // 25e: return
      // 25f: invokevirtual java/io/IOException.printStackTrace ()V
      // 262: return
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   private int dx(File var1) {
      if (!var1.exists()) {
         return 0;
      } else {
         int var2 = 0;

         IOException var10000;
         label85: {
            try {
               var14 = var1.getAbsolutePath();
            } catch (IOException var12) {
               var10000 = var12;
               boolean var10001 = false;
               break label85;
            }

            byte var20 = 0;

            try {
               var15 = Paths.get(var14);
               var22 = StandardCharsets.UTF_8;
            } catch (IOException var11) {
               var10000 = var11;
               boolean var21 = false;
               break label85;
            }

            BufferedReader var3;
            try {
               var3 = Files.newBufferedReader(var15, var22);
            } catch (IOException var10) {
               var10000 = var10;
               boolean var23 = false;
               break label85;
            }

            while (true) {
               try {
                  var16 = var3.readLine();
                  var25 = var16;
               } catch (IOException var7) {
                  var10000 = var7;
                  boolean var24 = false;
                  break;
               }

               label87: {
                  try {
                     var13 = var25;

                     try {
                        var16.toString();
                        break label87;
                     } catch (Exception var8) {
                     }
                  } catch (IOException var9) {
                     var10000 = var9;
                     boolean var26 = false;
                     break;
                  }

                  BufferedReader var17 = var3;

                  try {
                     var17.close();
                  } catch (IOException var4) {
                     var10000 = var4;
                     boolean var27 = false;
                     break;
                  }

                  return var2;
               }

               String var18 = var13;

               try {
                  var19 = var18.trim();
               } catch (IOException var6) {
                  var10000 = var6;
                  boolean var28 = false;
                  break;
               }

               String var29 = xM[9];

               try {
                  if (!var19.startsWith(var29)) {
                     continue;
                  }
               } catch (IOException var5) {
                  var10000 = var5;
                  boolean var30 = false;
                  break;
               }

               var2++;
            }
         }

         var10000.printStackTrace();
         return 0;
      }
   }
}
