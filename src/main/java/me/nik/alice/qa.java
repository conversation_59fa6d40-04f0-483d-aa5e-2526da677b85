package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class qa extends PacketCheck {
   private static String[] hM;

   private static void B8() {
      hM = new String[]{"C", "Checks for colliding flight", "Ground ticks: ", " Client fly ticks: ", " Server fly ticks: "};
   }

   public qa(UC var1) {
      super(var1, CheckType.FLY, hM[0], Category.MOVE, 2.0F);
   }

   static {
      B8();
   }

   @Override
   public String sB() {
      return hM[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().tr() && !this.Aa.dx().DB() && !this.Aa.dx().dx().DP()) {
         ES var10;
         int var2 = (var10 = this.Aa.dx()).xx();
         int var3 = var10.u();
         int var4 = var10.Q();
         double var6 = var10.Ch();
         double var8 = var10.iv();
         if (var2 > 9 && var3 > 9 && var4 > 9 && var6 > var8) {
            this.Aa(hM[2] + var2 + hM[3] + var3 + hM[4] + var4);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.075);
         }
      }
   }
}
