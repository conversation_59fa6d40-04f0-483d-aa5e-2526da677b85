package me.nik.alice;

import com.comphenix.protocol.events.PacketContainer;
import me.nik.alice.wrappers.WrapperPlayClientLook;
import me.nik.alice.wrappers.WrapperPlayClientPositionLook;

public class qn {
   private float dC;
   private float Vm;
   private float b;
   private float WB;
   private float sX;
   private final UC Aa;
   private float UH;
   private float VL;
   private float sB;
   private float gz;
   private int fa;
   private final ZQ dx;
   private final xT dx;
   private float Fh;
   private float tm;
   private float yM;

   public float getYaw() {
      return this.sB;
   }

   public float getPitch() {
      return this.dC;
   }

   public float Vm() {
      return this.gz;
   }

   public float yM() {
      return this.sX;
   }

   public float VL() {
      return this.UH;
   }

   public ZQ dx() {
      return this.dx;
   }

   public xT dx() {
      return this.dx;
   }

   public float WB() {
      return this.Vm;
   }

   public float h5() {
      return this.Fh;
   }

   public float Fh() {
      return this.b;
   }

   public float gz() {
      return this.tm;
   }

   public float b() {
      return this.VL;
   }

   public float sX() {
      return this.WB;
   }

   public float UH() {
      return this.yM;
   }

   public int tV() {
      return this.fa;
   }

   public void AC(DH var1) {
      PacketContainer var2 = var1.dx();
      if (var1.PU()) {
         WrapperPlayClientPositionLook var5;
         float var8 = (var5 = new WrapperPlayClientPositionLook(var2)).getYaw();
         float var6 = var5.getPitch();
         this.dx(var8, var6);
      } else if (var1.xx()) {
         WrapperPlayClientLook var3;
         float var7 = (var3 = new WrapperPlayClientLook(var2)).getYaw();
         float var4 = var3.getPitch();
         this.dx(var7, var4);
      } else if (var1.K()) {
         this.dx.xx();
      } else {
         if (var1.qa()) {
            this.fa = 0;
         }
      }
   }

   private void dx(float var1, float var2) {
      float var3 = this.sB;
      this.Fh = var3;
      this.sB = var1;
      float var4 = this.dC;
      this.b = var4;
      this.dC = var2;
      float var5 = this.VL;
      var1 = Math.abs(var1 - var3);
      this.UH = var5;
      this.VL = var1;
      var3 = this.yM;
      var2 = Math.abs(var2 - var4);
      this.sX = var3;
      this.yM = var2;
      var4 = this.WB;
      var1 = Math.abs(var1 - var5);
      this.Vm = var4;
      this.WB = var1;
      var1 = this.gz;
      var2 = Math.abs(var2 - var3);
      this.tm = var1;
      this.gz = var2;
      this.dx.yk();
      this.dx.yk();
      this.fa++;
   }

   public qn(UC var1) {
      this.Aa = var1;
      this.dx = new ZQ(var1);
      this.dx = new xT(var1);
   }
}
