package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class qx extends PacketCheck {
   private static String[] ed;

   static {
      Os();
   }

   private static void Os() {
      ed = new String[]{"A", "Checks for invalid gravity", "Fly ticks: ", " delta: "};
   }

   public qx(UC var1) {
      super(var1, CheckType.FLY, ed[0], Category.MOVE, 1.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && !this.Aa.dx().jD()
         && !this.Aa.dx().jA()
         && this.Aa.dx().R2() >= 60
         && !this.Aa.dx().tr()
         && this.Aa.dx().yk() >= 10
         && this.Aa.dx().dx().og(2500L) <= 0
         && !this.Aa.dx().DB()
         && !this.Aa.dx().dx().DP()) {
         ES var16;
         double var3 = (var16 = this.Aa.dx()).Ch();
         double var7;
         if (Math.abs(var7 = (var16.iv() - 0.08) * 0.98F) < 0.06) {
            var7 = 0.0;
         }

         double var9 = Math.abs(var3 - var7);
         int var2 = var16.M3();
         double var12 = this.Aa.dx().dx(RH.og) ? 0.05 : 0.001;
         double var14 = Math.abs(var7);
         boolean var17 = var16.Aa().getY() % 0.5 != 0.0 && var16.a() > 2;
         if (var2 > 2 && var9 >= var12 && var14 >= 0.005 && var17) {
            this.Aa(ed[2] + var2 + ed[3] + var9);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.05);
         }
      }
   }

   @Override
   public String sB() {
      return ed[1];
   }
}
