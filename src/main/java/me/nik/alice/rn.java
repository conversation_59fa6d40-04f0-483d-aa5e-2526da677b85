package me.nik.alice;

import me.badbones69.crazyenchantments.api.CrazyEnchantments;
import me.badbones69.crazyenchantments.api.enums.CEnchantments;
import org.bukkit.entity.Player;
import org.bukkit.inventory.ItemStack;

public class rn extends f2 {
   private static String[] xw;

   @Override
   public boolean og(Player var1) {
      return false;
   }

   static {
      jj();
   }

   private static void jj() {
      xw = new String[]{"CrazyEnchantments"};
   }

   public rn() {
      super(xw[0]);
   }

   @Override
   public boolean Aa(Player var1) {
      return false;
   }

   @Override
   public boolean dx(Player var1) {
      CrazyEnchantments var2 = CrazyEnchantments.getInstance();
      ItemStack var3 = JA.dx(var1);
      return var2.hasEnchantment(var3, CEnchantments.BLAST);
   }
}
