package me.nik.alice;

import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.bukkit.entity.Player;

public class rq {
   private String jA;
   private static String[] Cc;
   private final String Vm;

   private static void tb() {
      Cc = new String[]{
         "PacketPlayOutTitle",
         "IChatBaseComponent",
         "IChatBaseComponent$ChatSerializer",
         "PacketPlayOutTitle$EnumTitleAction",
         "a",
         "{\"text\": \"",
         "\"}",
         "TITLE",
         "{\"text\": \"",
         "\"}",
         "SUBTITLE"
      };
   }

   public rq(String var1) {
      this.Vm = var1;
   }

   // $VF: Handled exception range with multiple entry points by splitting it
   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   public void Fh(Player var1) {
      Class var2 = TY.dx(Cc[0]);
      Class var3 = TY.dx(Cc[1]);
      Class var4 = TY.dx(Cc[2]);
      Class var5 = TY.dx(Cc[3]);
      Method var33 = TY.dx(var4, Cc[4], new Class[]{String.class});
      Constructor var32 = TY.dx(var2, new Class[]{var5, var3});
      Class var10000 = var2;
      Class[] var10001 = new Class[]{int.class, null, null};
      Class[] var10002 = var10001;
      byte var10003 = 1;

      label156: {
         try {
            var10002[var10003] = int.class;
            var10001[2] = int.class;
            var35 = TY.dx(var10000, var10001);
            var10001 = new Object[]{20, null, null};
            var10002 = var10001;
            var10003 = 1;
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var26) {
            var34 = var26;
            boolean var45 = false;
            break label156;
         }

         byte var10004 = 80;

         try {
            var10002[var10003] = Integer.valueOf(var10004);
            var10002 = var10001;
            var10003 = 2;
            var83 = 20;
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var25) {
            var34 = var25;
            boolean var47 = false;
            break label156;
         }

         try {
            var10002[var10003] = var83;
            var2 = (Class)var35.newInstance(var10001);
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var24) {
            var34 = var24;
            boolean var48 = false;
            break label156;
         }

         Player var36 = var1;
         var10001 = var2;

         try {
            TY.dx(var36, var10001);
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var23) {
            var34 = var23;
            boolean var50 = false;
            break label156;
         }

         Method var37 = var33;
         var10001 = null;
         Object[] var73 = new Object[1];
         Object[] var79 = var73;
         var10004 = 0;
         StringBuilder var10005 = new StringBuilder;

         rq var10006;
         try {
            var10005.<init>(Cc[5]);
            var10006 = this;
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var22) {
            var34 = var22;
            boolean var52 = false;
            break label156;
         }

         try {
            var93 = var10006.Vm;
         } catch (NullPointerException var21) {
            var34 = var21;
            boolean var53 = false;
            break label156;
         }

         try {
            var10005 = var10005.append(var93);
            var94 = Cc;
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var20) {
            var34 = var20;
            boolean var54 = false;
            break label156;
         }

         try {
            var79[var10004] = var10005.append(var94[6]).toString();
            var10000 = (Class)var37.invoke(var10001, var73);
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var19) {
            var34 = var19;
            boolean var55 = false;
            break label156;
         }

         var2 = var10000;

         try {
            var39 = var32;
            var10001 = new Object[]{TY.dx(var5, Cc[7]).get(null), null};
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var18) {
            var34 = var18;
            boolean var56 = false;
            break label156;
         }

         Object[] var74 = var10001;
         var10003 = 1;
         Object var85 = var2;

         try {
            var74[var10003] = var85;
            var10000 = (Class)var39.newInstance(var10001);
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var17) {
            var34 = var17;
            boolean var58 = false;
            break label156;
         }

         var2 = var10000;
         Player var41 = var1;

         try {
            TY.dx(var41, var2);
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var16) {
            var34 = var16;
            boolean var59 = false;
            break label156;
         }

         label157: {
            try {
               String var42 = this.jA;

               try {
                  var42.hashCode();
               } catch (Exception var14) {
                  break label157;
               }
            } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var15) {
               var34 = var15;
               boolean var60 = false;
               break label156;
            }

            try {
               var43 = var33;
               var10001 = null;
               var10002 = new Object[1];
               var81 = var10002;
               var10004 = 0;
               var10005 = new StringBuilder(Cc[8]);
            } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var13) {
               var34 = var13;
               boolean var61 = false;
               break label156;
            }

            byte var10007;
            try {
               var10005 = var10005.append(Dq.sB(this.jA));
               var95 = Cc;
               var10007 = 9;
            } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var12) {
               var34 = var12;
               boolean var63 = false;
               break label156;
            }

            String var96 = var95[var10007];

            try {
               var10005 = var10005.append(var96);
            } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var11) {
               var34 = var11;
               boolean var64 = false;
               break label156;
            }

            try {
               var81[var10004] = var10005.toString();
               var2 = (Class)var43.invoke(var10001, var10002);
            } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var10) {
               var34 = var10;
               boolean var65 = false;
               break label156;
            }

            Constructor var44 = var32;
            Object[] var66 = new Object[2];
            Object[] var76 = var66;
            var10003 = 0;
            Class var87 = var5;
            String[] var92 = Cc;
            byte var97 = 10;

            try {
               var76[var10003] = TY.dx(var87, var92[var97]).get(null);
               var66[1] = var2;
            } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var9) {
               var34 = var9;
               boolean var67 = false;
               break label156;
            }

            try {
               var2 = (Class)var44.newInstance(var66);
            } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var8) {
               var34 = var8;
               boolean var68 = false;
               break label156;
            }

            try {
               TY.dx(var1, var2);
            } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var7) {
               var34 = var7;
               boolean var69 = false;
               break label156;
            }
         }

         try {
            return;
         } catch (InvocationTargetException | IllegalAccessException | NullPointerException | InstantiationException var6) {
            var34 = var6;
            boolean var70 = false;
         }
      }

      var34.printStackTrace();
   }

   public rq(String var1, String var2) {
      this.Vm = var1;
      this.jA = var2;
   }

   static {
      tb();
   }
}
