package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class rr extends PacketCheck {
   private static String[] 9i;

   private static void O1() {
      9i = new String[]{"G", "Checks for flight by using collisions", "Fly ticks: ", " delta Y: "};
   }

   @Override
   public String sB() {
      return 9i[1];
   }

   static {
      O1();
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().jD() && !this.Aa.dx().dx().DP() && !this.Aa.dx().DB()) {
         ES var9 = this.Aa.dx();
         Qk var2 = this.Aa.dx();
         double var4 = var9.Ch();
         int var3 = var9.M3();
         int var6 = (int)(Math.abs(var2.getVelocityY()) * 75.0);
         int var7 = var9.dx().og(5000L);
         int var8 = 20;
         if (var2.tr()) {
            var8 = 20 + var6 + 10;
         }

         if (var9.R2() < 60) {
            var8 += 60;
         }

         if (var7 > 0) {
            var8 += 3 * var7;
         }

         if (var4 >= 0.0 && var3 > var8) {
            this.Aa(9i[2] + var3 + 9i[3] + var4);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.025);
         }
      }
   }

   public rr(UC var1) {
      super(var1, CheckType.FLY, 9i[0], Category.MOVE, 5.0F);
   }
}
