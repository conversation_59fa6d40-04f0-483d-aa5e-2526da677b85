package me.nik.alice;

public class s6
{
    private org.bukkit.block.BlockFace dx;
    private org.bukkit.util.Vector dx;
    private double iv;
    private org.bukkit.entity.Entity dx;
    private org.bukkit.block.Block dx;
    private static java.lang.String[] ir;
    
    static {
        Qk();
    }
    
    public double Vm() {
        return this.iv;
    }
    
    @java.lang.Override
    public java.lang.String toString() {
        return me.nik.alice.s6.ir[0] + this.dx + me.nik.alice.s6.ir[1] + this.dx + me.nik.alice.s6.ir[2] + this.dx + me.nik.alice.s6.ir[3] + this.dx + me.nik.alice.s6.ir[4];
    }
    
    public org.bukkit.util.Vector sB() {
        return this.dx.clone();
    }
    
    public s6() {
    }
    
    @java.lang.Override
    public boolean equals(final java.lang.Object o) {
        if (this == o) {
            return true;
        }
        if (!(o instanceof me.nik.alice.s6)) {
            return false;
        }
        final me.nik.alice.s6 s6 = (me.nik.alice.s6)o;
        return this.dx.equals((java.lang.Object)s6.dx) && java.util.Objects.equals(this.dx, s6.dx) && java.util.Objects.equals(this.dx, s6.dx) && java.util.Objects.equals(this.dx, s6.dx);
    }
    
    public me.nik.alice.s6 dx(final org.bukkit.block.Block dx) {
        this.dx = dx;
        return this;
    }
    
    public me.nik.alice.s6 dx(final org.bukkit.block.BlockFace dx) {
        this.dx = dx;
        return this;
    }
    
    public me.nik.alice.s6 dx(final org.bukkit.util.Vector dx) {
        this.dx = dx;
        return this;
    }
    
    private static void Qk() {
        me.nik.alice.s6.ir = new java.lang.String[] { "RayTraceResult [hitPosition=", ", hitBlock=", ", hitBlockFace=", ", hitEntity=", "]" };
    }
    
    public s6(final org.bukkit.util.Vector vector, final org.bukkit.block.Block dx, final org.bukkit.block.BlockFace dx2, final org.bukkit.entity.Entity dx3, final double iv) {
        this.dx = vector.clone();
        this.dx = dx;
        this.dx = dx2;
        this.dx = dx3;
        this.iv = iv;
    }
    
    public org.bukkit.block.Block dx() {
        return this.dx;
    }
    
    @java.lang.Override
    public int hashCode() {
        final boolean mDcz = me.nik.alice.s6.MDcz;
        final int n = (31 + this.dx.hashCode()) * 31;
        int hashCode;
        if (this.dx == null) {
            hashCode = 0;
            if (mDcz) {
                throw null;
            }
        }
        else {
            hashCode = this.dx.hashCode();
        }
        final int n2 = (n + hashCode) * 31;
        int hashCode2;
        if (this.dx == null) {
            hashCode2 = 0;
            if (mDcz) {
                throw null;
            }
        }
        else {
            hashCode2 = this.dx.hashCode();
        }
        final int n3 = (n2 + hashCode2) * 31;
        int hashCode3;
        if (this.dx == null) {
            hashCode3 = 0;
            if (mDcz) {
                throw null;
            }
        }
        else {
            hashCode3 = this.dx.hashCode();
        }
        return n3 + hashCode3;
    }
    
    public me.nik.alice.s6 dx(final org.bukkit.entity.Entity dx) {
        this.dx = dx;
        return this;
    }
    
    public me.nik.alice.s6 dx(final double iv) {
        this.iv = iv;
        return this;
    }
    
    public org.bukkit.entity.Entity dx() {
        return this.dx;
    }
    
    public org.bukkit.block.BlockFace dx() {
        return this.dx;
    }
}
