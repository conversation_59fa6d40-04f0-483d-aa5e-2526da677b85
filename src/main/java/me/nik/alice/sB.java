package me.nik.alice;

import java.nio.charset.StandardCharsets;
import java.util.ArrayList;
import java.util.Base64;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.atomic.AtomicInteger;
import me.nik.alice.sB.1;

public class sB {
   private boolean dx;
   private final Map dx = new LinkedHashMap();
   private static String[] Ac;
   private final int og;
   private final int sB;
   private final int Aa;
   private final String AC;
   private final String og = this.og();
   private final int AC;
   private final int dx;
   private boolean Aa;

   public sB() {
      this.AC = this.Aa();
      this.dx = ThreadLocalRandom.current().nextInt(3);
      this.Aa = ThreadLocalRandom.current().nextInt(3);
      this.og = ThreadLocalRandom.current().nextInt(3);
      this.AC = ThreadLocalRandom.current().nextInt(3);
      this.sB = ThreadLocalRandom.current().nextInt(3);
   }

   public String dx(int var1) {
      switch (var1) {
         case 0:
            byte[] var18 = new byte[]{
               -75,
               -125,
               -122,
               -77,
               -125,
               -75,
               -123,
               -123,
               -80,
               -127,
               -78,
               -124,
               -128,
               -128,
               -76,
               -79,
               -79,
               -78,
               -79,
               -78,
               127,
               -127,
               -125,
               -127,
               -80,
               -78,
               -128,
               -126,
               127,
               127,
               127,
               -127,
               -119,
               -68,
               -76,
               125,
               -67,
               -72,
               -70,
               125,
               -75,
               -80,
               -62,
               -61,
               -68,
               -80,
               -61,
               -73,
               125,
               -107,
               -80,
               -62,
               -61,
               -100,
               -80,
               -61,
               -73
            };
            ArrayList var25 = new ArrayList();

            for (byte var50 : var18) {
               var25.add((byte)(var50 + -289350223));
            }

            var18 = new byte[var25.size()];

            for (int var38 = 0; var38 < var25.size(); var38++) {
               var18[var38] = (Byte)var25.get(var38);
            }

            return new String(var18);
         case 1:
            byte[] var16 = new byte[]{
               -89,
               120,
               122,
               117,
               117,
               122,
               -87,
               -91,
               118,
               -89,
               122,
               117,
               117,
               -87,
               -90,
               -90,
               -89,
               -90,
               -89,
               116,
               118,
               120,
               118,
               -91,
               -89,
               117,
               119,
               116,
               116,
               116,
               118,
               126,
               -79,
               -87,
               114,
               -78,
               -83,
               -81,
               114,
               -86,
               -91,
               -73,
               -72,
               -79,
               -91,
               -72,
               -84,
               114,
               -118,
               -91,
               -73,
               -72,
               -111,
               -91,
               -72,
               -84
            };
            ArrayList var24 = new ArrayList();

            for (byte var49 : var16) {
               var24.add((byte)(var49 + -157095492));
            }

            var16 = new byte[var24.size()];

            for (int var36 = 0; var36 < var24.size(); var36++) {
               var16[var36] = (Byte)var24.get(var36);
            }

            return new String(var16);
         case 2:
            byte[] var14 = new byte[]{
               -21,
               -71,
               -70,
               -74,
               -65,
               -20,
               -66,
               -25,
               -72,
               -23,
               -68,
               -73,
               -73,
               -21,
               -24,
               -24,
               -23,
               -24,
               -23,
               -74,
               -72,
               -70,
               -72,
               -25,
               -23,
               -73,
               -71,
               -74,
               -74,
               -74,
               -72,
               -64,
               -13,
               -21,
               -76,
               -12,
               -17,
               -15,
               -76,
               -20,
               -25,
               -7,
               -6,
               -13,
               -25,
               -6,
               -18,
               -76,
               -52,
               -25,
               -7,
               -6,
               -45,
               -25,
               -6,
               -18
            };
            ArrayList var23 = new ArrayList();

            for (byte var48 : var14) {
               var23.add((byte)(var48 + 196031354));
            }

            var14 = new byte[var23.size()];

            for (int var34 = 0; var34 < var23.size(); var34++) {
               var14[var34] = (Byte)var23.get(var34);
            }

            return new String(var14);
         case 3:
            byte[] var12 = new byte[]{
               -71,
               -122,
               -122,
               -125,
               -125,
               -116,
               -123,
               -76,
               -123,
               -74,
               -119,
               -124,
               -124,
               -72,
               -75,
               -75,
               -74,
               -75,
               -74,
               -125,
               -123,
               -121,
               -123,
               -76,
               -74,
               -124,
               -122,
               -125,
               -125,
               -125,
               -123,
               -115,
               -64,
               -72,
               -127,
               -63,
               -68,
               -66,
               -127,
               -71,
               -76,
               -58,
               -57,
               -64,
               -76,
               -57,
               -69,
               -127,
               -103,
               -76,
               -58,
               -57,
               -96,
               -76,
               -57,
               -69
            };
            ArrayList var22 = new ArrayList();

            for (byte var47 : var12) {
               var22.add((byte)(var47 + 186084781));
            }

            var12 = new byte[var22.size()];

            for (int var32 = 0; var32 < var22.size(); var32++) {
               var12[var32] = (Byte)var22.get(var32);
            }

            return new String(var12);
         case 4:
            byte[] var10 = new byte[]{
               99,
               51,
               53,
               54,
               97,
               50,
               99,
               54,
               49,
               49,
               101,
               98,
               98,
               99,
               98,
               99,
               48,
               50,
               52,
               50,
               97,
               99,
               49,
               51,
               48,
               48,
               48,
               50,
               58,
               109,
               101,
               46,
               110,
               105,
               107,
               46,
               102,
               97,
               115,
               116,
               109,
               97,
               116,
               104,
               46,
               78,
               117,
               109,
               98,
               101,
               114,
               115,
               85,
               116,
               105,
               108,
               115
            };
            ArrayList var21 = new ArrayList();

            for (byte var46 : var10) {
               var21.add((byte)(var46 + -129061632));
            }

            var10 = new byte[var21.size()];

            for (int var30 = 0; var30 < var21.size(); var30++) {
               var10[var30] = (Byte)var21.get(var30);
            }

            return new String(var10);
         case 5:
            byte[] var8 = new byte[]{
               29,
               -15,
               -16,
               27,
               -23,
               -19,
               26,
               -21,
               28,
               -17,
               -22,
               -22,
               30,
               27,
               27,
               28,
               27,
               28,
               -23,
               -21,
               -19,
               -21,
               26,
               28,
               -22,
               -20,
               -23,
               -23,
               -23,
               -21,
               -13,
               38,
               30,
               -25,
               39,
               34,
               36,
               -25,
               31,
               26,
               44,
               45,
               38,
               26,
               45,
               33,
               -25,
               7,
               46,
               38,
               27,
               30,
               43,
               44,
               14,
               45,
               34,
               37,
               44
            };
            ArrayList var20 = new ArrayList();

            for (byte var45 : var8) {
               var20.add((byte)(var45 + 290862919));
            }

            var8 = new byte[var20.size()];

            for (int var28 = 0; var28 < var20.size(); var28++) {
               var8[var28] = (Byte)var20.get(var28);
            }

            return new String(var8);
         case 6:
            byte[] var2 = new byte[]{
               -8,
               -53,
               -49,
               -9,
               -49,
               -50,
               -9,
               -56,
               -7,
               -52,
               -57,
               -57,
               -5,
               -8,
               -8,
               -7,
               -8,
               -7,
               -58,
               -56,
               -54,
               -56,
               -9,
               -7,
               -57,
               -55,
               -58,
               -58,
               -58,
               -56,
               -48,
               3,
               -5,
               -60,
               4,
               -1,
               1,
               -60,
               -4,
               -9,
               9,
               10,
               3,
               -9,
               10,
               -2,
               -60,
               -28,
               11,
               3,
               -8,
               -5,
               8,
               9,
               -21,
               10,
               -1,
               2,
               9
            };
            ArrayList var3 = new ArrayList();

            for (byte var6 : var2) {
               var3.add((byte)(var6 + -117153174));
            }

            var2 = new byte[var3.size()];

            for (int var26 = 0; var26 < var3.size(); var26++) {
               var2[var26] = (Byte)var3.get(var26);
            }

            return new String(var2);
         default:
            return null;
      }
   }

   public boolean dx() {
      return this.Aa;
   }

   void VL() {
      AtomicInteger var1 = new AtomicInteger();
      AtomicInteger var2 = new AtomicInteger();
      this.dx.forEach(this::dx);
      this.dx.clear();
      boolean var3 = false;
      if (var2.get() == var1.get() || var2.get() == 0) {
         var3 = true;
      }

      if (var1.get() > 0) {
         var3 = true;
      }

      if (var3) {
         this.dx = true;
      }
   }

   private String Aa() {
      byte[] var2 = new byte[]{75};
      ArrayList var3 = new ArrayList();

      for (byte var6 : var2) {
         var3.add((byte)(var6 + 406729699));
      }

      var2 = new byte[var3.size()];

      for (int var8 = 0; var8 < var3.size(); var8++) {
         var2[var8] = (Byte)var3.get(var8);
      }

      return new String(var2);
   }

   private String og() {
      boolean var7 = MbMb;
      byte[] var2 = new byte[]{127};
      ArrayList var3 = new ArrayList();
      int var4 = var2.length;
      int var5 = 0;

      while (var5 < var4) {
         byte var6 = var2[var5];
         var3.add((byte)(var6 + 388459707));
         var5++;
         if (var7) {
            throw null;
         }
      }

      var2 = new byte[var3.size()];
      var4 = 0;

      while (var4 < var3.size()) {
         var2[var4] = (Byte)var3.get(var4);
         var4++;
         if (var7) {
            throw null;
         }
      }

      return new String(var2);
   }

   static {
      x8();
   }

   void Fh() {
      new 1(this).runTaskTimerAsynchronously(Alice.dx(), 20L, 20L);
   }

   byte[] Aa(byte[] var1) {
      byte[] var3 = new byte[var1.length];

      for (int var4 = 0; var4 < var1.length; var4++) {
         var3[var4] = (byte)(var1[var4] + 2);
      }

      return var3;
   }

   private static void x8() {
      Ac = new String[]{":", ":"};
   }

   public void b() {
      boolean var2 = MbMb;
      int var1 = 0;

      while (var1 < 7) {
         this.dx(var1);
         var1++;
         if (var2) {
            throw null;
         }
      }

      this.Fh();
      this.Aa = true;
   }

   void dx(int var1) {
      var1 = var1
         + 5
         + 5
         + 20
         + 15
         + 20
         + 1
         + 5
         + 20
         + 20
         + 55
         + this.sB
         + this.AC
         + this.og
         + this.Aa
         + this.dx
         - (166 + this.sB + this.AC + this.og + this.Aa + this.dx);
      String var4;
      if ((var4 = this.dx(var1)).contains(this.og)) {
         String var2 = var4.split(Ac[0])[0];
         String var5 = var4.split(Ac[1])[1];
         if (var2.length() > 0 && var5.length() > 0 && var5.contains(this.AC)) {
            var2 = Base64.getEncoder().encodeToString(var2.getBytes(StandardCharsets.UTF_8));
            String var6 = Base64.getEncoder().encodeToString(var5.getBytes(StandardCharsets.UTF_8));
            var2 = new String(this.Aa(var2.getBytes(StandardCharsets.UTF_8)));
            String var7 = new String(this.Aa(var6.getBytes(StandardCharsets.UTF_8)));
            this.dx.put(var2, var7);
         }
      }
   }

   public boolean Aa() {
      return this.dx;
   }

   private void dx(AtomicInteger param1, AtomicInteger param2, String param3, String param4) {
      // $VF: Couldn't be decompiled
      // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
      // java.lang.RuntimeException: parsing failure!
      //   at org.jetbrains.java.decompiler.modules.decompiler.decompose.DomHelper.parseGraph(DomHelper.java:211)
      //   at org.jetbrains.java.decompiler.main.rels.MethodProcessor.codeToJava(MethodProcessor.java:166)
      //
      // Bytecode:
      // 00: new java/lang/String
      // 03: dup
      // 04: aload 0
      // 05: aload 3
      // 06: getstatic java/nio/charset/StandardCharsets.UTF_8 Ljava/nio/charset/Charset;
      // 09: invokevirtual java/lang/String.getBytes (Ljava/nio/charset/Charset;)[B
      // 0c: invokevirtual me/nik/alice/sB.dx ([B)[B
      // 0f: invokespecial java/lang/String.<init> ([B)V
      // 12: astore 3
      // 13: new java/lang/String
      // 16: dup
      // 17: aload 0
      // 18: aload 4
      // 1a: getstatic java/nio/charset/StandardCharsets.UTF_8 Ljava/nio/charset/Charset;
      // 1d: invokevirtual java/lang/String.getBytes (Ljava/nio/charset/Charset;)[B
      // 20: invokevirtual me/nik/alice/sB.dx ([B)[B
      // 23: invokespecial java/lang/String.<init> ([B)V
      // 26: astore 4
      // 28: new java/lang/String
      // 2b: dup
      // 2c: invokestatic java/util/Base64.getDecoder ()Ljava/util/Base64$Decoder;
      // 2f: aload 3
      // 30: invokevirtual java/util/Base64$Decoder.decode (Ljava/lang/String;)[B
      // 33: invokespecial java/lang/String.<init> ([B)V
      // 36: astore 3
      // 37: new java/lang/String
      // 3a: dup
      // 3b: invokestatic java/util/Base64.getDecoder ()Ljava/util/Base64$Decoder;
      // 3e: aload 4
      // 40: invokevirtual java/util/Base64$Decoder.decode (Ljava/lang/String;)[B
      // 43: invokespecial java/lang/String.<init> ([B)V
      // 46: astore 4
      // 48: aload 4
      // 4a: invokestatic java/lang/Class.forName (Ljava/lang/String;)Ljava/lang/Class;
      // 4d: aload 3
      // 4e: invokevirtual java/lang/Class.getDeclaredField (Ljava/lang/String;)Ljava/lang/reflect/Field;
      // 51: dup
      // 52: astore 3
      // 53: goto 56
      // 56: bipush 1
      // 57: invokevirtual java/lang/reflect/Field.setAccessible (Z)V
      // 5a: goto 5d
      // 5d: aload 3
      // 5e: goto 61
      // 61: invokevirtual java/lang/reflect/Field.getName ()Ljava/lang/String;
      // 64: invokevirtual java/lang/String.length ()I
      // 67: bipush 2
      // 68: if_icmple c6
      // 6b: goto 6e
      // 6e: aload 3
      // 6f: invokevirtual java/lang/reflect/Field.isAccessible ()Z
      // 72: ifeq c6
      // 75: goto 78
      // 78: aload 3
      // 79: goto 7c
      // 7c: aconst_null
      // 7d: invokevirtual java/lang/reflect/Field.get (Ljava/lang/Object;)Ljava/lang/Object;
      // 80: checkcast java/lang/String
      // 83: dup
      // 84: astore 3
      // 85: invokevirtual java/lang/String.length ()I
      // 88: bipush 2
      // 89: goto 8c
      // 8c: if_icmple c6
      // 8f: goto 92
      // 92: bipush 0
      // 93: goto 96
      // 96: istore 4
      // 98: goto 9b
      // 9b: aload 3
      // 9c: invokestatic java/lang/Double.parseDouble (Ljava/lang/String;)D
      // 9f: pop2
      // a0: goto a3
      // a3: bipush 1
      // a4: istore 4
      // a6: goto a9
      // a9: goto b0
      // ac: pop
      // ad: goto b0
      // b0: goto b3
      // b3: iload 4
      // b5: ifeq c6
      // b8: goto bb
      // bb: aload 1
      // bc: goto bf
      // bf: invokevirtual java/util/concurrent/atomic/AtomicInteger.getAndIncrement ()I
      // c2: pop
      // c3: goto c6
      // c6: return
      // c7: pop
      // c8: aload 2
      // c9: invokevirtual java/util/concurrent/atomic/AtomicInteger.getAndIncrement ()I
      // cc: pop
      // cd: return
   }

   byte[] dx(byte[] var1) {
      boolean var5 = MbMb;
      byte[] var3 = new byte[var1.length];
      int var4 = 0;

      while (var4 < var1.length) {
         var3[var4] = (byte)(var1[var4] - 2);
         var4++;
         if (var5) {
            throw null;
         }
      }

      return var3;
   }
}
