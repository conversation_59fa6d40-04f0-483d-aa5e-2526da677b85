package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import org.bukkit.Location;
import org.bukkit.block.Block;
import org.bukkit.event.Event;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;

public class sX extends EventCheck {
   private static String[] 8C;
   private final o2 dx = new o2();

   @Override
   public void on(Event var1) {
      if (var1 instanceof BlockBreakEvent) {
         BlockBreakEvent var5;
         if (!(var5 = (BlockBreakEvent)var1).isCancelled()) {
            Block var6 = var5.getBlock();
            if (me.nik.alice.Ww.dx.Zb.Aa().contains(var6.getType().toString().toUpperCase())) {
               Location var8 = var6.getLocation();
               if (!me.nik.alice.Ww.dx.qJ.dC() || var8.getBlockY() < var6.getWorld().getHighestBlockAt(var8).getY()) {
                  this.dx.Aa(var8);
                  int var7;
                  if ((var7 = this.dx.sB()) > me.nik.alice.Ww.dx.GD.Aa()) {
                     this.og(8C[1].concat(String.valueOf(var7)));
                     this.dx.reset();
                  }
               }
            }
         }
      } else {
         if (var1 instanceof BlockPlaceEvent) {
            BlockPlaceEvent var3;
            if ((var3 = (BlockPlaceEvent)var1).isCancelled()) {
               return;
            }

            Block var4 = var3.getBlockPlaced();
            if (!me.nik.alice.Ww.dx.Zb.Aa().contains(var4.getType().name().toUpperCase())) {
               return;
            }

            Location var2 = var4.getLocation();
            if (me.nik.alice.Ww.dx.qJ.dC() && var2.getBlockY() >= var4.getWorld().getHighestBlockAt(var2).getY()) {
               return;
            }

            this.dx.og(var2);
         }
      }
   }

   public sX(UC var1) {
      super(var1, CheckType.XRAY, Category.WORLD);
   }

   private static void v7() {
      8C = new String[]{"Checks for high amount of broken ores", "Mined Ores: "};
   }

   static {
      v7();
   }

   @Override
   public String sB() {
      return 8C[0];
   }
}
