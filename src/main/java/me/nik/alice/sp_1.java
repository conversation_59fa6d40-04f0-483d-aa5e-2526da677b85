package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class sp extends PacketCheck {
   private long sX;
   private static String[] Kf;

   @Override
   public void dx(DH var1) {
   }

   static {
      Xe();
   }

   @Override
   public String sB() {
      return Kf[1];
   }

   public sp(UC var1) {
      super(var1, CheckType.INTERACT, Kf[0], Category.WORLD);
   }

   private static void Xe() {
      Kf = new String[]{"FastBreak", "Checks for irregular block breaking speed"};
   }
}
