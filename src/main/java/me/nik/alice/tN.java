package me.nik.alice;

import java.util.List;
import java.util.stream.Collectors;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.fastmath.FastMath;
import org.bukkit.GameMode;
import org.bukkit.entity.Entity;
import org.bukkit.entity.Player;
import org.bukkit.util.Vector;

public class tN extends PacketCheck {
   private static String[] tG;

   private static double dx(double var0, double var2, Vector var4) {
      double var6 = gW.dx(var4).Fh();
      return l.hypot(var0 - var4.getX(), var2 - var4.getZ()) - var6;
   }

   @Override
   public String sB() {
      return tG[1];
   }

   private static void Rs() {
      tG = new String[]{"A", "Checks for combat reach", "Distance: ", " FM: ", "ms LL: ", " ms"};
   }

   static {
      Rs();
   }

   public tN(UC var1) {
      super(var1, CheckType.REACH, tG[0], Category.COMBAT, 4.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.Tq()) {
         Entity var2;
         Fc var25;
         Entity var10000 = var2 = (var25 = this.Aa.dx()).Aa();

         try {
            var10000.toString();
         } catch (Exception var24) {
            return;
         }

         if (var25.dx().h5()
            && var2 instanceof Player
            && this.Aa.dx().Vm() >= 20
            && !this.Aa.dx().o4()
            && var2 == var25.og()
            && this.Aa.getPlayer().getGameMode() != GameMode.CREATIVE) {
            Sk var3 = this.Aa.dx();
            long var6 = (long)FastMath.max(25, var3.Zm());
            long var8 = this.Aa.dx().dx().sB(1000L) > 0 ? 200L : me.nik.alice.Ww.dx.z.Aa();
            List var26 = (List)var25.dx().dx(var6, var8).stream().map(bV::dx).collect(Collectors.toList());
            Vector var4;
            double var13 = (var4 = this.Aa.dx().Aa().clone().toVector()).getX();
            double var15 = var4.getZ();
            float var27 = (float)l.Aa(var26.stream().mapToDouble(tN::broadCast).min().orElse(0.0), 2);
            UC var28;
            if ((var28 = Alice.dx().dx().dx((Player)var2)) != null && var28.dx().h5() < 2500L) {
               var27--;
            }

            float var29;
            if ((var29 = this.Aa.dx().getPitch()) > -6.5F && var29 < -5.0F) {
               var27 -= 0.15F;
            }

            long var20 = var3.Fh();
            long var22 = var3.h5();
            float var30 = var20 < 25L || var22 < 5000L ? 0.125F : (var6 > 800L ? 0.25F : 1.0F);
            if (var27 >= me.nik.alice.Ww.dx.YL.sB()) {
               this.Aa(tG[2] + var27 + tG[3] + var20 + tG[4] + var22 + tG[5]);
               if (this.broadCast((double)var30) > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.075);
            }
         }
      }
   }
}
