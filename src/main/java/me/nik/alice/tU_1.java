package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientChat;

public class tU extends PacketCheck {
   private static String[] uC;

   public tU(UC var1) {
      super(var1, CheckType.PACKET, uC[0], Category.WORLD);
   }

   @Override
   public String sB() {
      return uC[1];
   }

   private static void jh() {
      uC = new String[]{"M", "Checks for empty chat packets", "Sent empty chat message"};
   }

   @Override
   public void dx(DH var1) {
      if (var1.Vl()) {
         String var3;
         String var10000 = var3 = new WrapperPlayClientChat(var1.dx()).getMessage();

         label18: {
            try {
               var10000.hashCode();
            } catch (Exception var2) {
               break label18;
            }

            if (!var3.isEmpty()) {
               return;
            }
         }

         this.og(uC[2]);
      }
   }

   static {
      jh();
   }
}
