package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class tV extends PacketCheck {
   private static String[] 8p;

   @Override
   public String sB() {
      return 8p[1];
   }

   private static void LD() {
      8p = new String[]{"K", "Checks for big rotations without decelerating", "Dy: ", " accel: ", " delta XZ: "};
   }

   static {
      LD();
   }

   public tV(UC var1) {
      super(var1, CheckType.PACKET, 8p[0], Category.WORLD, 3.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.R() && !this.Aa.dx().Fh()) {
         ES var11 = this.Aa.dx();
         float var2 = this.Aa.dx().b();
         double var4 = var11.F7();
         double var6 = var11.DB();
         Qk var12 = this.Aa.dx();
         double var9 = 1.0E-5 - (var12.tr() ? var12.cO() : 0.0);
         if (var2 >= 10.0F && var6 > 0.0 && var6 < var9 && var4 > 0.15) {
            this.Aa(8p[2] + var2 + 8p[3] + var6 + 8p[4] + var4);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.125);
         }
      }
   }
}
