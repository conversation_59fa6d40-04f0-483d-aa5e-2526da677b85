package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class tr extends PacketCheck {
   private static String[] iF;

   public tr(UC var1) {
      super(var1, CheckType.PACKET, iF[0], Category.WORLD, 100.0F);
   }

   @Override
   public String sB() {
      return iF[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.M3()) {
         Sk var5;
         int var2 = (var5 = this.Aa.dx()).jA();
         int var3 = var5.Zm();
         ES var4 = this.Aa.dx();
         boolean var6 = var5.Fh() < 25L || this.Aa.AC() < 10000L || this.Aa.dx().o4() || var5.Aa(5) || var4.nZ() < 80 || Ku.QJ() < 18.0;
         int var7;
         if ((var7 = Math.abs(var2 - var3)) > 250 && !var6) {
            this.Aa(iF[2] + var2 + iF[3] + var3 + iF[4] + var7);
            if (this.Aa() > this.dx()) {
               this.sX();
               this.Aa((double)(this.AC() / 2.0F));
               return;
            }
         } else {
            this.Aa((double)(this.AC() / 10.0F));
         }
      }
   }

   private static void ga() {
      iF = new String[]{"AA", "Checks for spoofed ping", "K: ", " T: ", " Delta: "};
   }

   static {
      ga();
   }
}
