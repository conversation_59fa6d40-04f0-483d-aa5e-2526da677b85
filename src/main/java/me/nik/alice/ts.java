package me.nik.alice;

import java.util.Iterator;
import java.util.LinkedHashSet;
import java.util.Set;
import me.nik.alice.api.events.AliceViolationEvent;
import me.nik.fastmath.FastMath;
import org.bukkit.Bukkit;
import org.bukkit.entity.Player;

public abstract class ts {
   private final boolean experimental;
   private float AC;
   private int vl;
   private final b AC;
   private static String[] Fz;
   protected final UC Aa;
   private float og;
   private final Set dx = new LinkedHashSet();
   private String Zh;
   private final String fullCheckName;
   private final String checkName;
   private final boolean Zh;
   private final String dC;
   private final int maxVl;
   private final boolean enabled;

   public boolean isEnabled() {
      return this.enabled;
   }

   static {
      0t();
   }

   public void Aa(int var1) {
      this.vl = var1;
   }

   private static void _t/* $VF was: 0t*/() {
      Fz = new String[]{
         " ", "_", ".enabled", ".", ".enabled", ".", ".max_vl", ".commands", "", " (", ")", "", " (", ")", "%check%", "%information%", "%player%"
      };
   }

   public void og(String var1) {
      this.Zh = var1;
      this.sX();
   }

   public float dx() {
      return this.AC;
   }

   public String dC() {
      return this.dC;
   }

   protected float Aa() {
      return this.og = FastMath.min(10000.0F, this.og + 1.0F);
   }

   protected long dx() {
      return System.currentTimeMillis();
   }

   public b dx() {
      return this.AC;
   }

   public String AC() {
      return this.checkName;
   }

   public ts(UC var1, VL var2, String var3, b var4, float var5) {
      this.Aa = var1;
      this.checkName = var2.AC();
      this.dC = var3;
      this.AC = var5;
      D8 var7 = Alice.dx().Aa();
      String var8 = var2.AC().toLowerCase();
      String var6 = var3.toLowerCase().replace(Fz[0], Fz[1]);
      boolean var9;
      if (var3.isEmpty()) {
         var9 = var7.getBoolean(var8 + Fz[2]);
      } else {
         var9 = var7.getBoolean(var8 + Fz[3] + var6 + Fz[4], var7.getBoolean(var8 + Fz[5] + var6));
      }

      this.Zh = !me.nik.alice.UN.dx.zP.dC() && (var2 == VL.h5 || var2 == VL.AC || var2 == VL.Fh);
      this.enabled = var9;
      this.maxVl = var7.getInt(var8 + Fz[6]);
      this.dx.addAll(var7.getStringList(var8 + Fz[7]));
      this.experimental = this.getClass().isAnnotationPresent(h5.class);
      this.AC = var4;
      this.fullCheckName = this.checkName + (var3.isEmpty() ? Fz[8] : Fz[9] + var3 + Fz[10]);
   }

   protected float Aa(double var1) {
      return this.og = (float)FastMath.max(0.0, (double)this.og - var1);
   }

   protected void dx(Object var1) {
      Bukkit.broadcastMessage(String.valueOf(var1));
   }

   protected void Vm() {
      this.og = 0.0F;
   }

   public void sX() {
      if (!Alice.dx().dx().Aa() && Alice.dx().dx().dx()) {
         if (this.vl < 0) {
            this.vl = 0;
         }

         Player var1;
         Player var10000 = var1 = this.Aa.getPlayer();

         try {
            var10000.hashCode();
         } catch (Exception var3) {
            return;
         }

         AliceViolationEvent var2 = new AliceViolationEvent(var1, this.checkName, this.sB(), this.dC, this.Zh, this.vl++, this.maxVl, this.experimental);
         Bukkit.getPluginManager().callEvent(var2);
         if (var2.isCancelled()) {
            this.vl--;
         } else if (this.experimental && !me.nik.alice.UN.dx.hW.dC()) {
            this.vl--;
         } else {
            if (this.Zh) {
               this.Aa.dx().dx().dx(true, false);
            }

            if (this.vl >= this.maxVl) {
               String var4 = var1.getName();
               Iterator var5 = this.dx.iterator();

               while (var5.hasNext()) {
                  gE.h5(((String)var5.next()).replace(Fz[16], var4));
               }

               this.vl = 0;
               this.og = 0.0F;
            }
         }
      }
   }

   public String getFullCheckName() {
      return this.fullCheckName;
   }

   public abstract String sB();

   public void WB() {
      this.vl = 0;
   }

   public void Aa(String var1) {
      this.Zh = var1;
      VL[] var6;
      VL[] var10000 = var6 = this.Aa.dx();

      try {
         var10000.equals(null);
      } catch (Exception var5) {
         return;
      }

      int var2 = var6.length;

      for (int var3 = 0; var3 < var2; var3++) {
         if (Integer.valueOf(this.checkName.hashCode()).equals(var6[var3].AC().hashCode())) {
            String var4 = this.Zh + (this.AC == 0.0F ? Fz[11] : Fz[12] + gE.dx((double)this.og, (double)this.AC) + Fz[13]);
            var4 = F.jD.getMessage().replace(Fz[14], this.getFullCheckName()).replace(Fz[15], var4);
            this.Aa.getPlayer().sendMessage(var4);
         }
      }
   }

   public int getVl() {
      return this.vl;
   }

   protected float dx(double var1) {
      return this.og = (float)FastMath.min(10000.0, (double)this.og + var1);
   }

   protected float AC() {
      return this.og;
   }

   protected float og() {
      return this.og = FastMath.max(0.0F, this.og - 1.0F);
   }
}
