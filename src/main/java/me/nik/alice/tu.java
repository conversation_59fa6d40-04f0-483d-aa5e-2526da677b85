package me.nik.alice;

import org.bukkit.event.EventHandler;
import org.bukkit.event.EventPriority;
import org.bukkit.event.Listener;
import org.bukkit.event.block.BlockBreakEvent;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.event.player.PlayerBucketEmptyEvent;

public class tu implements Listener {
   private final Alice plugin;

   @EventHandler(
      priority = EventPriority.HIGHEST
   )
   public void dx(PlayerBucketEmptyEvent var1) {
      if (var1.isCancelled()) {
         this.plugin.dx().dx(var1.getPlayer()).dx().dx().P();
      }
   }

   public tu(Alice var1) {
      this.plugin = var1;
   }

   @EventHandler(
      priority = EventPriority.HIGHEST
   )
   public void dx(BlockPlaceEvent var1) {
      if (var1.isCancelled()) {
         this.plugin.dx().dx(var1.getPlayer()).dx().dx().R();
      }
   }

   @EventHandler(
      priority = EventPriority.HIGHEST
   )
   public void dx(BlockBreakEvent var1) {
      if (var1.isCancelled()) {
         this.plugin.dx().dx(var1.getPlayer()).dx().dx().R();
      }
   }
}
