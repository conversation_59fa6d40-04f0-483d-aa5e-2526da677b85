package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class uD extends PacketCheck {
   private long yM;
   private static String[] r9;

   public uD(UC var1) {
      super(var1, CheckType.PACKET, r9[0], Category.WORLD, 3.0F);
   }

   private static void mH() {
      r9 = new String[]{"T", "Checks for attacks while digging", "Attack packet while digging"};
   }

   static {
      mH();
   }

   @Override
   public void dx(DH var1) {
      if (!var1.Tq()) {
         if (var1.uD()) {
            this.yM = var1.getTimeStamp();
         }
      } else {
         long var2 = l.og(this.yM);
         long var4;
         boolean var6 = (var4 = this.Aa.dx().Fh()) < 40L || var4 > 100L;
         if (var2 <= 1L && !var6) {
            this.Aa(r9[2]);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.25);
         }
      }
   }

   @Override
   public String sB() {
      return r9[1];
   }
}
