package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import org.bukkit.enchantments.Enchantment;

public class uX extends PacketCheck {
   private static String[] QX;

   public uX(UC var1) {
      super(var1, CheckType.PACKET, QX[0], Category.WORLD, 5.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && this.Aa.AC() >= 5000L
         && !this.Aa.getPlayer().getAllowFlight()
         && this.Aa.dx().gz() >= 60
         && (w.v() || JA.dx(this.Aa.getPlayer().getInventory().getBoots(), Enchantment.FROST_WALKER) <= 0)
         && this.Aa.dx().UH() >= 80
         && this.Aa.dx().Ch() >= 40) {
         ES var14 = this.Aa.dx();
         MN var2 = this.Aa.dx();
         boolean var3 = this.Aa.dx().Aa(4);
         double var5 = Math.abs(var14.F7());
         double var7 = Math.abs(var14.Ch());
         double var9 = (double)JA.dx(this.Aa) + var2.Zm() + 0.3;
         if (var3) {
            var9 += 0.35;
         }

         int var4;
         if ((var4 = var14.dx().sB(2000L)) > 0) {
            var9 += 0.05 * (double)var4;
         }

         double var12 = var7;
         int var15;
         if ((var15 = var14.dx().og(2000L)) > 0) {
            var12 = var7 + 0.11 * (double)var15;
         }

         var12 += 0.1;
         if (var3) {
            var9 += 0.1;
         }

         var12 += var2.jA();
         boolean var16 = (var5 > var9 || var7 > var12) && var2.o4();
         boolean var17 = var2.GE() == 1 && var16;
         if (var16) {
            this.Aa(QX[2] + var5 + QX[3] + var9 + QX[4] + var7 + QX[5] + var12);
            if (var17) {
               this.Aa();
            }

            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.0075);
         }
      }
   }

   static {
      iT();
   }

   private static void iT() {
      QX = new String[]{"P", "Checks if the player is trying to exploit teleports", "XZ: ", " max: ", " Y: ", " max: "};
   }

   @Override
   public String sB() {
      return QX[1];
   }
}
