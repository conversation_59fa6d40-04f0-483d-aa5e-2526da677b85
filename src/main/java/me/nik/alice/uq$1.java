package me.nik.alice;

import com.comphenix.protocol.wrappers.EnumWrappers.PlayerAction;
import com.comphenix.protocol.wrappers.EnumWrappers.PlayerDigType;

class uq$1 {
   static final int[] AC = new int[PlayerDigType.values().length];
   static final int[] og;

   static {
      try {
         AC[PlayerDigType.RELEASE_USE_ITEM.ordinal()] = 1;
      } catch (NoSuchFieldError var7) {
      }

      int[] var10000 = AC;

      try {
         var10000[PlayerDigType.START_DESTROY_BLOCK.ordinal()] = 2;
      } catch (NoSuchFieldError var6) {
      }

      try {
         AC[PlayerDigType.STOP_DESTROY_BLOCK.ordinal()] = 3;
      } catch (NoSuchFieldError var5) {
      }

      try {
         AC[PlayerDigType.ABORT_DESTROY_BLOCK.ordinal()] = 4;
      } catch (NoSuchFieldError var4) {
      }

      try {
         AC[PlayerDigType.DROP_ITEM.ordinal()] = 5;
      } catch (NoSuchFieldError var3) {
      }

      label94: {
         int var10001;
         byte var10002;
         try {
            var10000 = AC;
            var10001 = PlayerDigType.DROP_ALL_ITEMS.ordinal();
            var10002 = 6;
         } catch (NoSuchFieldError var11) {
            break label94;
         }

         var10000[var10001] = var10002;
      }

      og = new int[PlayerAction.values().length];

      label89: {
         int var17;
         try {
            var10000 = og;
            var17 = PlayerAction.START_SNEAKING.ordinal();
         } catch (NoSuchFieldError var10) {
            break label89;
         }

         var10000[var17] = 1;
      }

      var10000 = og;
      PlayerAction var18 = PlayerAction.STOP_SNEAKING;

      try {
         var10000[var18.ordinal()] = 2;
      } catch (NoSuchFieldError var2) {
      }

      try {
         og[PlayerAction.START_SPRINTING.ordinal()] = 3;
      } catch (NoSuchFieldError var1) {
      }

      label84: {
         try {
            var10000 = og;
            var19 = PlayerAction.STOP_SPRINTING.ordinal();
         } catch (NoSuchFieldError var9) {
            break label84;
         }

         var10000[var19] = 4;
      }

      label80: {
         try {
            var10000 = og;
            var20 = PlayerAction.START_FALL_FLYING.ordinal();
         } catch (NoSuchFieldError var8) {
            break label80;
         }

         var10000[var20] = 5;
      }

      try {
         og[PlayerAction.START_RIDING_JUMP.ordinal()] = 6;
      } catch (NoSuchFieldError var0) {
      }
   }
}
