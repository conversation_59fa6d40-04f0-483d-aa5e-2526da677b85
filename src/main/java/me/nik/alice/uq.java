package me.nik.alice;

import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.EnumWrappers.PlayerAction;
import java.util.ArrayList;
import java.util.List;
import me.nik.alice.uq.1;
import me.nik.alice.wrappers.WrapperPlayClientBlockDig;
import me.nik.alice.wrappers.WrapperPlayClientEntityAction;
import org.bukkit.block.Block;
import org.bukkit.enchantments.Enchantment;
import org.bukkit.entity.Player;
import org.bukkit.event.Event;
import org.bukkit.event.block.BlockPlaceEvent;
import org.bukkit.inventory.ItemStack;

public class uq {
   private int tV;
   private int GE;
   private int yk;
   private int Sh;
   private int R;
   private final UC Aa;
   private int tU;
   private long Eu;
   private int u;
   private boolean Ch;
   private boolean F7;
   private int tr;
   private List VL;
   private static String[] 2r;
   private int pO;
   private int M3;
   private int QJ;
   private int tm;
   private int P;
   private int R2;
   private int p6;
   private int pQ;
   private int zP;
   private int DP;
   private int o4;
   private int E;
   private boolean iv;
   private int nZ;
   private int Q;
   private Block Aa;
   private int xx;
   private final V0 h5 = new V0(20, true);
   private long nZ;

   public int yM() {
      return l.Aa(this.Sh);
   }

   public void o4() {
      this.pQ = Ku.iK();
   }

   public int Vm() {
      return this.tr;
   }

   public boolean Ch() {
      return this.Ch;
   }

   public int zP() {
      return l.Aa(this.R2);
   }

   public void Q() {
      this.E = Ku.iK();
   }

   public int iv() {
      return this.zP;
   }

   public int VL() {
      return this.GE;
   }

   public void QJ() {
      this.Sh = Ku.iK();
   }

   public boolean DB() {
      ES var1;
      if (!((var1 = this.Aa.dx()).Ch() > 0.7 + (double)var1.dx().og(1500L) * 0.11) && !(var1.F7() > (double)JA.dx(this.Aa) * 2.0)) {
         Sk var3 = this.Aa.dx();
         int var2 = l.Aa(this.Q);
         int var4 = 18 + l.dC((long)(var3.jA() + 5)) + (var3.Aa(3) ? 40 : 0);
         return var2 < var4;
      } else {
         return false;
      }
   }

   public int tm() {
      return l.Aa(this.pQ);
   }

   private static void eK() {
      2r = new String[]{"ELYTRA", "Timed out", "CHORUS"};
   }

   public void pO() {
      this.P = Ku.iK();
   }

   static {
      eK();
   }

   public boolean Zp() {
      return l.Aa(this.u) < 10;
   }

   public int hW() {
      return this.tm;
   }

   public uq(UC var1) {
      this.VL = new ArrayList();
      this.Aa = var1;
   }

   public int sX() {
      return this.QJ;
   }

   public void AC(Event var1) {
      boolean var4 = o8cT;
      int var2 = Ku.iK();
      if (var1 instanceof BlockPlaceEvent) {
         this.u = var2;
         BlockPlaceEvent var5;
         Block var3 = (var5 = (BlockPlaceEvent)var1).getBlockPlaced();
         this.Aa = var3;
         this.VL = fD.Aa(var5.getPlayer().getLocation());
         if (this.VL.contains(var3)) {
            this.Aa.dx().pQ();
            this.Q = var2;
         }

         int var10001;
         if (var5.getPlayer().getLocation().clone().subtract(0.0, 2.0, 0.0).getBlock().isEmpty() && this.VL.contains(var3)) {
            var10001 = 0;
            if (var4) {
               throw null;
            }
         } else {
            var10001 = this.GE + 1;
         }

         this.GE = var10001;
      }
   }

   public int h5() {
      return l.Aa(this.tV);
   }

   public boolean iv() {
      return this.iv;
   }

   public int gz() {
      return this.pO;
   }

   public long sB() {
      return this.nZ;
   }

   public Block Aa() {
      return this.Aa;
   }

   public boolean dx(int var1) {
      return this.tU <= var1;
   }

   public int F7() {
      return l.Aa(this.DP);
   }

   // $VF: Unable to simplify switch on enum
   // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
   public void AC(DH var1) {
      Player var2;
      Player var10000 = var2 = this.Aa.getPlayer();

      try {
         var10000.equals(null);
      } catch (Exception var11) {
         return;
      }

      PacketContainer var3 = var1.dx();
      long var4 = var1.getTimeStamp();
      int var6 = Ku.iK();
      ItemStack var7 = JA.dx(var2);
      if (var1.tV()) {
         WrapperPlayClientEntityAction var14;
         PlayerAction var15 = (var14 = new WrapperPlayClientEntityAction(var3)).getAction();

         try {
            var15.toString();
         } catch (Exception var10) {
            return;
         }

         switch (1.og[var14.getAction().ordinal()]) {
            case 1:
               this.iv = true;
               this.Ch = false;
               this.zP = 0;
               return;
            case 2:
               this.iv = false;
               this.zP = 0;
               return;
            case 3:
               this.Ch = true;
               this.iv = false;
               this.zP = 0;
               return;
            case 4:
               this.Ch = false;
               this.tm = 0;
               return;
            case 5:
               if (JA.dx(var2, 2r[0])) {
                  this.pO = 0;
                  return;
               }
               break;
            case 6:
               this.R2 = var6;
         }
      } else if (var1.M3()) {
         this.tm = this.Ch ? this.tm + 1 : 0;
         this.zP = this.iv ? this.zP + 1 : 0;
         this.M3 = var2.isSleeping() ? 0 : this.M3 + 1;
         this.QJ = !var2.isFlying() && !var2.getAllowFlight() ? this.QJ + 1 : 0;
         this.o4 = !w.v() && var2.isRiptiding() ? 0 : this.o4 + 1;
         this.tr = var2.isInsideVehicle() ? 0 : this.tr + 1;
         this.pO = w.Sx() && var2.isGliding() ? 0 : this.pO + 1;
         this.p6 = var2.isDead() && var2.getHealth() == 0.0 ? 0 : this.p6 + 1;
      } else if (var1.i2()) {
         this.tU = JA.dx(var2);
         if (this.F7 && this.dx(5)) {
            this.nZ = var6;
         }

         if (!this.Aa.dx().UH()) {
            long var13 = var4 - this.Eu;
            this.Eu = var4;
            if (var13 > 5L) {
               this.h5.add(var13);
               if (this.h5.h5()) {
                  this.nZ = (long)l.dx(this.h5);
               }
            }
         }
      } else if (var1.uD()) {
         WrapperPlayClientBlockDig var12 = new WrapperPlayClientBlockDig(var3);
         switch (1.AC[var12.getStatus().ordinal()]) {
            case 1:
               if (!w.v() && var7.containsEnchantment(Enchantment.RIPTIDE)) {
                  this.o4 = 0;
                  return;
               }
               break;
            case 2:
               this.F7 = true;
               return;
            case 3:
            case 4:
               this.nZ = var6;
               this.F7 = false;
               return;
            case 5:
            case 6:
               if (var7.getAmount() > 0) {
                  this.yk = var6;
               }
         }
      } else if (var1.qa()) {
         this.iv = false;
         this.Ch = false;
      } else if (var1.yk()) {
         this.tV = var6;
      } else {
         if (var1.d() || var1.CS()) {
            int var8 = l.Aa(this.R);
            this.R = var6;
            if (var8 == 0 && this.Aa.dx().Ch() > 0.1 && this.Aa.dx().Aa(4) && this.Aa.dx().M3() > 6) {
               this.Aa.sB(2r[1]);
            }

            if (var1.CS() && var7.getType().toString().contains(2r[2])) {
               this.xx = var6;
            }
         }
      }
   }

   public int UH() {
      return l.Aa(this.xx);
   }

   public boolean F7() {
      return l.Aa(this.nZ) < 10;
   }

   public void tr() {
      this.DP = Ku.iK();
   }

   public int Fh() {
      return this.M3;
   }

   public int Zp() {
      return l.Aa(this.P);
   }

   public int x() {
      return l.Aa(this.yk);
   }

   public int WB() {
      return this.o4;
   }

   public int Ch() {
      return this.p6;
   }

   public int b() {
      return l.Aa(this.E);
   }
}
