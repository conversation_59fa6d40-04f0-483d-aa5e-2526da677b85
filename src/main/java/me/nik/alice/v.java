package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class v extends PacketCheck {
   private static String[] AT;

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !(this.Aa.dx().dC() >= 25.0F) && !this.Aa.dx().tr() && !this.Aa.dx().dx().DP() && this.Aa.dx().GE() >= 20) {
         ES var5;
         double var3 = (var5 = this.Aa.dx()).Qt();
         int var6 = var5.a();
         if (var3 < 0.01 && var6 > 1) {
            this.Aa(AT[2] + var3 + AT[3] + var6);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.05);
         }
      }
   }

   private static void el() {
      AT = new String[]{"D", "Checks for very small acceleration", "Acceleration: ", " fly ticks: "};
   }

   @Override
   public String sB() {
      return AT[1];
   }

   public v(UC var1) {
      super(var1, CheckType.FLY, AT[0], Category.MOVE, 1.0F);
   }

   static {
      el();
   }
}
