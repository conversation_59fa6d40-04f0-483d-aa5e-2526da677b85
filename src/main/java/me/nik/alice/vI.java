package me.nik.alice;

import java.util.UUID;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.bukkit.Bukkit;
import org.bukkit.OfflinePlayer;
import org.bukkit.entity.Player;

public final class vI {
   private static final String bI = "https://api.mojang.com/users/profiles/minecraft/";
   private static String[] 5C;

   private static UUID og(String var0) {
      var0 = Zh(5C[0].concat(String.valueOf(var0)));
      StringBuilder var1 = new StringBuilder();
      dx(var0, var1);
      var0 = var1.toString();
      var1 = new StringBuilder();

      for (int var2 = 0; var2 <= 31; var2++) {
         var1.append(var0.charAt(var2));
         if (var2 == 7 || var2 == 11 || var2 == 15 || var2 == 19) {
            var1.append('-');
         }
      }

      return UUID.fromString(var1.toString());
   }

   public static UUID Aa(String var0) {
      Player var1;
      Player var10000 = var1 = Bukkit.getPlayer(var0);

      try {
         var10000.hashCode();
      } catch (Exception var2) {
         OfflinePlayer var3;
         if ((var3 = Bukkit.getOfflinePlayer(var0)).hasPlayedBefore()) {
            return var3.getUniqueId();
         }

         return null;
      }

      return var1.getUniqueId();
   }

   private static void vq() {
      5C = new String[]{"https://api.mojang.com/users/profiles/minecraft/"};
   }

   static {
      vq();
   }

   public static UUID dx(String var0) {
      CompletableFuture var2 = CompletableFuture.supplyAsync(vI::og);

      try {
         return (UUID)var2.get();
      } catch (ExecutionException | InterruptedException var1) {
         var1.printStackTrace();
         return null;
      }
   }

   private static void dx(String var0, StringBuilder var1) {
      for (int var2 = var0.length() - 3; var2 >= 0 && var0.charAt(var2) != '"'; var2--) {
         var1.insert(0, var0.charAt(var2));
      }
   }

   private vI() {
   }

   private static String Zh(String param0) {
      // $VF: Couldn't be decompiled
      // Please report this to the Vineflower issue tracker, at https://github.com/Vineflower/vineflower/issues with a copy of the class file (if you have the rights to distribute it!)
      // java.lang.RuntimeException: parsing failure!
      //   at org.jetbrains.java.decompiler.modules.decompiler.decompose.DomHelper.parseGraph(DomHelper.java:211)
      //   at org.jetbrains.java.decompiler.main.rels.MethodProcessor.codeToJava(MethodProcessor.java:166)
      //
      // Bytecode:
      // 00: new java/lang/StringBuilder
      // 03: dup
      // 04: invokespecial java/lang/StringBuilder.<init> ()V
      // 07: astore 1
      // 08: goto 0b
      // 0b: new java/net/URL
      // 0e: dup
      // 0f: aload 0
      // 10: invokespecial java/net/URL.<init> (Ljava/lang/String;)V
      // 13: dup
      // 14: goto 17
      // 17: pop
      // 18: goto 1b
      // 1b: invokevirtual java/net/URL.openConnection ()Ljava/net/URLConnection;
      // 1e: dup
      // 1f: astore 0
      // 20: goto 23
      // 23: invokevirtual java/lang/Object.getClass ()Ljava/lang/Class;
      // 26: pop
      // 27: goto 2a
      // 2a: goto 31
      // 2d: pop
      // 2e: goto 3d
      // 31: goto 34
      // 34: aload 0
      // 35: ldc 60000
      // 37: invokevirtual java/net/URLConnection.setReadTimeout (I)V
      // 3a: goto 3d
      // 3d: aload 0
      // 3e: goto 41
      // 41: invokevirtual java/lang/Object.hashCode ()I
      // 44: pop
      // 45: goto 4c
      // 48: pop
      // 49: goto bd
      // 4c: aload 0
      // 4d: invokevirtual java/net/URLConnection.getInputStream ()Ljava/io/InputStream;
      // 50: goto 53
      // 53: aconst_null
      // 54: invokevirtual java/lang/Object.equals (Ljava/lang/Object;)Z
      // 57: pop
      // 58: goto 5f
      // 5b: pop
      // 5c: goto bd
      // 5f: goto 62
      // 62: new java/io/InputStreamReader
      // 65: goto 68
      // 68: dup
      // 69: aload 0
      // 6a: goto 6d
      // 6d: invokevirtual java/net/URLConnection.getInputStream ()Ljava/io/InputStream;
      // 70: invokestatic java/nio/charset/Charset.defaultCharset ()Ljava/nio/charset/Charset;
      // 73: goto 76
      // 76: invokespecial java/io/InputStreamReader.<init> (Ljava/io/InputStream;Ljava/nio/charset/Charset;)V
      // 79: astore 0
      // 7a: goto 7d
      // 7d: new java/io/BufferedReader
      // 80: dup
      // 81: aload 0
      // 82: invokespecial java/io/BufferedReader.<init> (Ljava/io/Reader;)V
      // 85: astore 2
      // 86: goto 89
      // 89: goto 8c
      // 8c: aload 2
      // 8d: invokevirtual java/io/BufferedReader.read ()I
      // 90: dup
      // 91: goto 94
      // 94: istore 3
      // 95: bipush -1
      // 96: if_icmpeq ac
      // 99: goto 9c
      // 9c: aload 1
      // 9d: iload 3
      // 9e: i2c
      // 9f: goto a2
      // a2: invokevirtual java/lang/StringBuilder.append (C)Ljava/lang/StringBuilder;
      // a5: pop
      // a6: goto a9
      // a9: goto 8c
      // ac: aload 2
      // ad: goto b0
      // b0: invokevirtual java/io/BufferedReader.close ()V
      // b3: goto b6
      // b6: aload 0
      // b7: invokevirtual java/io/InputStreamReader.close ()V
      // ba: goto bd
      // bd: goto c3
      // c0: invokevirtual java/lang/Exception.printStackTrace ()V
      // c3: aload 1
      // c4: invokevirtual java/lang/StringBuilder.toString ()Ljava/lang/String;
      // c7: areturn
   }
}
