package me.nik.alice;

import org.bukkit.Location;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerTeleportEvent.TeleportCause;
import org.bukkit.scheduler.BukkitRunnable;

class vR$1 extends BukkitRunnable {
   final Location sB;
   final vR Aa;
   final boolean Q;
   final Player og;
   int count;

   public void run() {
      if (this.count++ <= 2 && (!this.Q || !vR.dx(this.Aa).dx().Aa(5))) {
         this.og.teleport(this.sB, TeleportCause.PLUGIN);
         vR.dx(this.Aa, Ku.iK());
      } else {
         this.cancel();
      }
   }

   vR$1(vR var1, boolean var2, Player var3, Location var4) {
      this.Aa = var1;
      this.Q = var2;
      this.og = var3;
      this.sB = var4;
      this.count = 0;
   }
}
