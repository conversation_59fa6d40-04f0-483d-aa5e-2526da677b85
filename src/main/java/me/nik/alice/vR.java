package me.nik.alice;

import me.nik.alice.vR.1;
import org.bukkit.Location;
import org.bukkit.block.BlockFace;
import org.bukkit.entity.Player;
import org.bukkit.event.player.PlayerTeleportEvent.TeleportCause;

public class vR {
   private int W;
   private final UC Aa;
   private final V0 UH = new V0(10, true);
   private int G;

   public void dC(Location var1) {
      if (l.Aa(this.W) >= 20 && this.uX() >= 40) {
         this.UH.add(var1);
         this.W = Ku.iK();
      }
   }

   public vR(UC var1) {
      this.Aa = var1;
   }

   private void E() {
      Location var1;
      double var2 = (var1 = this.Aa.dx().og().clone()).getY();
      int var4 = 0;

      while (var1.getBlock().getRelative(BlockFace.DOWN).isEmpty()) {
         var1.setY(var2 - 1.0);
         if (var4++ > 5) {
            break;
         }
      }

      wf.dx(this::Zh);
   }

   static int dx(vR var0, int var1) {
      return var0.G = var1;
   }

   public void dx(boolean var1, boolean var2) {
      if ((long)this.uX() >= 8L || var2) {
         if (this.UH.isEmpty()) {
            this.E();
         } else {
            Player var5;
            Player var10000 = var5 = this.Aa.getPlayer();

            try {
               var10000.toString();
            } catch (Exception var4) {
               return;
            }

            Location var3;
            if ((var3 = (Location)this.UH.getLast()).getWorld() == var5.getWorld()) {
               new 1(this, var1, var5, var3).runTaskTimer(Alice.dx(), 0L, 0L);
            }
         }
      }
   }

   private void Zh(Location var1) {
      this.Aa.getPlayer().teleport(var1, TeleportCause.PLUGIN);
      this.G = Ku.iK();
   }

   public int uX() {
      return l.Aa(this.G);
   }

   static UC dx(vR var0) {
      return var0.Aa;
   }
}
