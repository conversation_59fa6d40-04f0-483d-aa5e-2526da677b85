package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class vg extends PacketCheck {
   private static String[] jC;

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().Fh() && !this.Aa.dx().Qt() && this.Aa.dx().u() >= 2) {
         ES var9;
         double var3 = (var9 = this.Aa.dx()).Zp() * 0.91F;
         double var5 = (var9.F7() - var3) * 38.2;
         double var7 = 0.9932 + (var9.b().size() > 0 ? 5.0 : 0.0);
         Qk var10 = this.Aa.dx();
         var7 += var10.tr() ? Math.abs(var10.cO()) * 40.0 : 0.0;
         if (var5 >= var7) {
            this.Aa(jC[2].concat(String.valueOf(var5)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.75);
         }
      }
   }

   public vg(UC var1) {
      super(var1, CheckType.SPEED, jC[0], Category.MOVE, 3.0F);
   }

   @Override
   public String sB() {
      return jC[1];
   }

   static {
      O9();
   }

   private static void O9() {
      jC = new String[]{"Air", "Checks for irregular air friction", "Delta: "};
   }
}
