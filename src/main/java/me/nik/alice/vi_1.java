package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class vi extends PacketCheck {
   private static String[] cC;

   @Override
   public String sB() {
      return cC[1];
   }

   public vi(UC var1) {
      super(var1, CheckType.SPEED, cC[0], Category.MOVE, 1.0F);
   }

   private static void Et() {
      cC = new String[]{"Strafe", "Checks for strafe", "Absolute delta X: ", " Absolute delta Z: "};
   }

   static {
      Et();
   }

   @Override
   public void dx(DH var1) {
      boolean var26 = JNiu;
      if (var1.pQ()
         && this.Aa.dx().u() > 2
         && !this.Aa.dx().Fh()
         && this.Aa.dx().tV() >= 3
         && !this.Aa.dx().Qt()
         && this.Aa.dx().yk() >= 20
         && !this.Aa.dx().DB()
         && !this.Aa.dx().dx().DP()) {
         ES var27;
         double var3 = (var27 = this.Aa.dx()).tm();
         double var5 = var27.zP();
         double var7 = var27.x();
         double var9 = var27.hW();
         double var11 = var7 * 0.91F + 0.025999999F;
         double var13 = var9 * 0.91F + 0.025999999F;
         double var15 = l.dC(var3, var11);
         double var17 = l.dC(var5, var13);
         double var19 = 0.1;
         double var21 = 0.1;
         Qk var2;
         if ((var2 = this.Aa.dx()).tr()) {
            double var24 = Math.abs(var2.getVelocityY() / 1.5);
            var19 = 0.1 + Math.abs(var2.getVelocityX() * 3.25) + var24;
            var21 = 0.1 + Math.abs(var2.getVelocityZ() * 3.25) + var24;
         }

         if (this.Aa.dx().Qt() < 10) {
            var19 += 0.125;
            var21 += 0.125;
         }

         boolean var10000;
         if (var15 > var19) {
            var10000 = true;
            if (var26) {
               throw null;
            }
         } else {
            var10000 = false;
         }

         boolean var28 = var10000;
         if (var17 > var21) {
            var10000 = true;
            if (var26) {
               throw null;
            }
         } else {
            var10000 = false;
         }

         boolean var25 = var10000;
         if ((var28 || var25) && var27.F7() > 0.21) {
            var10000 = true;
            if (var26) {
               throw null;
            }
         } else {
            var10000 = false;
         }

         if (var10000) {
            this.Aa(cC[2] + var15 + cC[3] + var17);
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.125);
         }
      }
   }
}
