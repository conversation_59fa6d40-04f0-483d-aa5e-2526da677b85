package me.nik.alice;

import java.util.Queue;
import org.bukkit.scheduler.BukkitRunnable;

public class wU extends BukkitRunnable {
   private final Alice plugin;

   public wU(Alice var1) {
      this.plugin = var1;
   }

   public void run() {
      if (!this.plugin.dx().Vm() && dC.og()) {
         oG var1;
         if (!(var1 = this.plugin.dx()).Aa().isEmpty()) {
            this.plugin.dx().og(true);
            TQ var2 = var1.dx();
            Queue var6 = var1.Aa();
            int var3 = 0;

            for (hL var5 : var6) {
               var2.dx(var5);
               var6.remove(var5);
               if (var3++ >= 15) {
                  break;
               }
            }

            this.plugin.dx().bI();
            if (!var6.isEmpty()) {
               for (hL var8 : var6) {
                  this.plugin.dx().Aa(var8);
               }
            }

            this.plugin.dx().og(false);
         }
      }
   }
}
