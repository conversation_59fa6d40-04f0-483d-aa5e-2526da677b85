package me.nik.alice;

import org.bukkit.Material;

class wZ$1 {
   static final int[] Aa = new int[Material.values().length];

   static {
      label29: {
         int[] var10000;
         int var10001;
         try {
            var10000 = Aa;
            var10001 = Material.PAPER.ordinal();
         } catch (NoSuchFieldError var2) {
            break label29;
         }

         var10000[var10001] = 1;
      }

      label25: {
         int[] var3;
         int var4;
         byte var10002;
         try {
            var3 = Aa;
            var4 = Material.BARRIER.ordinal();
            var10002 = 2;
         } catch (NoSuchFieldError var1) {
            break label25;
         }

         var3[var4] = var10002;
      }

      try {
         Aa[Material.BOOK.ordinal()] = 3;
      } catch (NoSuchFieldError var0) {
      }
   }
}
