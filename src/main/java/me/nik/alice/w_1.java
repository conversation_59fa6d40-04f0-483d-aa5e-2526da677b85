package me.nik.alice;

import org.bukkit.Bukkit;

public final class w {
   private static final String Zm = Bukkit.getVersion();
   private static String[] GL;
   private static final boolean tr = !og(GL[8]);
   private static final boolean LEGACY;
   private static final boolean o4 = !(LEGACY = og(GL[0]) || og(GL[1]) || og(GL[2]) || og(GL[3]) || og(GL[4])) && !og(GL[5]) && !og(GL[6]) && !og(GL[7]);

   public static boolean N7() {
      return o4;
   }

   public static boolean og(String var0) {
      return Zm.contains(var0);
   }

   public static boolean Sx() {
      return tr;
   }

   private static void Q3() {
      GL = new String[]{"1.8", "1.9", "1.10", "1.11", "1.12", "1.13", "1.14", "1.15", "1.8"};
   }

   static {
      Q3();
   }

   private w() {
   }

   public static boolean v() {
      return LEGACY;
   }
}
