package me.nik.alice;

import org.bukkit.Bukkit;
import org.bukkit.scheduler.BukkitTask;

public final class wf {
   public static BukkitTask Aa(Runnable var0, long var1, long var3) {
      return Bukkit.getScheduler().runTaskTimerAsynchronously(Alice.dx(), var0, var1, var3);
   }

   public static BukkitTask Aa(Runnable var0, long var1) {
      return Bukkit.getScheduler().runTaskLaterAsynchronously(Alice.dx(), var0, var1);
   }

   private wf() {
   }

   public static BukkitTask Aa(Runnable var0) {
      return Bukkit.getScheduler().runTaskAsynchronously(Alice.dx(), var0);
   }

   public static BukkitTask dx(Runnable var0) {
      return Bukkit.getScheduler().runTask(Alice.dx(), var0);
   }

   public static BukkitTask dx(Runnable var0, long var1) {
      return Bukkit.getScheduler().runTaskLater(Alice.dx(), var0, var1);
   }

   public static BukkitTask dx(Runnable var0, long var1, long var3) {
      return Bukkit.getScheduler().runTaskTimer(Alice.dx(), var0, var1, var3);
   }
}
