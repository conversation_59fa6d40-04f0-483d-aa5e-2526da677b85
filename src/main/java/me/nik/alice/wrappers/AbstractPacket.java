package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.ProtocolLibrary;
import com.comphenix.protocol.events.PacketContainer;
import org.bukkit.entity.Player;

public abstract class AbstractPacket {
   protected PacketContainer handle;

   protected AbstractPacket(PacketContainer var1, PacketType var2) {
      if (var1 == null) {
         throw new IllegalArgumentException("Packet handle cannot be NULL.");
      } else {
         this.handle = var1;
      }
   }

   public PacketContainer getHandle() {
      return this.handle;
   }

   public void sendPacket(Player var1) {
      try {
         ProtocolLibrary.getProtocolManager().sendServerPacket(var1, this.getHandle());
      } catch (Exception var2) {
      }
   }

   public void broadcastPacket() {
      ProtocolLibrary.getProtocolManager().broadcastServerPacket(this.getHandle());
   }

   public void receivePacket(Player var1) {
      try {
         ProtocolLibrary.getProtocolManager().recieveClientPacket(var1, this.getHandle());
      } catch (Exception var2) {
      }
   }
}
