package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.BlockPosition;
import com.comphenix.protocol.wrappers.EnumWrappers.Direction;
import com.comphenix.protocol.wrappers.EnumWrappers.PlayerDigType;

public class WrapperPlayClientBlockDig extends AbstractPacket {
   public static final PacketType TYPE = Client.BLOCK_DIG;

   public WrapperPlayClientBlockDig() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientBlockDig(PacketContainer var1) {
      super(var1, TYPE);
   }

   public BlockPosition getLocation() {
      return (BlockPosition)this.handle.getBlockPositionModifier().read(0);
   }

   public void setLocation(BlockPosition var1) {
      this.handle.getBlockPositionModifier().write(0, var1);
   }

   public Direction getDirection() {
      return (Direction)this.handle.getDirections().read(0);
   }

   public void setDirection(Direction var1) {
      this.handle.getDirections().write(0, var1);
   }

   public PlayerDigType getStatus() {
      return (PlayerDigType)this.handle.getPlayerDigTypes().read(0);
   }

   public void setStatus(PlayerDigType var1) {
      this.handle.getPlayerDigTypes().write(0, var1);
   }
}
