package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayClientChat extends AbstractPacket {
   public static final PacketType TYPE = Client.CHAT;

   public WrapperPlayClientChat() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientChat(PacketContainer var1) {
      super(var1, TYPE);
   }

   public String getMessage() {
      return (String)this.handle.getStrings().read(0);
   }

   public void setMessage(String var1) {
      this.handle.getStrings().write(0, var1);
   }
}
