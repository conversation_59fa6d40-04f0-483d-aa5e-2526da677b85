package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.EnumWrappers.ClientCommand;

public class WrapperPlayClientClientCommand extends AbstractPacket {
   public static final PacketType TYPE = Client.CLIENT_COMMAND;

   public WrapperPlayClientClientCommand() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientClientCommand(PacketContainer var1) {
      super(var1, TYPE);
   }

   public ClientCommand getAction() {
      return (ClientCommand)this.handle.getClientCommands().read(0);
   }

   public void setAction(ClientCommand var1) {
      this.handle.getClientCommands().write(0, var1);
   }
}
