package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.events.PacketEvent;
import com.comphenix.protocol.wrappers.EnumWrappers.PlayerAction;
import org.bukkit.World;
import org.bukkit.entity.Entity;

public class WrapperPlayClientEntityAction extends AbstractPacket {
   public static final PacketType TYPE = Client.ENTITY_ACTION;

   public WrapperPlayClientEntityAction() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientEntityAction(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getEntityID() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setEntityID(int var1) {
      this.handle.getIntegers().write(0, var1);
   }

   public Entity getEntity(World var1) {
      return (Entity)this.handle.getEntityModifier(var1).read(0);
   }

   public Entity getEntity(PacketEvent var1) {
      return this.getEntity(var1.getPlayer().getWorld());
   }

   public PlayerAction getAction() {
      try {
         return (PlayerAction)this.handle.getPlayerActions().read(0);
      } catch (IllegalArgumentException var1) {
         return null;
      }
   }

   public void setAction(PlayerAction var1) {
      this.handle.getPlayerActions().write(0, var1);
   }

   public int getJumpBoost() {
      return (Integer)this.handle.getIntegers().read(1);
   }

   public void setJumpBoost(int var1) {
      this.handle.getIntegers().write(1, var1);
   }
}
