package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayClientHeldItemSlot extends AbstractPacket {
   public static final PacketType TYPE = Client.HELD_ITEM_SLOT;

   public WrapperPlayClientHeldItemSlot() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientHeldItemSlot(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getSlot() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setSlot(int var1) {
      this.handle.getIntegers().write(0, var1);
   }
}
