package me.nik.alice.wrappers;

import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayClientInstanceOfFlying extends AbstractPacket {
   public WrapperPlayClientInstanceOfFlying(PacketContainer var1) {
      super(var1, var1.getType());
   }

   public double getX() {
      return (Double)this.handle.getDoubles().readSafely(0);
   }

   public double getY() {
      return (Double)this.handle.getDoubles().readSafely(1);
   }

   public double getZ() {
      return (Double)this.handle.getDoubles().readSafely(2);
   }

   public float getYaw() {
      return (Float)this.handle.getFloat().readSafely(0);
   }

   public float getPitch() {
      return (Float)this.handle.getFloat().readSafely(1);
   }

   public boolean getOnGround() {
      return (Boolean)this.handle.getBooleans().readSafely(0);
   }
}
