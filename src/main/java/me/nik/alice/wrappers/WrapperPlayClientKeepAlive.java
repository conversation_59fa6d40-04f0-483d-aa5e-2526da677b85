package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;
import me.nik.alice.w;

public class WrapperPlayClientKeepAlive extends AbstractPacket {
   public static final PacketType TYPE = Client.KEEP_ALIVE;
   private static final boolean LEGACY = w.og("1.8") || w.og("1.9") || w.og("1.10") || w.og("1.11");

   public WrapperPlayClientKeepAlive() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientKeepAlive(PacketContainer var1) {
      super(var1, TYPE);
   }

   public long getKeepAliveId() {
      return LEGACY ? (long)((Integer)this.handle.getIntegers().read(0)).intValue() : (Long)this.handle.getLongs().read(0);
   }

   public void setKeepAliveId(long var1) {
      if (LEGACY) {
         this.handle.getIntegers().write(0, (int)var1);
      } else {
         this.handle.getLongs().write(0, var1);
      }
   }
}
