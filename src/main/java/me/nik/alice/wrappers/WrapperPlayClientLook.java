package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayClientLook extends AbstractPacket {
   public static final PacketType TYPE = Client.LOOK;

   public WrapperPlayClientLook() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientLook(PacketContainer var1) {
      super(var1, TYPE);
   }

   public float getYaw() {
      return (Float)this.handle.getFloat().read(0);
   }

   public void setYaw(float var1) {
      this.handle.getFloat().write(0, var1);
   }

   public float getPitch() {
      return (Float)this.handle.getFloat().read(1);
   }

   public void setPitch(float var1) {
      this.handle.getFloat().write(1, var1);
   }

   public boolean getOnGround() {
      return (<PERSON><PERSON><PERSON>)this.handle.getBooleans().read(0);
   }

   public void setOnGround(boolean var1) {
      this.handle.getBooleans().write(0, var1);
   }
}
