package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayClientPosition extends AbstractPacket {
   public static final PacketType TYPE = Client.POSITION;

   public WrapperPlayClientPosition() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientPosition(PacketContainer var1) {
      super(var1, TYPE);
   }

   public double getX() {
      return (Double)this.handle.getDoubles().read(0);
   }

   public void setX(double var1) {
      this.handle.getDoubles().write(0, var1);
   }

   public double getY() {
      return (Double)this.handle.getDoubles().read(1);
   }

   public void setY(double var1) {
      this.handle.getDoubles().write(1, var1);
   }

   public double getZ() {
      return (Double)this.handle.getDoubles().read(2);
   }

   public void setZ(double var1) {
      this.handle.getDoubles().write(2, var1);
   }

   public boolean getOnGround() {
      return (Boolean)this.handle.getBooleans().read(0);
   }

   public void setOnGround(boolean var1) {
      this.handle.getBooleans().write(0, var1);
   }
}
