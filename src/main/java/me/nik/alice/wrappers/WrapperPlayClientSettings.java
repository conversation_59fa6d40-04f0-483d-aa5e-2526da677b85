package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.EnumWrappers.ChatVisibility;

public class WrapperPlayClientSettings extends AbstractPacket {
   public static final PacketType TYPE = Client.SETTINGS;

   public WrapperPlayClientSettings() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientSettings(PacketContainer var1) {
      super(var1, TYPE);
   }

   public String getLocale() {
      return (String)this.handle.getStrings().read(0);
   }

   public void setLocale(String var1) {
      this.handle.getStrings().write(0, var1);
   }

   public int getViewDistance() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setViewDistance(byte var1) {
      this.handle.getIntegers().write(0, Integer.valueOf(var1));
   }

   public ChatVisibility getChatFlags() {
      return (ChatVisibility)this.handle.getChatVisibilities().read(0);
   }

   public void setChatFlags(ChatVisibility var1) {
      this.handle.getChatVisibilities().write(0, var1);
   }

   public boolean getChatColours() {
      return (Boolean)this.handle.getBooleans().read(0);
   }

   public void setChatColours(boolean var1) {
      this.handle.getBooleans().write(0, var1);
   }

   public int getDisplayedSkinParts() {
      return (Integer)this.handle.getIntegers().read(1);
   }

   public void setDisplayedSkinParts(int var1) {
      this.handle.getIntegers().write(1, var1);
   }
}
