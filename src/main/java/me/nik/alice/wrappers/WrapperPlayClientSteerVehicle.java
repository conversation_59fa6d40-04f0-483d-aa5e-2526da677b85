package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayClientSteerVehicle extends AbstractPacket {
   public static final PacketType TYPE = Client.STEER_VEHICLE;

   public WrapperPlayClientSteerVehicle() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientSteerVehicle(PacketContainer var1) {
      super(var1, TYPE);
   }

   public float getSideways() {
      return (Float)this.handle.getFloat().read(0);
   }

   public void setSideways(float var1) {
      this.handle.getFloat().write(0, var1);
   }

   public float getForward() {
      return (Float)this.handle.getFloat().read(1);
   }

   public void setForward(float var1) {
      this.handle.getFloat().write(1, var1);
   }

   public boolean isJump() {
      return (Boolean)this.handle.getBooleans().read(0);
   }

   public void setJump(boolean var1) {
      this.handle.getBooleans().write(0, var1);
   }

   public boolean isUnmount() {
      return (Boolean)this.handle.getBooleans().read(1);
   }

   public void setUnmount(boolean var1) {
      this.handle.getBooleans().write(1, var1);
   }
}
