package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayClientTransaction extends AbstractPacket {
   public static final PacketType TYPE = Client.TRANSACTION;

   public WrapperPlayClientTransaction() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientTransaction(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getWindowId() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setWindowId(byte var1) {
      this.handle.getIntegers().write(0, Integer.valueOf(var1));
   }

   public short getActionNumber() {
      return (Short)this.handle.getShorts().read(0);
   }

   public void setActionNumber(short var1) {
      this.handle.getShorts().write(0, var1);
   }

   public boolean getAccepted() {
      return (Boolean)this.handle.getBooleans().read(0);
   }

   public void setAccepted(boolean var1) {
      this.handle.getBooleans().write(0, var1);
   }
}
