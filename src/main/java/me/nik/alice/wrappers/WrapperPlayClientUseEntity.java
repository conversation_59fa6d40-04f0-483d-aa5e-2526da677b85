package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.events.PacketEvent;
import com.comphenix.protocol.wrappers.EnumWrappers.EntityUseAction;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.entity.EntityType;
import org.bukkit.util.Vector;

public class WrapperPlayClientUseEntity extends AbstractPacket {
   public static final PacketType TYPE = Client.USE_ENTITY;

   public WrapperPlayClientUseEntity() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientUseEntity(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getTargetID() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setTargetID(int var1) {
      this.handle.getIntegers().write(0, var1);
   }

   public boolean isTargetPlayer(World var1) {
      return ((Entity)this.handle.getEntityModifier(var1).read(0)).getType() == EntityType.PLAYER;
   }

   public boolean isNullTarget(World var1) {
      return this.handle.getEntityModifier(var1).read(0) == null;
   }

   public Entity getTarget(World var1) {
      return (Entity)this.handle.getEntityModifier(var1).read(0);
   }

   public Entity getTarget(PacketEvent var1) {
      return this.getTarget(var1.getPlayer().getWorld());
   }

   public EntityUseAction getType() {
      return (EntityUseAction)this.handle.getEntityUseActions().read(0);
   }

   public void setType(EntityUseAction var1) {
      this.handle.getEntityUseActions().write(0, var1);
   }

   public Vector getTargetVector() {
      return (Vector)this.handle.getVectors().read(0);
   }

   public void setTargetVector(Vector var1) {
      this.handle.getVectors().write(0, var1);
   }
}
