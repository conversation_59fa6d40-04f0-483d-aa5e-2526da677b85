package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayClientVehicleMove extends AbstractPacket {
   public static final PacketType TYPE = Client.VEHICLE_MOVE;

   public WrapperPlayClientVehicleMove() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientVehicleMove(PacketContainer var1) {
      super(var1, TYPE);
   }

   public double getX() {
      return (Double)this.handle.getDoubles().read(0);
   }

   public void setX(double var1) {
      this.handle.getDoubles().write(0, var1);
   }

   public double getY() {
      return (Double)this.handle.getDoubles().read(1);
   }

   public void setY(double var1) {
      this.handle.getDoubles().write(1, var1);
   }

   public double getZ() {
      return (Double)this.handle.getDoubles().read(2);
   }

   public void setZ(double var1) {
      this.handle.getDoubles().write(2, var1);
   }

   public float getYaw() {
      return (Float)this.handle.getFloat().read(0);
   }

   public void setYaw(float var1) {
      this.handle.getFloat().write(0, var1);
   }

   public float getPitch() {
      return (Float)this.handle.getFloat().read(1);
   }

   public void setPitch(float var1) {
      this.handle.getFloat().write(1, var1);
   }
}
