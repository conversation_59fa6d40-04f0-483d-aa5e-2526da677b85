package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Client;
import com.comphenix.protocol.events.PacketContainer;
import org.bukkit.inventory.ItemStack;

public class WrapperPlayClientWindowClick extends AbstractPacket {
   public static final PacketType TYPE = Client.WINDOW_CLICK;

   public WrapperPlayClientWindowClick() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayClientWindowClick(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getWindowId() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setWindowId(int var1) {
      this.handle.getIntegers().write(0, var1);
   }

   public int getSlot() {
      return (Integer)this.handle.getIntegers().read(1);
   }

   public void setSlot(int var1) {
      this.handle.getIntegers().write(1, var1);
   }

   public int getButton() {
      return (Integer)this.handle.getIntegers().read(2);
   }

   public void setButton(int var1) {
      this.handle.getIntegers().write(2, var1);
   }

   public short getActionNumber() {
      return (Short)this.handle.getShorts().read(0);
   }

   public void setActionNumber(short var1) {
      this.handle.getShorts().write(0, var1);
   }

   public ItemStack getClickedItem() {
      return (ItemStack)this.handle.getItemModifier().read(0);
   }

   public void setClickedItem(ItemStack var1) {
      this.handle.getItemModifier().write(0, var1);
   }

   public WrapperPlayClientWindowClick.InventoryClickType getShift() {
      return (WrapperPlayClientWindowClick.InventoryClickType)this.handle.getEnumModifier(WrapperPlayClientWindowClick.InventoryClickType.class, 5).read(0);
   }

   public void setShift(WrapperPlayClientWindowClick.InventoryClickType var1) {
      this.handle.getEnumModifier(WrapperPlayClientWindowClick.InventoryClickType.class, 5).write(0, var1);
   }

   public static enum InventoryClickType {
      PICKUP,
      QUICK_MOVE,
      SWAP,
      CLONE,
      THROW,
      QUICK_CRAFT,
      PICKUP_ALL;

      private static final WrapperPlayClientWindowClick.InventoryClickType[] $VALUES = new WrapperPlayClientWindowClick.InventoryClickType[]{
         PICKUP, QUICK_MOVE, SWAP, CLONE, THROW, QUICK_CRAFT, PICKUP_ALL
      };
   }
}
