package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.BlockPosition;
import com.comphenix.protocol.wrappers.WrappedBlockData;
import org.bukkit.Location;
import org.bukkit.World;

public class WrapperPlayServerBlockChange extends AbstractPacket {
   public static final PacketType TYPE = Server.BLOCK_CHANGE;

   public WrapperPlayServerBlockChange() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerBlockChange(PacketContainer var1) {
      super(var1, TYPE);
   }

   public BlockPosition getLocation() {
      return (BlockPosition)this.handle.getBlockPositionModifier().read(0);
   }

   public void setLocation(BlockPosition var1) {
      this.handle.getBlockPositionModifier().write(0, var1);
   }

   public Location getBukkitLocation(World var1) {
      return this.getLocation().toVector().toLocation(var1);
   }

   public WrappedBlockData getBlockData() {
      return (WrappedBlockData)this.handle.getBlockData().read(0);
   }

   public void setBlockData(WrappedBlockData var1) {
      this.handle.getBlockData().write(0, var1);
   }
}
