package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.WrappedChatComponent;
import com.comphenix.protocol.wrappers.EnumWrappers.ChatType;

public class WrapperPlayServerChat extends AbstractPacket {
   public static final PacketType TYPE = Server.CHAT;

   public WrapperPlayServerChat() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerChat(PacketContainer var1) {
      super(var1, TYPE);
   }

   public WrappedChatComponent getMessage() {
      return (WrappedChatComponent)this.handle.getChatComponents().read(0);
   }

   public void setMessage(WrappedChatComponent var1) {
      this.handle.getChatComponents().write(0, var1);
   }

   public ChatType getChatType() {
      return (ChatType)this.handle.getChatTypes().read(0);
   }

   public void setChatType(ChatType var1) {
      this.handle.getChatTypes().write(0, var1);
   }
}
