package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayServerEntityDestroy extends AbstractPacket {
   public static final PacketType TYPE = Server.ENTITY_DESTROY;

   public WrapperPlayServerEntityDestroy() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerEntityDestroy(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getCount() {
      return ((int[])this.handle.getIntegerArrays().read(0)).length;
   }

   public int[] getEntityIDs() {
      return (int[])this.handle.getIntegerArrays().read(0);
   }

   public void setEntityIds(int[] var1) {
      this.handle.getIntegerArrays().write(0, var1);
   }
}
