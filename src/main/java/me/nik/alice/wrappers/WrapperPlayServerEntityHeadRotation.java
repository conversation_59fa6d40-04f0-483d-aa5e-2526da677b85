package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.events.PacketEvent;
import org.bukkit.World;
import org.bukkit.entity.Entity;

public class WrapperPlayServerEntityHeadRotation extends AbstractPacket {
   public static final PacketType TYPE = Server.ENTITY_HEAD_ROTATION;

   public WrapperPlayServerEntityHeadRotation() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerEntityHeadRotation(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getEntityID() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setEntityID(int var1) {
      this.handle.getIntegers().write(0, var1);
   }

   public Entity getEntity(World var1) {
      return (Entity)this.handle.getEntityModifier(var1).read(0);
   }

   public Entity getEntity(PacketEvent var1) {
      return this.getEntity(var1.getPlayer().getWorld());
   }

   public byte getHeadYaw() {
      return (Byte)this.handle.getBytes().read(0);
   }

   public void setHeadYaw(float var1) {
      this.handle.getBytes().write(0, (byte)((int)(var1 * 256.0F / 360.0F)));
   }
}
