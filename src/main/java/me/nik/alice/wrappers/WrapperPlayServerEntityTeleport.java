package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.events.PacketEvent;
import com.comphenix.protocol.reflect.FieldAccessException;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.util.Vector;

public class WrapperPlayServerEntityTeleport extends AbstractPacket {
   public static final PacketType TYPE = Server.ENTITY_TELEPORT;

   public WrapperPlayServerEntityTeleport() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerEntityTeleport(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getEntityID() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setEntityID(int var1) {
      this.handle.getIntegers().write(0, var1);
   }

   public Entity getEntity(World var1) {
      return (Entity)this.handle.getEntityModifier(var1).read(0);
   }

   public Entity getEntity(PacketEvent var1) {
      return this.getEntity(var1.getPlayer().getWorld());
   }

   public void setPos(Vector var1) {
      try {
         this.handle.getDoubles().write(0, var1.getX());
         this.handle.getDoubles().write(1, var1.getY());
         this.handle.getDoubles().write(2, var1.getZ());
      } catch (FieldAccessException var2) {
         this.handle.getIntegers().write(1, (int)(var1.getX() * 32.0));
         this.handle.getIntegers().write(2, (int)(var1.getY() * 32.0));
         this.handle.getIntegers().write(3, (int)(var1.getZ() * 32.0));
      }
   }

   public float getYaw() {
      return (float)((Byte)this.handle.getBytes().read(0)).byteValue() * 360.0F / 256.0F;
   }

   public void setYaw(float var1) {
      this.handle.getBytes().write(0, (byte)((int)(var1 * 256.0F / 360.0F)));
   }

   public float getPitch() {
      return (float)((Byte)this.handle.getBytes().read(1)).byteValue() * 360.0F / 256.0F;
   }

   public void setPitch(float var1) {
      this.handle.getBytes().write(1, (byte)((int)(var1 * 256.0F / 360.0F)));
   }

   public boolean getOnGround() {
      return (Boolean)this.handle.getBooleans().read(0);
   }

   public void setOnGround(boolean var1) {
      this.handle.getBooleans().write(0, var1);
   }
}
