package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.events.PacketEvent;
import org.bukkit.World;
import org.bukkit.entity.Entity;

public class WrapperPlayServerEntityVelocity extends AbstractPacket {
   public static final PacketType TYPE = Server.ENTITY_VELOCITY;

   public WrapperPlayServerEntityVelocity() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerEntityVelocity(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getEntityID() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setEntityID(int var1) {
      this.handle.getIntegers().write(0, var1);
   }

   public Entity getEntity(World var1) {
      return (Entity)this.handle.getEntityModifier(var1).read(0);
   }

   public Entity getEntity(PacketEvent var1) {
      return this.getEntity(var1.getPlayer().getWorld());
   }

   public double getVelocityX() {
      return (double)((Integer)this.handle.getIntegers().read(1)).intValue() / 8000.0;
   }

   public void setVelocityX(double var1) {
      this.handle.getIntegers().write(1, (int)(var1 * 8000.0));
   }

   public double getVelocityY() {
      return (double)((Integer)this.handle.getIntegers().read(2)).intValue() / 8000.0;
   }

   public void setVelocityY(double var1) {
      this.handle.getIntegers().write(2, (int)(var1 * 8000.0));
   }

   public double getVelocityZ() {
      return (double)((Integer)this.handle.getIntegers().read(3)).intValue() / 8000.0;
   }

   public void setVelocityZ(double var1) {
      this.handle.getIntegers().write(3, (int)(var1 * 8000.0));
   }
}
