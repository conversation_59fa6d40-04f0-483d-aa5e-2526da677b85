package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.BlockPosition;
import java.util.List;

public class WrapperPlayServerExplosion extends AbstractPacket {
   public static final PacketType TYPE = Server.EXPLOSION;

   public WrapperPlayServerExplosion() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerExplosion(PacketContainer var1) {
      super(var1, TYPE);
   }

   public double getX() {
      return (Double)this.handle.getDoubles().read(0);
   }

   public void setX(double var1) {
      this.handle.getDoubles().write(0, var1);
   }

   public double getY() {
      return (Double)this.handle.getDoubles().read(1);
   }

   public void setY(double var1) {
      this.handle.getDoubles().write(1, var1);
   }

   public double getZ() {
      return (Double)this.handle.getDoubles().read(2);
   }

   public void setZ(double var1) {
      this.handle.getDoubles().write(2, var1);
   }

   public float getRadius() {
      return (Float)this.handle.getFloat().read(0);
   }

   public void setRadius(float var1) {
      this.handle.getFloat().write(0, var1);
   }

   public List<BlockPosition> getRecords() {
      return (List<BlockPosition>)this.handle.getBlockPositionCollectionModifier().read(0);
   }

   public void setRecords(List<BlockPosition> var1) {
      this.handle.getBlockPositionCollectionModifier().write(0, var1);
   }

   public float getPlayerVelocityX() {
      return (Float)this.handle.getFloat().read(1);
   }

   public void setPlayerVelocityX(float var1) {
      this.handle.getFloat().write(1, var1);
   }

   public float getPlayerVelocityY() {
      return (Float)this.handle.getFloat().read(2);
   }

   public void setPlayerVelocityY(float var1) {
      this.handle.getFloat().write(2, var1);
   }

   public float getPlayerVelocityZ() {
      return (Float)this.handle.getFloat().read(3);
   }

   public void setPlayerVelocityZ(float var1) {
      this.handle.getFloat().write(3, var1);
   }
}
