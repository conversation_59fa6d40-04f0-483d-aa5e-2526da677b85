package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;

public class WrapperPlayServerHeldItemSlot extends AbstractPacket {
   public static final PacketType TYPE = Server.HELD_ITEM_SLOT;

   public WrapperPlayServerHeldItemSlot() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerHeldItemSlot(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getSlot() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setSlot(int var1) {
      this.handle.getIntegers().write(0, var1);
   }
}
