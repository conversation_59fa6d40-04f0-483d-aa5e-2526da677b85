package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import me.nik.alice.w;

public class WrapperPlayServerKeepAlive extends AbstractPacket {
   public static final PacketType TYPE = Server.KEEP_ALIVE;

   public WrapperPlayServerKeepAlive() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerKeepAlive(PacketContainer var1) {
      super(var1, TYPE);
   }

   public long getKeepAliveId() {
      return w.v() ? (long)((Integer)this.handle.getIntegers().read(0)).intValue() : (Long)this.handle.getLongs().read(0);
   }

   public void setKeepAliveId(long var1) {
      if (w.v()) {
         this.handle.getIntegers().write(0, (int)var1);
      } else {
         this.handle.getLongs().write(0, var1);
      }
   }
}
