package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.WrappedChatComponent;

public class WrapperPlayServerKickDisconnect extends AbstractPacket {
   public static final PacketType TYPE = Server.KICK_DISCONNECT;

   public WrapperPlayServerKickDisconnect() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerKickDisconnect(PacketContainer var1) {
      super(var1, TYPE);
   }

   public WrappedChatComponent getReason() {
      return (WrappedChatComponent)this.handle.getChatComponents().read(0);
   }

   public void setReason(WrappedChatComponent var1) {
      this.handle.getChatComponents().write(0, var1);
   }
}
