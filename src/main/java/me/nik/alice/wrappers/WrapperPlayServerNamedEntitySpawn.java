package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.events.PacketEvent;
import com.comphenix.protocol.reflect.FieldAccessException;
import com.comphenix.protocol.wrappers.WrappedDataWatcher;
import java.util.UUID;
import org.bukkit.World;
import org.bukkit.entity.Entity;
import org.bukkit.util.Vector;

public class WrapperPlayServerNamedEntitySpawn extends AbstractPacket {
   public static final PacketType TYPE = Server.NAMED_ENTITY_SPAWN;

   public WrapperPlayServerNamedEntitySpawn() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerNamedEntitySpawn(PacketContainer var1) {
      super(var1, TYPE);
   }

   public int getEntityID() {
      return (Integer)this.handle.getIntegers().read(0);
   }

   public void setEntityID(int var1) {
      this.handle.getIntegers().write(0, var1);
   }

   public Entity getEntity(World var1) {
      return (Entity)this.handle.getEntityModifier(var1).read(0);
   }

   public Entity getEntity(PacketEvent var1) {
      return this.getEntity(var1.getPlayer().getWorld());
   }

   public UUID getPlayerUUID() {
      return (UUID)this.handle.getUUIDs().read(0);
   }

   public void setPlayerUUID(UUID var1) {
      this.handle.getUUIDs().write(0, var1);
   }

   public Vector getPosition() {
      return new Vector(this.getX(), this.getY(), this.getZ());
   }

   public void setPosition(Vector var1) {
      try {
         this.handle.getDoubles().write(0, var1.getX());
         this.handle.getDoubles().write(1, var1.getY());
         this.handle.getDoubles().write(2, var1.getZ());
      } catch (FieldAccessException var2) {
         this.handle.getIntegers().write(1, (int)(var1.getX() * 32.0));
         this.handle.getIntegers().write(2, (int)(var1.getY() * 32.0));
         this.handle.getIntegers().write(3, (int)(var1.getZ() * 32.0));
      }
   }

   public double getX() {
      try {
         return (Double)this.handle.getDoubles().read(0);
      } catch (FieldAccessException var1) {
         return (double)((Integer)this.handle.getIntegers().read(0)).intValue();
      }
   }

   public void setX(double var1) {
      try {
         this.handle.getDoubles().write(0, var1);
      } catch (FieldAccessException var3) {
         this.handle.getIntegers().write(0, (int)(var1 * 32.0));
      }
   }

   public double getY() {
      try {
         return (Double)this.handle.getDoubles().read(1);
      } catch (FieldAccessException var1) {
         return (double)((Integer)this.handle.getIntegers().read(1)).intValue();
      }
   }

   public void setY(double var1) {
      try {
         this.handle.getDoubles().write(1, var1);
      } catch (FieldAccessException var3) {
         this.handle.getIntegers().write(1, (int)(var1 * 32.0));
      }
   }

   public double getZ() {
      try {
         return (Double)this.handle.getDoubles().read(2);
      } catch (FieldAccessException var1) {
         return (double)((Integer)this.handle.getIntegers().read(2)).intValue();
      }
   }

   public void setZ(double var1) {
      try {
         this.handle.getDoubles().write(2, var1);
      } catch (FieldAccessException var3) {
         this.handle.getIntegers().write(2, (int)(var1 * 32.0));
      }
   }

   public float getYaw() {
      return (float)((Byte)this.handle.getBytes().read(0)).byteValue() * 360.0F / 256.0F;
   }

   public void setYaw(float var1) {
      this.handle.getBytes().write(0, (byte)((int)(var1 * 256.0F / 360.0F)));
   }

   public float getPitch() {
      return (float)((Byte)this.handle.getBytes().read(1)).byteValue() * 360.0F / 256.0F;
   }

   public void setPitch(float var1) {
      this.handle.getBytes().write(1, (byte)((int)(var1 * 256.0F / 360.0F)));
   }

   public WrappedDataWatcher getMetadata() {
      return (WrappedDataWatcher)this.handle.getDataWatcherModifier().read(0);
   }

   public void setMetadata(WrappedDataWatcher var1) {
      this.handle.getDataWatcherModifier().write(0, var1);
   }

   private int floor(double var1) {
      int var3 = (int)var1;
      return var1 < (double)var3 ? var3 - 1 : var3;
   }
}
