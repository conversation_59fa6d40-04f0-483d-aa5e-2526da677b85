package me.nik.alice.wrappers;

import com.comphenix.protocol.PacketType;
import com.comphenix.protocol.PacketType.Play.Server;
import com.comphenix.protocol.events.PacketContainer;
import com.comphenix.protocol.wrappers.PlayerInfoData;
import com.comphenix.protocol.wrappers.EnumWrappers.PlayerInfoAction;
import java.util.List;

public class WrapperPlayServerPlayerInfo extends AbstractPacket {
   public static final PacketType TYPE = Server.PLAYER_INFO;

   public WrapperPlayServerPlayerInfo() {
      super(new PacketContainer(TYPE), TYPE);
      this.handle.getModifier().writeDefaults();
   }

   public WrapperPlayServerPlayerInfo(PacketContainer var1) {
      super(var1, TYPE);
   }

   public PlayerInfoAction getAction() {
      return (PlayerInfoAction)this.handle.getPlayerInfoAction().read(0);
   }

   public void setAction(PlayerInfoAction var1) {
      this.handle.getPlayerInfoAction().write(0, var1);
   }

   public List<PlayerInfoData> getData() {
      return (List<PlayerInfoData>)this.handle.getPlayerInfoDataLists().read(0);
   }

   public void setData(List<PlayerInfoData> var1) {
      this.handle.getPlayerInfoDataLists().write(0, var1);
   }
}
