package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import org.bukkit.entity.Player;

public class xB extends PacketCheck {
   private static String[] 5v;
   private int Vm;

   private static void Bw() {
      5v = new String[]{"C", "Checks for invalid sprint deceleration", "Acceleration: "};
   }

   @Override
   public String sB() {
      return 5v[1];
   }

   @Override
   public void dx(DH var1) {
      if (!var1.pQ()) {
         if (var1.Tq()) {
            if (!(this.Aa.dx().Aa() instanceof Player)) {
               return;
            }

            this.Vm = 0;
         }
      } else if (this.Aa.dx().Ch() && this.Vm++ <= 2) {
         ES var7;
         double var3 = (var7 = this.Aa.dx()).DB();
         double var5 = (double)JA.Aa(this.Aa) - 0.03;
         if (var3 < 0.0025 && var7.F7() > var5) {
            this.Aa(5v[2].concat(String.valueOf(var3)));
            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa((double)(this.AC() / 2.0F));
         }
      }
   }

   static {
      Bw();
   }

   public xB(UC var1) {
      super(var1, CheckType.KILLAURA, 5v[0], Category.COMBAT, 5.0F);
   }
}
