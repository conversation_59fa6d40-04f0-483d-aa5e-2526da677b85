package me.nik.alice;

public class xT {
   private int FH;
   private boolean u;
   private final UC Aa;
   private int KJ;

   public void yk() {
      if (me.nik.alice.UN.dx.E.dC()) {
         int var1 = Ku.iK();
         qn var2;
         if ((var2 = this.Aa.dx()).UH() != 0.0F && var2.b() != 0.0F) {
            float var3 = var2.sX();
            float var4 = var2.Vm();
            boolean var5 = l.yM(var3) || var3 == 0.0F || l.yM(var4) || var4 == 0.0F;
            ZQ var11;
            double var7 = (var11 = var2.dx()).nZ();
            double var9 = var11.Eu();
            if (!var5 && var3 < 1.0F && var4 < 1.0F) {
               if (var7 < 0.003 && var9 < 0.003) {
                  this.FH++;
               }
            } else {
               this.FH = this.FH - (this.FH > 0 ? 1 : 0);
            }

            this.FH = this.FH - (this.FH > 5 ? 1 : 0);
            this.u = this.FH > 2 || this.hB() < 40;
            if (this.u && this.FH > 2) {
               this.KJ = var1;
            }
         }
      }
   }

   public boolean pO() {
      return me.nik.alice.UN.dx.E.dC() && this.u;
   }

   public xT(UC var1) {
      this.Aa = var1;
   }

   public int hB() {
      return l.Aa(this.KJ);
   }
}
