package me.nik.alice;

import java.util.Iterator;
import java.util.List;

import me.nik.alice.checks.Check;
import org.bukkit.scheduler.BukkitRunnable;

public class xo extends BukkitRunnable {
   private final Alice plugin;

   public void run() {
      boolean var3 = Buix;
      Iterator var1 = this.plugin.dx().dx().values().iterator();

      while (var1.hasNext()) {
         List var2;
         if (!(var2 = ((UC)var1.next()).dx().dC()).isEmpty()) {
            var2.forEach(Check::WB);
            if (var3) {
               throw null;
            }
         }
      }
   }

   public xo(Alice var1) {
      this.plugin = var1;
   }
}
