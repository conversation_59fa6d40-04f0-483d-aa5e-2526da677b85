package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;
import me.nik.alice.wrappers.WrapperPlayClientInstanceOfFlying;

public class xx extends PacketCheck {
   private static String[] Xk;

   private static void cY() {
      Xk = new String[]{"H", "Checks impossible ground status", "C: ", " S: ", " Air ticks: "};
   }

   public xx(UC var1) {
      super(var1, CheckType.PACKET, Xk[0], Category.WORLD, 5.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.M3()
         && !this.Aa.dx().Fh()
         && this.Aa.dx().R2() >= 20
         && this.Aa.dx().cO() >= 20
         && !this.Aa.dx().jA()
         && this.Aa.dx().yV() >= 20
         && !this.Aa.dx().dx().DP()
         && !this.Aa.dx().tr()
         && this.Aa.dx().pQ() >= 40
         && this.Aa.dx().GE() >= 20
         && this.Aa.dx().nZ() >= 80
         && this.Aa.dx().yk() >= 10
         && !(this.Aa.dx().Aa().getY() < 4.0)) {
         WrapperPlayClientInstanceOfFlying var6;
         boolean var2 = (var6 = new WrapperPlayClientInstanceOfFlying(var1.dx())).getOnGround();
         double var4;
         if ((var4 = var6.getY()) != 0.0) {
            boolean var7 = var4 % 0.015625 < 1.0E-4;
            int var3 = this.Aa.dx().M3();
            if ((var2 || var7) && var3 > 8) {
               this.Aa(Xk[2] + var2 + Xk[3] + var7 + Xk[4] + var3);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.05);
            }
         }
      }
   }

   static {
      cY();
   }

   @Override
   public String sB() {
      return Xk[1];
   }
}
