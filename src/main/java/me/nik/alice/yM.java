package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import org.bukkit.event.Event;

public class yM extends EventCheck {
   private int Fh;
   private static final double og = me.nik.alice.Ww.dx.tu.dx();
   private final int h5 = me.nik.alice.Ww.dx.O8.Aa();
   private static String[] Mi;

   @Override
   public String sB() {
      return Mi[0];
   }

   public yM(UC var1) {
      super(var1, CheckType.ESP, Category.WORLD);
   }

   static {
      ho();
   }

   @Override
   public void on(Event var1) {
   }

   private static void ho() {
      Mi = new String[]{"Checks and prevents the usage of player esp"};
   }
}
