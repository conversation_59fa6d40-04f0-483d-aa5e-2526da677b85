package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class y extends PacketCheck {
   private static String[] E5;

   private static void Qm() {
      E5 = new String[]{"B", "Checks for invalid elytra accelerations", "AccelXZ: ", " AccelY: "};
   }

   public y(UC var1) {
      super(var1, CheckType.ELYTRA, E5[0], Category.MOVE, 5.0F);
   }

   static {
      Qm();
   }

   @Override
   public String sB() {
      return E5[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ() && !this.Aa.dx().sX()) {
         ES var7;
         double var3 = (var7 = this.Aa.dx()).DB();
         double var5 = var7.Qt();
         if (var3 <= 0.0 && var5 <= 0.0) {
            this.Aa(E5[2] + var3 + E5[3] + var5);
            if (this.AC() > this.dx() / 2.0F && me.nik.alice.Ww.dx.ZU.dC()) {
               this.Aa.getPlayer().setGliding(false);
            }

            if (this.Aa() > this.dx()) {
               this.sX();
               return;
            }
         } else {
            this.Aa(0.075);
         }
      }
   }
}
