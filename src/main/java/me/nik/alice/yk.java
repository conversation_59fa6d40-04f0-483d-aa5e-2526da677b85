package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class yk extends PacketCheck {
   private static String[] Ox;

   static {
      af();
   }

   @Override
   public String sB() {
      return Ox[1];
   }

   public yk(UC var1) {
      super(var1, CheckType.PACKET, Ox[0], Category.WORLD, 5.0F);
   }

   @Override
   public void dx(DH var1) {
      if (var1.K()) {
         ES var10 = this.Aa.dx();
         uq var2;
         if ((var2 = this.Aa.dx()).Vm() >= 60
            && !this.Aa.dx().o4()
            && var10.Sh() >= 60
            && var10.P() >= 60
            && var10.p6() >= 60
            && var10.pQ() >= 60
            && var2.sX() >= 60
            && var2.gz() >= 60
            && var10.nZ() >= 60
            && var2.Zp() >= 60
            && var2.WB() >= 60
            && !var10.jA()
            && var10.Eu() >= 60
            && !var10.rc()
            && !this.Aa.dx().tr()) {
            double var4 = (double)JA.Aa(this.Aa) - 0.108;
            double var6 = (double)JA.dx(this.Aa);
            double var8 = var10.F7();
            int var3 = var10.u();
            int var11 = var10.tk();
            int var12 = var2.hW();
            if (var11 > 5 && var8 > var4 || var3 > 0 && var8 > var6 || this.Aa.dx().Qt() == 0 || var12 > 5) {
               this.Aa(Ox[2] + var8 + Ox[3] + var12);
               if (this.Aa() > this.dx()) {
                  this.sX();
                  return;
               }
            } else {
               this.Aa(0.75);
            }
         }
      }
   }

   private static void af() {
      Ox = new String[]{"E", "Checks for invalid client settings packets", "Delta XZ: ", " sprinting ticks: "};
   }
}
