package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.EventCheck;
import org.bukkit.block.Block;
import org.bukkit.event.Event;
import org.bukkit.event.block.BlockPlaceEvent;

public class yx extends EventCheck {
   private int gz;
   private int tm;
   private static String[] rC;

   @Override
   public void on(Event var1) {
      if (var1 instanceof BlockPlaceEvent) {
         BlockPlaceEvent var9;
         if (!(var9 = (BlockPlaceEvent)var1).getPlayer().getAllowFlight()) {
            Block var2;
            if ((var2 = var9.getBlockPlaced()).getType().isSolid()) {
               int var10 = var2.getY();
               int var3 = this.gz;
               this.gz = var10;
               if (var10 == var3) {
                  ES var11 = this.Aa.dx();
                  this.tm = var9.getPlayer().isSprinting() ? this.tm + 1 : 0;
                  double var7 = var11.F7();
                  if (this.Aa.dx().VL() < 2 && this.tm > 5 && var7 >= 0.1) {
                     this.Aa(rC[1] + this.tm + rC[2] + var7);
                     if (this.Aa() > this.on()) {
                        this.sX();
                        return;
                     }
                  } else {
                     this.Aa(0.75);
                  }
               }
            }
         }
      }
   }

   static {
      vr();
   }

   private static void vr() {
      rC = new String[]{"D", "Sprinting ticks: ", " delta XZ: ", "Checks for scaffold while sprinting"};
   }

   @Override
   public String sB() {
      return rC[3];
   }

   public yx(UC var1) {
      super(var1, CheckType.SCAFFOLD, rC[0], Category.WORLD, 5.0F);
   }
}
