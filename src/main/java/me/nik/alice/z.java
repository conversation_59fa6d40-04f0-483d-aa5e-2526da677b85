package me.nik.alice;

import me.nik.alice.checks.Category;
import me.nik.alice.checks.CheckType;
import me.nik.alice.checks.PacketCheck;

public class z extends PacketCheck {
   private static String[] x0;

   private static void kk() {
      x0 = new String[]{"F", "Checks for low hop", "LowHop, delta Y: ", " last delta Y: ", " expected: 0.41999998688697815"};
   }

   static {
      kk();
   }

   @Override
   public String sB() {
      return x0[1];
   }

   @Override
   public void dx(DH var1) {
      if (var1.pQ()
         && !this.Aa.dx().Fh()
         && this.Aa.dx().cO() >= 20
         && !this.Aa.dx().tr()
         && !this.Aa.dx().Q()
         && this.Aa.dx().R2() >= 20
         && this.Aa.dx().yk() >= 20) {
         ES var7;
         double var3 = (var7 = this.Aa.dx()).Ch();
         double var5 = var7.iv();
         if (var7.bI() && var3 < 0.42F) {
            this.og(x0[2] + var3 + x0[3] + var5 + x0[4]);
         }
      }
   }

   public z(UC var1) {
      super(var1, CheckType.MOTION, x0[0], Category.MOVE);
   }
}
