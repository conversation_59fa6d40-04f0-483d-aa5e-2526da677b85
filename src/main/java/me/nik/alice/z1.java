package me.nik.alice;

import java.io.BufferedReader;
import java.io.IOException;
import java.io.InputStreamReader;
import java.net.URL;
import java.net.URLConnection;
import org.bukkit.event.EventHandler;
import org.bukkit.event.Listener;
import org.bukkit.event.player.PlayerJoinEvent;
import org.bukkit.scheduler.BukkitRunnable;

public class z1 extends BukkitRunnable implements Listener {
   private static String[] ro;
   private final Alice plugin;
   private String iv;

   private static void Fi() {
      ro = new String[]{
         "https://raw.githubusercontent.com/NikV2/AliceAPI/master/version.txt",
         "User-Agent",
         "Mozilla/4.0",
         "Couldn't check for updates, Is the server connected to the Internet?",
         "%current%",
         "%new%",
         "You're running the Latest Version of Alice !",
         "%current%",
         "%new%"
      };
   }

   @EventHandler
   public void dx(PlayerJoinEvent var1) {
      if (var1.getPlayer().hasPermission(ZM.dx.h5())) {
         var1.getPlayer().sendMessage(F.dC.getMessage().replace(ro[7], this.plugin.getDescription().getVersion()).replace(ro[8], this.iv));
      }
   }

   static {
      Fi();
   }

   // $VF: Inserted dummy exception handlers to handle obfuscated exceptions
   public void run() {
      URL var10000 = new URL;

      label53: {
         label57: {
            URLConnection var12;
            try {
               var10000.<init>(ro[0]);
               var8 = var10000.openConnection();
               var12 = var8;
            } catch (IOException var6) {
               boolean var10001 = false;
               break label57;
            }

            URLConnection var1 = var12;
            String var13 = ro[1];

            try {
               var8.addRequestProperty(var13, ro[2]);
            } catch (IOException var5) {
               boolean var14 = false;
               break label57;
            }

            BufferedReader var9 = new BufferedReader;
            BufferedReader var15 = var9;
            InputStreamReader var10002 = new InputStreamReader;
            InputStreamReader var10003 = var10002;
            URLConnection var10004 = var1;

            try {
               var10003.<init>(var10004.getInputStream());
               var15.<init>(var10002);
            } catch (IOException var4) {
               boolean var16 = false;
               break label57;
            }

            BufferedReader var7 = var9;
            z1 var10 = this;
            BufferedReader var17 = var7;

            try {
               var10.iv = var17.readLine();
            } catch (IOException var3) {
               boolean var18 = false;
               break label57;
            }

            BufferedReader var11 = var7;

            try {
               var11.close();
               break label53;
            } catch (IOException var2) {
               boolean var19 = false;
            }
         }

         this.plugin.getLogger().warning(ro[3]);
         return;
      }

      if (!Integer.valueOf(this.iv.hashCode()).equals(this.plugin.getDescription().getVersion().hashCode())) {
         this.plugin
            .getServer()
            .getConsoleSender()
            .sendMessage(F.dC.getMessage().replace(ro[4], this.plugin.getDescription().getVersion()).replace(ro[5], this.iv));
         this.plugin.getServer().getPluginManager().registerEvents(this, this.plugin);
      } else {
         this.plugin.getLogger().info(ro[6]);
      }
   }

   public z1(Alice var1) {
      this.plugin = var1;
   }
}
