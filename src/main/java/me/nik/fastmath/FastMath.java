package me.nik.fastmath;

public final class FastMath {
   public static String c46116ea2c611ebbcbc0242ac130002 = "20488";
   public static final double PI = Math.PI;
   public static final double E = Math.E;
   static final int EXP_INT_TABLE_MAX_INDEX = 750;
   static final int EXP_INT_TABLE_LEN = 1500;
   static final int LN_MANT_LEN = 1024;
   static final int EXP_FRAC_TABLE_LEN = 1025;
   private static final double LOG_MAX_VALUE = StrictMath.log(Double.MAX_VALUE);
   private static final boolean RECOMPUTE_TABLES_AT_RUNTIME = false;
   private static final double LN_2_A = 0.69314706F;
   private static final double LN_2_B = 1.1730463525082348E-7;
   private static final double[][] LN_QUICK_COEF = new double[][]{
      {1.0, 5.669184079525E-24},
      {-0.25, -0.25},
      {0.3333333F, 1.986821492305628E-8},
      {-0.25, -6.663542893624021E-14},
      {0.19999999F, 1.1921056801463227E-8},
      {-0.16666666F, -7.800414592973399E-9},
      {0.14285713F, 5.650007086920087E-9},
      {-0.1250253F, -7.44321345601866E-11},
      {0.111138076F, 9.219544613762692E-9}
   };
   private static final double[][] LN_HI_PREC_COEF = new double[][]{
      {1.0, -6.032174644509064E-23},
      {-0.25, -0.25},
      {0.3333333F, 1.9868161777724352E-8},
      {-0.24999997F, -2.957007209750105E-8},
      {0.19999954F, 1.5830993332061267E-10},
      {-0.1662488F, -2.6033824355191673E-8}
   };
   private static final int SINE_TABLE_LEN = 14;
   private static final double[] SINE_TABLE_A = new double[]{
      0.0,
      0.12467474F,
      0.24740395F,
      0.3662725F,
      0.47942555F,
      0.5850973F,
      0.6816387F,
      0.76754355F,
      0.84147096F,
      0.9022676F,
      0.9489846F,
      0.980893F,
      0.99749494F,
      0.99853134F
   };
   private static final double[] SINE_TABLE_B = new double[]{
      0.0,
      -4.068233003401932E-9,
      9.755392680573412E-9,
      1.9987994582857286E-8,
      -1.0902938113007961E-8,
      -3.9986783938944604E-8,
      4.23719669792332E-8,
      -5.207000323380292E-8,
      2.800552834259E-8,
      1.883511811213715E-8,
      -3.5997360512765566E-9,
      4.116164446561962E-8,
      5.0614674548127384E-8,
      -1.0129027912496858E-9
   };
   private static final double[] COSINE_TABLE_A = new double[]{
      1.0,
      0.99219763F,
      0.96891236F,
      0.93050766F,
      0.87758255F,
      0.81096315F,
      0.73168886F,
      0.6409968F,
      0.5403023F,
      0.43117654F,
      0.31532234F,
      0.19454771F,
      0.0707372F,
      -0.054177135F
   };
   private static final double[] COSINE_TABLE_B = new double[]{
      0.0,
      3.4439717236742845E-8,
      5.865827662008209E-8,
      -3.7999795083850525E-8,
      1.184154459111628E-8,
      -3.43338934259355E-8,
      1.1795268640216787E-8,
      4.438921624363781E-8,
      2.925681159240093E-8,
      -2.6437112632041807E-8,
      2.2860509143963117E-8,
      -4.813899778443457E-9,
      3.6725170580355583E-9,
      2.0217439756338078E-10
   };
   private static final double[] TANGENT_TABLE_A = new double[]{
      0.0,
      0.12565514F,
      0.25534195F,
      0.39362657F,
      0.54630244F,
      0.7214844F,
      0.9315965F,
      1.1974216F,
      1.5574076F,
      2.0925713F,
      3.0095696F,
      5.041915F,
      14.101419F,
      -18.430862F
   };
   private static final double[] TANGENT_TABLE_B = new double[]{
      0.0,
      -7.877917738262007E-9,
      -2.5857668567479893E-8,
      5.2240336371356666E-9,
      5.206150291559893E-8,
      1.8307188599677033E-8,
      -5.7618793749770706E-8,
      7.848361555046424E-8,
      1.0708593250394448E-7,
      1.7827257129423813E-8,
      2.893485277253286E-8,
      3.1660099222737955E-7,
      4.983191803254889E-7,
      -3.356118100840571E-7
   };
   private static final long[] RECIP_2PI = new long[]{
      2935890503282001226L,
      9154082963658192752L,
      3952090531849364496L,
      9193070505571053912L,
      7910884519577875640L,
      113236205062349959L,
      4577762542105553359L,
      -5034868814120038111L,
      4208363204685324176L,
      5648769086999809661L,
      2819561105158720014L,
      -4035746434778044925L,
      -302932621132653753L,
      -2644281811660520851L,
      -3183605296591799669L,
      6722166367014452318L,
      -3512299194304650054L,
      -7278142539171889152L
   };
   private static final long[] PI_O_4_BITS = new long[]{-3958705157555305932L, -4267615245585081135L};
   private static final double[] EIGHTHS = new double[]{0.0, 0.125, 0.25, 0.375, 0.5, 0.625, 0.75, 0.875, 1.0, 1.125, 1.25, 1.375, 1.5, 1.625};
   private static final double[] CBRTTWO = new double[]{0.6299605249474366, 0.7937005259840998, 1.0, 1.2599210498948732, 1.5874010519681994};
   private static final long HEX_40000000 = 1073741824L;
   private static final long MASK_30BITS = -1073741824L;
   private static final int MASK_NON_SIGN_INT = Integer.MAX_VALUE;
   private static final long MASK_NON_SIGN_LONG = Long.MAX_VALUE;
   private static final long MASK_DOUBLE_EXPONENT = 9218868437227405312L;
   private static final long MASK_DOUBLE_MANTISSA = 4503599627370495L;
   private static final long IMPLICIT_HIGH_BIT = 4503599627370496L;
   private static final double TWO_POWER_52 = 4.5035996E15F;
   private static final double F_1_3 = 0.3333333333333333;
   private static final double F_1_5 = 0.2;
   private static final double F_1_7 = 0.14285714285714285;
   private static final double F_1_9 = 0.1111111111111111;
   private static final double F_1_11 = 0.09090909090909091;
   private static final double F_1_13 = 0.07692307692307693;
   private static final double F_1_15 = 0.06666666666666667;
   private static final double F_1_17 = 0.058823529411764705;
   private static final double F_3_4 = 0.75;
   private static final double F_15_16 = 0.9375;
   private static final double F_13_14 = 0.9285714285714286;
   private static final double F_11_12 = 0.9166666666666666;
   private static final double F_9_10 = 0.9;
   private static final double F_7_8 = 0.875;
   private static final double F_5_6 = 0.8333333333333334;
   private static final double F_1_2 = 0.5;
   private static final double F_1_4 = 0.25;
   public static String f47d4f66a2c511ebbcbc0242ac130002 = "20488";
   public static String e3409f8a2c611ebbcbc0242ac130002 = "20488";

   private FastMath() {
   }

   private static double doubleHighPart(double var0) {
      long var2 = Double.doubleToRawLongBits(var0);
      var2 &= -1073741824L;
      return Double.longBitsToDouble(var2);
   }

   public static double sqrt(double var0) {
      if (var0 == 0.0) {
         return 0.0;
      } else {
         double var4 = var0 / 2.0;

         double var2;
         do {
            var2 = var4;
            var4 = (var4 + var0 / var4) / 2.0;
         } while (var2 - var4 != 0.0);

         return var4;
      }
   }

   public static double cosh(double var0) {
      if (Double.isNaN(var0)) {
         return var0;
      } else if (var0 > 20.0) {
         if (var0 >= LOG_MAX_VALUE) {
            double var22 = exp(0.5 * var0);
            return 0.5 * var22 * var22;
         } else {
            return 0.5 * exp(var0);
         }
      } else if (var0 < -20.0) {
         if (var0 <= -LOG_MAX_VALUE) {
            double var21 = exp(-0.5 * var0);
            return 0.5 * var21 * var21;
         } else {
            return 0.5 * exp(-var0);
         }
      } else {
         double[] var2 = new double[2];
         if (var0 < 0.0) {
            var0 = -var0;
         }

         exp(var0, 0.0, var2);
         double var3 = var2[0] + var2[1];
         double var5 = -(var3 - var2[0] - var2[1]);
         double var7 = var3 * 1.0737418E9F;
         double var9 = var3 + var7 - var7;
         double var11 = var3 - var9;
         double var13 = 1.0 / var3;
         var7 = var13 * 1.0737418E9F;
         double var15 = var13 + var7 - var7;
         double var17 = var13 - var15;
         var17 += (1.0 - var9 * var15 - var9 * var17 - var11 * var15 - var11 * var17) * var13;
         var17 += -var5 * var13 * var13;
         var7 = var3 + var15;
         var5 += -(var7 - var3 - var15);
         double var28 = var7 + var17;
         var5 += -(var28 - var7 - var17);
         double var19 = var28 + var5;
         return var19 * 0.5;
      }
   }

   public static double sinh(double var0) {
      boolean var2 = false;
      if (Double.isNaN(var0)) {
         return var0;
      } else if (var0 > 20.0) {
         if (var0 >= LOG_MAX_VALUE) {
            double var30 = exp(0.5 * var0);
            return 0.5 * var30 * var30;
         } else {
            return 0.5 * exp(var0);
         }
      } else if (var0 < -20.0) {
         if (var0 <= -LOG_MAX_VALUE) {
            double var29 = exp(-0.5 * var0);
            return -0.5 * var29 * var29;
         } else {
            return -0.5 * exp(-var0);
         }
      } else if (var0 == 0.0) {
         return var0;
      } else {
         if (var0 < 0.0) {
            var0 = -var0;
            var2 = true;
         }

         double[] var5 = new double[2];
         double var3;
         if (var0 > 0.25) {
            exp(var0, 0.0, var5);
            double var6 = var5[0] + var5[1];
            double var8 = -(var6 - var5[0] - var5[1]);
            double var10 = var6 * 1.0737418E9F;
            double var12 = var6 + var10 - var10;
            double var14 = var6 - var12;
            double var16 = 1.0 / var6;
            var10 = var16 * 1.0737418E9F;
            double var18 = var16 + var10 - var10;
            double var20 = var16 - var18;
            var20 += (1.0 - var12 * var18 - var12 * var20 - var14 * var18 - var14 * var20) * var16;
            var20 += -var8 * var16 * var16;
            var18 = -var18;
            var20 = -var20;
            var10 = var6 + var18;
            var8 += -(var10 - var6 - var18);
            double var41 = var10 + var20;
            var8 += -(var41 - var10 - var20);
            var3 = var41 + var8;
         } else {
            expm1(var0, var5);
            double var32 = var5[0] + var5[1];
            double var36 = -(var32 - var5[0] - var5[1]);
            double var42 = 1.0 + var32;
            double var43 = 1.0 / var42;
            double var44 = -(var42 - 1.0 - var32) + var36;
            double var45 = var32 * var43;
            double var47 = var45 * 1.0737418E9F;
            double var54 = var45 + var47 - var47;
            double var22 = var45 - var54;
            var47 = var42 * 1.0737418E9F;
            double var24 = var42 + var47 - var47;
            double var26 = var42 - var24;
            var22 += (var32 - var24 * var54 - var24 * var22 - var26 * var54 - var26 * var22) * var43;
            var22 += var36 * var43;
            var22 += -var32 * var44 * var43 * var43;
            var47 = var32 + var54;
            var36 += -(var47 - var32 - var54);
            double var50 = var47 + var22;
            var36 += -(var50 - var47 - var22);
            var3 = var50 + var36;
         }

         var3 *= 0.5;
         if (var2) {
            var3 = -var3;
         }

         return var3;
      }
   }

   public static double tanh(double var0) {
      boolean var2 = false;
      if (Double.isNaN(var0)) {
         return var0;
      } else if (var0 > 20.0) {
         return 1.0;
      } else if (var0 < -20.0) {
         return -1.0;
      } else if (var0 == 0.0) {
         return var0;
      } else {
         if (var0 < 0.0) {
            var0 = -var0;
            var2 = true;
         }

         double[] var5 = new double[2];
         double var3;
         if (var0 >= 0.5) {
            exp(var0 * 2.0, 0.0, var5);
            double var6 = var5[0] + var5[1];
            double var8 = -(var6 - var5[0] - var5[1]);
            double var10 = -1.0 + var6;
            double var12 = -(var10 + 1.0 - var6);
            double var14 = var10 + var8;
            var12 += -(var14 - var10 - var8);
            double var16 = 1.0 + var6;
            double var18 = -(var16 - 1.0 - var6);
            double var38 = var16 + var8;
            var18 += -(var38 - var16 - var8);
            double var39 = var38 * 1.0737418E9F;
            double var20 = var38 + var39 - var39;
            double var22 = var38 - var20;
            double var24 = var14 / var38;
            double var40 = var24 * 1.0737418E9F;
            double var26 = var24 + var40 - var40;
            double var28 = var24 - var26;
            var28 += (var14 - var20 * var26 - var20 * var28 - var22 * var26 - var22 * var28) / var38;
            var28 += var12 / var38;
            var28 += -var18 * var14 / var38 / var38;
            var3 = var26 + var28;
         } else {
            expm1(var0 * 2.0, var5);
            double var30 = var5[0] + var5[1];
            double var31 = -(var30 - var5[0] - var5[1]);
            double var33 = 2.0 + var30;
            double var36 = -(var33 - 2.0 - var30);
            double var41 = var33 + var31;
            var36 += -(var41 - var33 - var31);
            double var42 = var41 * 1.0737418E9F;
            double var45 = var41 + var42 - var42;
            double var47 = var41 - var45;
            double var48 = var30 / var41;
            double var43 = var48 * 1.0737418E9F;
            double var49 = var48 + var43 - var43;
            double var50 = var48 - var49;
            var50 += (var30 - var45 * var49 - var45 * var50 - var47 * var49 - var47 * var50) / var41;
            var50 += var31 / var41;
            var50 += -var36 * var30 / var41 / var41;
            var3 = var49 + var50;
         }

         if (var2) {
            var3 = -var3;
         }

         return var3;
      }
   }

   public static double acosh(double var0) {
      return log(var0 + sqrt(var0 * var0 - 1.0));
   }

   public static double asinh(double var0) {
      boolean var2 = false;
      if (var0 < 0.0) {
         var2 = true;
         var0 = -var0;
      }

      double var3;
      if (var0 > 0.167) {
         var3 = log(sqrt(var0 * var0 + 1.0) + var0);
      } else {
         double var5 = var0 * var0;
         if (var0 > 0.097) {
            var3 = var0
               * (
                  1.0
                     - var5
                        * (
                           0.3333333333333333
                              - var5
                                 * (
                                    0.2
                                       - var5
                                          * (
                                             0.14285714285714285
                                                - var5
                                                   * (
                                                      0.1111111111111111
                                                         - var5
                                                            * (
                                                               0.09090909090909091
                                                                  - var5
                                                                     * (
                                                                        0.07692307692307693
                                                                           - var5
                                                                              * (0.06666666666666667 - var5 * 0.058823529411764705 * 0.9375)
                                                                              * 0.9285714285714286
                                                                     )
                                                                     * 0.9166666666666666
                                                            )
                                                            * 0.9
                                                   )
                                                   * 0.875
                                          )
                                          * 0.8333333333333334
                                 )
                                 * 0.75
                        )
                        * 0.5
               );
         } else if (var0 > 0.036) {
            var3 = var0
               * (
                  1.0
                     - var5
                        * (
                           0.3333333333333333
                              - var5
                                 * (
                                    0.2
                                       - var5
                                          * (
                                             0.14285714285714285
                                                - var5
                                                   * (0.1111111111111111 - var5 * (0.09090909090909091 - var5 * 0.07692307692307693 * 0.9166666666666666) * 0.9)
                                                   * 0.875
                                          )
                                          * 0.8333333333333334
                                 )
                                 * 0.75
                        )
                        * 0.5
               );
         } else if (var0 > 0.0036) {
            var3 = var0
               * (
                  1.0
                     - var5
                        * (0.3333333333333333 - var5 * (0.2 - var5 * (0.14285714285714285 - var5 * 0.1111111111111111 * 0.875) * 0.8333333333333334) * 0.75)
                        * 0.5
               );
         } else {
            var3 = var0 * (1.0 - var5 * (0.3333333333333333 - var5 * 0.2 * 0.75) * 0.5);
         }
      }

      return var2 ? -var3 : var3;
   }

   public static double atanh(double var0) {
      boolean var2 = false;
      if (var0 < 0.0) {
         var2 = true;
         var0 = -var0;
      }

      double var3;
      if (var0 > 0.15) {
         var3 = 0.5 * log((1.0 + var0) / (1.0 - var0));
      } else {
         double var5 = var0 * var0;
         if (var0 > 0.087) {
            var3 = var0
               * (
                  1.0
                     + var5
                        * (
                           0.3333333333333333
                              + var5
                                 * (
                                    0.2
                                       + var5
                                          * (
                                             0.14285714285714285
                                                + var5
                                                   * (
                                                      0.1111111111111111
                                                         + var5
                                                            * (
                                                               0.09090909090909091
                                                                  + var5 * (0.07692307692307693 + var5 * (0.06666666666666667 + var5 * 0.058823529411764705))
                                                            )
                                                   )
                                          )
                                 )
                        )
               );
         } else if (var0 > 0.031) {
            var3 = var0
               * (
                  1.0
                     + var5
                        * (
                           0.3333333333333333
                              + var5
                                 * (
                                    0.2
                                       + var5 * (0.14285714285714285 + var5 * (0.1111111111111111 + var5 * (0.09090909090909091 + var5 * 0.07692307692307693)))
                                 )
                        )
               );
         } else if (var0 > 0.003) {
            var3 = var0 * (1.0 + var5 * (0.3333333333333333 + var5 * (0.2 + var5 * (0.14285714285714285 + var5 * 0.1111111111111111))));
         } else {
            var3 = var0 * (1.0 + var5 * (0.3333333333333333 + var5 * 0.2));
         }
      }

      return var2 ? -var3 : var3;
   }

   public static double signum(double var0) {
      return var0 < 0.0 ? -1.0 : (var0 > 0.0 ? 1.0 : var0);
   }

   public static float signum(float var0) {
      return var0 < 0.0F ? -1.0F : (var0 > 0.0F ? 1.0F : var0);
   }

   public static double nextUp(double var0) {
      return nextAfter(var0, Double.POSITIVE_INFINITY);
   }

   public static float nextUp(float var0) {
      return nextAfter(var0, Double.POSITIVE_INFINITY);
   }

   public static double nextDown(double var0) {
      return nextAfter(var0, Double.NEGATIVE_INFINITY);
   }

   public static float nextDown(float var0) {
      return nextAfter(var0, Double.NEGATIVE_INFINITY);
   }

   public static double exp(double var0) {
      return exp(var0, 0.0, null);
   }

   private static double exp(double var0, double var2, double[] var4) {
      int var9 = (int)var0;
      if (var0 < 0.0) {
         if (var0 < -746.0) {
            if (var4 != null) {
               var4[0] = 0.0;
               var4[1] = 0.0;
            }

            return 0.0;
         }

         if (var9 < -709) {
            double var27 = exp(var0 + 40.191406F, var2, var4) / 2.8504009514401178E17;
            if (var4 != null) {
               var4[0] /= 2.8504009514401178E17;
               var4[1] /= 2.8504009514401178E17;
            }

            return var27;
         }

         if (var9 == -709) {
            double var10 = exp(var0 + 1.4941406F, var2, var4) / 4.455505956692757;
            if (var4 != null) {
               var4[0] /= 4.455505956692757;
               var4[1] /= 4.455505956692757;
            }

            return var10;
         }

         var9--;
      } else if (var9 > 709) {
         if (var4 != null) {
            var4[0] = Double.POSITIVE_INFINITY;
            var4[1] = 0.0;
         }

         return Double.POSITIVE_INFINITY;
      }

      double var5 = FastMath.ExpIntTable.access$000()[750 + var9];
      double var7 = FastMath.ExpIntTable.access$100()[750 + var9];
      int var28 = (int)((var0 - (double)var9) * 1024.0);
      double var11 = FastMath.ExpFracTable.access$200()[var28];
      double var13 = FastMath.ExpFracTable.access$300()[var28];
      double var15 = var0 - ((double)var9 + (double)var28 / 1024.0);
      double var17 = 0.04168701738764507 * var15 + 0.1666666505023083;
      var17 = var17 * var15 + 0.5000000000042687;
      var17 = var17 * var15 + 1.0;
      var17 = var17 * var15 + -3.940510424527919E-20;
      double var19 = var5 * var11;
      double var21 = var5 * var13 + var7 * var11 + var7 * var13;
      double var23 = var21 + var19;
      if (var23 == Double.POSITIVE_INFINITY) {
         return Double.POSITIVE_INFINITY;
      } else {
         double var25;
         if (var2 != 0.0) {
            var25 = var23 * var2 * var17 + var23 * var2 + var23 * var17 + var21 + var19;
         } else {
            var25 = var23 * var17 + var21 + var19;
         }

         if (var4 != null) {
            var4[0] = var19;
            var4[1] = var23 * var2 * var17 + var23 * var2 + var23 * var17 + var21;
         }

         return var25;
      }
   }

   public static double expm1(double var0) {
      return expm1(var0, null);
   }

   private static double expm1(double var0, double[] var2) {
      if (Double.isNaN(var0) || var0 == 0.0) {
         return var0;
      } else if (!(var0 <= -1.0) && !(var0 >= 1.0)) {
         boolean var9 = false;
         if (var0 < 0.0) {
            var0 = -var0;
            var9 = true;
         }

         int var10 = (int)(var0 * 1024.0);
         double var11 = FastMath.ExpFracTable.access$200()[var10] - 1.0;
         double var13 = FastMath.ExpFracTable.access$300()[var10];
         double var15 = var11 + var13;
         var13 = -(var15 - var11 - var13);
         double var57 = var15 * 1.0737418E9F;
         double var32 = var15 + var57 - var57;
         double var5 = var13 + (var15 - var32);
         double var7 = var0 - (double)var10 / 1024.0;
         double var34 = 0.008336750013465571 * var7 + 0.041666663879186654;
         double var35 = var34 * var7 + 0.16666666666745392;
         double var36 = var35 * var7 + 0.49999999999999994;
         double var37 = var36 * var7;
         double var38 = var37 * var7;
         double var14 = var7 + var38;
         double var39 = -(var14 - var7 - var38);
         double var46 = var14 * 1.0737418E9F;
         double var47 = var14 + var46 - var46;
         double var40 = var39 + (var14 - var47);
         double var16 = var47 * var32;
         var14 = var16 + var47 * var5;
         double var18 = -(var14 - var16 - var47 * var5);
         double var49 = var14 + var40 * var32;
         var18 += -(var49 - var14 - var40 * var32);
         var14 = var49 + var40 * var5;
         var18 += -(var14 - var49 - var40 * var5);
         double var51 = var14 + var32;
         var18 += -(var51 - var32 - var14);
         var14 = var51 + var47;
         var18 += -(var14 - var51 - var47);
         double var53 = var14 + var5;
         var18 += -(var53 - var14 - var5);
         var14 = var53 + var40;
         var18 += -(var14 - var53 - var40);
         var16 = var14;
         if (var9) {
            double var20 = 1.0 + var14;
            double var22 = 1.0 / var20;
            double var24 = -(var20 - 1.0 - var14) + var18;
            double var26 = var14 * var22;
            double var55 = var26 * 1.0737418E9F;
            double var28 = var26 + var55 - var55;
            double var30 = var26 - var28;
            double var56 = var20 * 1.0737418E9F;
            double var44 = var20 + var56 - var56;
            double var41 = var20 - var44;
            var30 += (var14 - var44 * var28 - var44 * var30 - var41 * var28 - var41 * var30) * var22;
            var30 += var18 * var22;
            var30 += -var14 * var24 * var22 * var22;
            var16 = -var28;
            var18 = -var30;
         }

         if (var2 != null) {
            var2[0] = var16;
            var2[1] = var18;
         }

         return var16 + var18;
      } else {
         double[] var3 = new double[2];
         exp(var0, 0.0, var3);
         if (var0 > 0.0) {
            return -1.0 + var3[0] + var3[1];
         } else {
            double var4 = -1.0 + var3[0];
            double var6 = -(var4 + 1.0 - var3[0]);
            var6 += var3[1];
            return var4 + var6;
         }
      }
   }

   public static double log(double var0) {
      return log(var0, null);
   }

   private static double log(double var0, double[] var2) {
      if (var0 == 0.0) {
         return Double.NEGATIVE_INFINITY;
      } else {
         long var3 = Double.doubleToRawLongBits(var0);
         if (((var3 & Long.MIN_VALUE) != 0L || Double.isNaN(var0)) && var0 != 0.0) {
            if (var2 != null) {
               var2[0] = Double.NaN;
            }

            return Double.NaN;
         } else if (var0 == Double.POSITIVE_INFINITY) {
            if (var2 != null) {
               var2[0] = Double.POSITIVE_INFINITY;
            }

            return Double.POSITIVE_INFINITY;
         } else {
            int var5 = (int)(var3 >> 52) - 1023;
            if ((var3 & 9218868437227405312L) == 0L) {
               if (var0 == 0.0) {
                  if (var2 != null) {
                     var2[0] = Double.NEGATIVE_INFINITY;
                  }

                  return Double.NEGATIVE_INFINITY;
               }

               for (var3 <<= 1; (var3 & 4503599627370496L) == 0L; var3 <<= 1) {
                  var5--;
               }
            }

            if ((var5 == -1 || var5 == 0) && var0 < 1.01 && var0 > 0.99 && var2 == null) {
               double var34 = var0 - 1.0;
               double var10000 = var34 - var0 + 1.0;
               double var10 = var34 * 1.0737418E9F;
               double var12 = var34 + var10 - var10;
               double var14 = var34 - var12;
               var34 = var12;
               double var8 = var14;
               double[] var16 = LN_QUICK_COEF[LN_QUICK_COEF.length - 1];
               double var74 = var16[0];
               double var82 = var16[1];

               for (int var85 = LN_QUICK_COEF.length - 2; var85 >= 0; var85--) {
                  var12 = var74 * var34;
                  var14 = var74 * var8 + var82 * var34 + var82 * var8;
                  var10 = var12 * 1.0737418E9F;
                  double var75 = var12 + var10 - var10;
                  var82 = var12 - var75 + var14;
                  double[] var22 = LN_QUICK_COEF[var85];
                  var12 = var75 + var22[0];
                  var14 = var82 + var22[1];
                  var10 = var12 * 1.0737418E9F;
                  var74 = var12 + var10 - var10;
                  var82 = var12 - var74 + var14;
               }

               var12 = var74 * var34;
               var14 = var74 * var8 + var82 * var34 + var82 * var8;
               var10 = var12 * 1.0737418E9F;
               var74 = var12 + var10 - var10;
               var82 = var12 - var74 + var14;
               return var74 + var82;
            } else {
               double[] var6 = FastMath.lnMant.access$400()[(int)((var3 & 4499201580859392L) >> 42)];
               double var7 = (double)(var3 & 4398046511103L) / (4.5035996E15F + (double)(var3 & 4499201580859392L));
               double var11 = 0.0;
               double var9;
               if (var2 != null) {
                  double var13 = var7 * 1.0737418E9F;
                  double var15 = var7 + var13 - var13;
                  double var17 = var7 - var15;
                  double var19 = var15;
                  double var23 = (double)(var3 & 4398046511103L);
                  double var25 = 4.5035996E15F + (double)(var3 & 4499201580859392L);
                  var15 = var23 - var15 * var25 - var17 * var25;
                  double var21 = var17 + var15 / var25;
                  double[] var27 = LN_HI_PREC_COEF[LN_HI_PREC_COEF.length - 1];
                  double var28 = var27[0];
                  double var30 = var27[1];

                  for (int var32 = LN_HI_PREC_COEF.length - 2; var32 >= 0; var32--) {
                     var15 = var28 * var19;
                     var17 = var28 * var21 + var30 * var19 + var30 * var21;
                     var13 = var15 * 1.0737418E9F;
                     double var86 = var15 + var13 - var13;
                     var30 = var15 - var86 + var17;
                     double[] var33 = LN_HI_PREC_COEF[var32];
                     var15 = var86 + var33[0];
                     var17 = var30 + var33[1];
                     var13 = var15 * 1.0737418E9F;
                     var28 = var15 + var13 - var13;
                     var30 = var15 - var28 + var17;
                  }

                  var15 = var28 * var19;
                  var17 = var28 * var21 + var30 * var19 + var30 * var21;
                  var9 = var15 + var17;
                  var11 = -(var9 - var15 - var17);
               } else {
                  var9 = -0.16624882440418567 * var7 + 0.19999954120254515;
                  var9 = var9 * var7 + -0.2499999997677497;
                  var9 = var9 * var7 + 0.3333333333332802;
                  var9 = var9 * var7 + -0.5;
                  var9 = var9 * var7 + 1.0;
                  var9 *= var7;
               }

               double var49 = 0.69314706F * (double)var5;
               double var69 = var49 + var6[0];
               double var77 = -(var69 - var49 - var6[0]);
               double var61 = 0.0 + var77;
               double var70 = var69 + var9;
               var77 = -(var70 - var69 - var9);
               var61 += var77;
               var69 = var70 + 1.1730463525082348E-7 * (double)var5;
               var77 = -(var69 - var70 - 1.1730463525082348E-7 * (double)var5);
               var61 += var77;
               double var72 = var69 + var6[1];
               var77 = -(var72 - var69 - var6[1]);
               var61 += var77;
               var69 = var72 + var11;
               var77 = -(var69 - var72 - var11);
               var61 += var77;
               if (var2 != null) {
                  var2[0] = var69;
                  var2[1] = var61;
               }

               return var69 + var61;
            }
         }
      }
   }

   public static double log1p(double var0) {
      if (var0 == -1.0) {
         return Double.NEGATIVE_INFINITY;
      } else if (var0 == Double.POSITIVE_INFINITY) {
         return Double.POSITIVE_INFINITY;
      } else if (!(var0 > 1.0E-6) && !(var0 < -1.0E-6)) {
         double var13 = (var0 * 0.3333333333333333 - 0.5) * var0 + 1.0;
         return var13 * var0;
      } else {
         double var2 = 1.0 + var0;
         double var4 = -(var2 - 1.0 - var0);
         double[] var6 = new double[2];
         double var7 = log(var2, var6);
         if (Double.isInfinite(var7)) {
            return var7;
         } else {
            double var9 = var4 / var2;
            double var11 = 0.5 * var9 + 1.0;
            return var11 * var9 + var6[1] + var6[0];
         }
      }
   }

   public static double log10(double var0) {
      double[] var2 = new double[2];
      double var3 = log(var0, var2);
      if (Double.isInfinite(var3)) {
         return var3;
      } else {
         double var5 = var2[0] * 1.0737418E9F;
         double var7 = var2[0] + var5 - var5;
         double var9 = var2[0] - var7 + var2[1];
         return 1.9699272335463627E-8 * var9 + 1.9699272335463627E-8 * var7 + 0.43429446F * var9 + 0.43429446F * var7;
      }
   }

   public static double log(double var0, double var2) {
      return log(var2) / log(var0);
   }

   public static double pow(double var0, double var2) {
      if (var2 == 0.0) {
         return 1.0;
      } else {
         long var4 = Double.doubleToRawLongBits(var2);
         int var6 = (int)((var4 & 9218868437227405312L) >> 52);
         long var7 = var4 & 4503599627370495L;
         long var9 = Double.doubleToRawLongBits(var0);
         int var11 = (int)((var9 & 9218868437227405312L) >> 52);
         long var12 = var9 & 4503599627370495L;
         if (var6 <= 1085) {
            if (var6 >= 1023) {
               long var14 = 4503599627370496L | var7;
               if (var6 >= 1075) {
                  long var39 = var14 << var6 - 1075;
                  return pow(var0, var2 < 0.0 ? -var39 : var39);
               }

               long var16 = -1L << 1075 - var6;
               if ((var14 & var16) == var14) {
                  long var40 = var14 >> 1075 - var6;
                  return pow(var0, var2 < 0.0 ? -var40 : var40);
               }
            }

            if (var0 == 0.0) {
               return var2 < 0.0 ? Double.POSITIVE_INFINITY : 0.0;
            } else if (var11 == 2047) {
               if (var12 == 0L) {
                  return var2 < 0.0 ? 0.0 : Double.POSITIVE_INFINITY;
               } else {
                  return Double.NaN;
               }
            } else if (var0 < 0.0) {
               return Double.NaN;
            } else {
               double var37 = var2 * 1.0737418E9F;
               double var38 = var2 + var37 - var37;
               double var18 = var2 - var38;
               double[] var20 = new double[2];
               double var21 = log(var0, var20);
               if (Double.isInfinite(var21)) {
                  return var21;
               } else {
                  double var23 = var20[0];
                  double var25 = var20[1];
                  double var27 = var23 * 1.0737418E9F;
                  double var29 = var23 + var27 - var27;
                  var25 += var23 - var29;
                  double var31 = var29 * var38;
                  double var33 = var29 * var18 + var25 * var38 + var25 * var18;
                  var23 = var31 + var33;
                  var25 = -(var23 - var31 - var33);
                  double var35 = 0.008333333333333333 * var25 + 0.041666666666666664;
                  var35 = var35 * var25 + 0.16666666666666666;
                  var35 = var35 * var25 + 0.5;
                  var35 = var35 * var25 + 1.0;
                  var35 *= var25;
                  return exp(var23, var35, null);
               }
            }
         } else if ((var6 != 2047 || var7 == 0L) && (var11 != 2047 || var12 == 0L)) {
            if (var11 == 1023 && var12 == 0L) {
               return var6 == 2047 ? Double.NaN : 1.0;
            } else {
               return var2 > 0.0 ^ var11 < 1023 ? Double.POSITIVE_INFINITY : 0.0;
            }
         } else {
            return Double.NaN;
         }
      }
   }

   public static double pow(double var0, int var2) {
      return pow(var0, (long)var2);
   }

   public static double pow(double var0, long var2) {
      if (var2 == 0L) {
         return 1.0;
      } else {
         return var2 > 0L
            ? FastMath.Split.access$600(FastMath.Split.access$500(new FastMath.Split(var0), var2))
            : FastMath.Split.access$600(FastMath.Split.access$500(new FastMath.Split(var0).reciprocal(), -var2));
      }
   }

   private static double polySine(double var0) {
      double var2 = var0 * var0;
      double var4 = 2.7553817452272217E-6 * var2 + -1.9841269659586505E-4;
      var4 = var4 * var2 + 0.008333333333329196;
      var4 = var4 * var2 + -0.16666666666666666;
      return var4 * var2 * var0;
   }

   private static double polyCosine(double var0) {
      double var2 = var0 * var0;
      double var4 = 2.479773539153719E-5 * var2 + -0.0013888888689039883;
      var4 = var4 * var2 + 0.041666666666621166;
      var4 = var4 * var2 + -0.49999999999999994;
      return var4 * var2;
   }

   private static double sinQ(double var0, double var2) {
      int var4 = (int)(var0 * 8.0 + 0.5);
      double var5 = var0 - EIGHTHS[var4];
      double var7 = SINE_TABLE_A[var4];
      double var9 = SINE_TABLE_B[var4];
      double var11 = COSINE_TABLE_A[var4];
      double var13 = COSINE_TABLE_B[var4];
      double var17 = polySine(var5);
      double var21 = polyCosine(var5);
      double var23 = var5 * 1.0737418E9F;
      double var25 = var5 + var23 - var23;
      var17 += var5 - var25;
      double var35 = 0.0 + var7;
      double var37 = -(var35 - 0.0 - var7);
      double var31 = 0.0 + var37;
      double var33 = var11 * var25;
      double var45 = var35 + var33;
      var37 = -(var45 - var35 - var33);
      double var40 = var45;
      var31 += var37;
      var31 = var31 + var7 * var21 + var11 * var17;
      var31 = var31 + var9 + var13 * var25 + var9 * var21 + var13 * var17;
      if (var2 != 0.0) {
         var33 = ((var11 + var13) * (1.0 + var21) - (var7 + var9) * (var25 + var17)) * var2;
         var35 = var45 + var33;
         var37 = -(var35 - var45 - var33);
         var40 = var35;
         var31 += var37;
      }

      return var40 + var31;
   }

   private static double cosQ(double var0, double var2) {
      double var8 = (Math.PI / 2) - var0;
      double var10 = -(var8 - (Math.PI / 2) + var0);
      var10 += 6.123233995736766E-17 - var2;
      return sinQ(var8, var10);
   }

   private static double tanQ(double var0, double var2, boolean var4) {
      int var5 = (int)(var0 * 8.0 + 0.5);
      double var6 = var0 - EIGHTHS[var5];
      double var8 = SINE_TABLE_A[var5];
      double var10 = SINE_TABLE_B[var5];
      double var12 = COSINE_TABLE_A[var5];
      double var14 = COSINE_TABLE_B[var5];
      double var18 = polySine(var6);
      double var22 = polyCosine(var6);
      double var24 = var6 * 1.0737418E9F;
      double var26 = var6 + var24 - var24;
      var18 += var6 - var26;
      double var34 = 0.0 + var8;
      double var36 = -(var34 - 0.0 - var8);
      double var30 = 0.0 + var36;
      double var32 = var12 * var26;
      double var73 = var34 + var32;
      var36 = -(var73 - var34 - var32);
      var30 += var36;
      var30 += var8 * var22 + var12 * var18;
      var30 += var10 + var14 * var26 + var10 * var22 + var14 * var18;
      double var38 = var73 + var30;
      double var40 = -(var38 - var73 - var30);
      var32 = var12 * 1.0;
      var34 = 0.0 + var32;
      var36 = -(var34 - 0.0 - var32);
      var30 = 0.0 + var36;
      var32 = -var8 * var26;
      double var75 = var34 + var32;
      var36 = -(var75 - var34 - var32);
      var30 += var36;
      var30 += var14 * 1.0 + var12 * var22 + var14 * var22;
      var30 -= var10 * var26 + var8 * var18 + var10 * var18;
      double var42 = var75 + var30;
      double var44 = -(var42 - var75 - var30);
      if (var4) {
         double var46 = var42;
         var42 = var38;
         var38 = var46;
         var46 = var44;
         var44 = var40;
         var40 = var46;
      }

      double var80 = var38 / var42;
      var24 = var80 * 1.0737418E9F;
      double var48 = var80 + var24 - var24;
      double var50 = var80 - var48;
      var24 = var42 * 1.0737418E9F;
      double var52 = var42 + var24 - var24;
      double var54 = var42 - var52;
      double var56 = (var38 - var48 * var52 - var48 * var54 - var50 * var52 - var50 * var54) / var42;
      var56 += var40 / var42;
      var56 += -var38 * var44 / var42 / var42;
      if (var2 != 0.0) {
         double var58 = var2 + var80 * var80 * var2;
         if (var4) {
            var58 = -var58;
         }

         var56 += var58;
      }

      return var80 + var56;
   }

   private static void reducePayneHanek(double var0, double[] var2) {
      long var3 = Double.doubleToRawLongBits(var0);
      int var5 = (int)(var3 >> 52 & 2047L) - 1023;
      var3 &= 4503599627370495L;
      var3 |= 4503599627370496L;
      var5++;
      var3 <<= 11;
      int var12 = var5 >> 6;
      int var13 = var5 - (var12 << 6);
      long var8;
      long var10;
      long var54;
      if (var13 != 0) {
         var54 = var12 == 0 ? 0L : RECIP_2PI[var12 - 1] << var13;
         var54 |= RECIP_2PI[var12] >>> 64 - var13;
         var8 = RECIP_2PI[var12] << var13 | RECIP_2PI[var12 + 1] >>> 64 - var13;
         var10 = RECIP_2PI[var12 + 1] << var13 | RECIP_2PI[var12 + 2] >>> 64 - var13;
      } else {
         var54 = var12 == 0 ? 0L : RECIP_2PI[var12 - 1];
         var8 = RECIP_2PI[var12];
         var10 = RECIP_2PI[var12 + 1];
      }

      long var14 = var3 >>> 32;
      long var16 = var3 & 4294967295L;
      long var18 = var8 >>> 32;
      long var20 = var8 & 4294967295L;
      long var22 = var14 * var18;
      long var24 = var16 * var20;
      long var26 = var16 * var18;
      long var28 = var14 * var20;
      long var30 = var24 + (var28 << 32);
      long var32 = var22 + (var28 >>> 32);
      boolean var34 = (var24 & Long.MIN_VALUE) != 0L;
      boolean var35 = (var28 & 2147483648L) != 0L;
      boolean var36 = (var30 & Long.MIN_VALUE) != 0L;
      if (var34 && var35 || (var34 || var35) && !var36) {
         var32++;
      }

      var34 = (var30 & Long.MIN_VALUE) != 0L;
      var35 = (var26 & 2147483648L) != 0L;
      var30 += var26 << 32;
      var32 += var26 >>> 32;
      var36 = (var30 & Long.MIN_VALUE) != 0L;
      if (var34 && var35 || (var34 || var35) && !var36) {
         var32++;
      }

      var18 = var10 >>> 32;
      var20 = var10 & 4294967295L;
      var22 = var14 * var18;
      var26 = var16 * var18;
      var28 = var14 * var20;
      var22 += var26 + var28 >>> 32;
      var34 = (var30 & Long.MIN_VALUE) != 0L;
      var35 = (var22 & Long.MIN_VALUE) != 0L;
      var30 += var22;
      var36 = (var30 & Long.MIN_VALUE) != 0L;
      if (var34 && var35 || (var34 || var35) && !var36) {
         var32++;
      }

      var18 = var54 >>> 32;
      var20 = var54 & 4294967295L;
      var24 = var16 * var20;
      var26 = var16 * var18;
      var28 = var14 * var20;
      var32 += var24 + (var26 + var28 << 32);
      int var37 = (int)(var32 >>> 62);
      var32 <<= 2;
      var32 |= var30 >>> 62;
      var30 <<= 2;
      var14 = var32 >>> 32;
      var16 = var32 & 4294967295L;
      var18 = PI_O_4_BITS[0] >>> 32;
      var20 = PI_O_4_BITS[0] & 4294967295L;
      var22 = var14 * var18;
      var24 = var16 * var20;
      var26 = var16 * var18;
      var28 = var14 * var20;
      long var38 = var24 + (var28 << 32);
      long var40 = var22 + (var28 >>> 32);
      var34 = (var24 & Long.MIN_VALUE) != 0L;
      var35 = (var28 & 2147483648L) != 0L;
      var36 = (var38 & Long.MIN_VALUE) != 0L;
      if (var34 && var35 || (var34 || var35) && !var36) {
         var40++;
      }

      var34 = (var38 & Long.MIN_VALUE) != 0L;
      var35 = (var26 & 2147483648L) != 0L;
      var38 += var26 << 32;
      var40 += var26 >>> 32;
      var36 = (var38 & Long.MIN_VALUE) != 0L;
      if (var34 && var35 || (var34 || var35) && !var36) {
         var40++;
      }

      var18 = PI_O_4_BITS[1] >>> 32;
      var20 = PI_O_4_BITS[1] & 4294967295L;
      var22 = var14 * var18;
      var26 = var16 * var18;
      var28 = var14 * var20;
      var22 += var26 + var28 >>> 32;
      var34 = (var38 & Long.MIN_VALUE) != 0L;
      var35 = (var22 & Long.MIN_VALUE) != 0L;
      var38 += var22;
      var36 = (var38 & Long.MIN_VALUE) != 0L;
      if (var34 && var35 || (var34 || var35) && !var36) {
         var40++;
      }

      var14 = var30 >>> 32;
      var16 = var30 & 4294967295L;
      var18 = PI_O_4_BITS[0] >>> 32;
      var20 = PI_O_4_BITS[0] & 4294967295L;
      var22 = var14 * var18;
      var26 = var16 * var18;
      var28 = var14 * var20;
      var22 += var26 + var28 >>> 32;
      var34 = (var38 & Long.MIN_VALUE) != 0L;
      var35 = (var22 & Long.MIN_VALUE) != 0L;
      var38 += var22;
      var36 = (var38 & Long.MIN_VALUE) != 0L;
      if (var34 && var35 || (var34 || var35) && !var36) {
         var40++;
      }

      double var42 = (double)(var40 >>> 12) / 4.5035996E15F;
      double var44 = (double)(((var40 & 4095L) << 40) + (var38 >>> 24)) / 4.5035996E15F / 4.5035996E15F;
      double var46 = var42 + var44;
      double var48 = -(var46 - var42 - var44);
      var2[0] = (double)var37;
      var2[1] = var46 * 2.0;
      var2[2] = var48 * 2.0;
   }

   public static double sin(double var0) {
      boolean var2 = false;
      int var3 = 0;
      double var6 = 0.0;
      double var4 = var0;
      if (var0 < 0.0) {
         var2 = true;
         var4 = -var0;
      }

      if (var4 == 0.0) {
         long var11 = Double.doubleToRawLongBits(var0);
         return var11 < 0L ? -0.0 : 0.0;
      } else if (var4 == var4 && var4 != Double.POSITIVE_INFINITY) {
         if (var4 > 3294198.0) {
            double[] var8 = new double[3];
            reducePayneHanek(var4, var8);
            var3 = (int)var8[0] & 3;
            var4 = var8[1];
            var6 = var8[2];
         } else if (var4 > Math.PI / 2) {
            FastMath.CodyWaite var10 = new FastMath.CodyWaite(var4);
            var3 = var10.getK() & 3;
            var4 = var10.getRemA();
            var6 = var10.getRemB();
         }

         if (var2) {
            var3 ^= 2;
         }

         switch (var3) {
            case 0:
               return sinQ(var4, var6);
            case 1:
               return cosQ(var4, var6);
            case 2:
               return -sinQ(var4, var6);
            case 3:
               return -cosQ(var4, var6);
            default:
               return Double.NaN;
         }
      } else {
         return Double.NaN;
      }
   }

   public static double cos(double var0) {
      int var2 = 0;
      double var3 = var0;
      if (var0 < 0.0) {
         var3 = -var0;
      }

      if (var3 == var3 && var3 != Double.POSITIVE_INFINITY) {
         double var5 = 0.0;
         if (var3 > 3294198.0) {
            double[] var7 = new double[3];
            reducePayneHanek(var3, var7);
            var2 = (int)var7[0] & 3;
            var3 = var7[1];
            var5 = var7[2];
         } else if (var3 > Math.PI / 2) {
            FastMath.CodyWaite var8 = new FastMath.CodyWaite(var3);
            var2 = var8.getK() & 3;
            var3 = var8.getRemA();
            var5 = var8.getRemB();
         }

         switch (var2) {
            case 0:
               return cosQ(var3, var5);
            case 1:
               return -sinQ(var3, var5);
            case 2:
               return -cosQ(var3, var5);
            case 3:
               return sinQ(var3, var5);
            default:
               return Double.NaN;
         }
      } else {
         return Double.NaN;
      }
   }

   public static double tan(double var0) {
      boolean var2 = false;
      int var3 = 0;
      double var4 = var0;
      if (var0 < 0.0) {
         var2 = true;
         var4 = -var0;
      }

      if (var4 == 0.0) {
         long var16 = Double.doubleToRawLongBits(var0);
         return var16 < 0L ? -0.0 : 0.0;
      } else if (var4 == var4 && var4 != Double.POSITIVE_INFINITY) {
         double var6 = 0.0;
         if (var4 > 3294198.0) {
            double[] var8 = new double[3];
            reducePayneHanek(var4, var8);
            var3 = (int)var8[0] & 3;
            var4 = var8[1];
            var6 = var8[2];
         } else if (var4 > Math.PI / 2) {
            FastMath.CodyWaite var17 = new FastMath.CodyWaite(var4);
            var3 = var17.getK() & 3;
            var4 = var17.getRemA();
            var6 = var17.getRemB();
         }

         if (var4 > 1.5) {
            double var12 = (Math.PI / 2) - var4;
            double var14 = -(var12 - (Math.PI / 2) + var4);
            var14 += 6.123233995736766E-17 - var6;
            var4 = var12 + var14;
            var6 = -(var4 - var12 - var14);
            var3 ^= 1;
            var2 ^= true;
         }

         double var18;
         if ((var3 & 1) == 0) {
            var18 = tanQ(var4, var6, false);
         } else {
            var18 = -tanQ(var4, var6, true);
         }

         if (var2) {
            var18 = -var18;
         }

         return var18;
      } else {
         return Double.NaN;
      }
   }

   public static double atan(double var0) {
      return atan(var0, 0.0, false);
   }

   private static double atan(double var0, double var2, boolean var4) {
      if (var0 == 0.0) {
         return var4 ? copySign(Math.PI, var0) : var0;
      } else {
         boolean var5;
         if (var0 < 0.0) {
            var0 = -var0;
            var2 = -var2;
            var5 = true;
         } else {
            var5 = false;
         }

         if (var0 > 1.633123935319537E16) {
            return var5 ^ var4 ? -Math.PI / 2 : Math.PI / 2;
         } else {
            int var6;
            if (var0 < 1.0) {
               var6 = (int)((-1.7168146928204135 * var0 * var0 + 8.0) * var0 + 0.5);
            } else {
               double var7 = 1.0 / var0;
               var6 = (int)(-((-1.7168146928204135 * var7 * var7 + 8.0) * var7) + 13.07);
            }

            double var36 = TANGENT_TABLE_A[var6];
            double var9 = TANGENT_TABLE_B[var6];
            double var11 = var0 - var36;
            double var13 = -(var11 - var0 + var36);
            var13 += var2 - var9;
            double var15 = var11 + var13;
            var13 = -(var15 - var11 - var13);
            var15 = var0 * 1.0737418E9F;
            double var17 = var0 + var15 - var15;
            double var19 = var2 + var0 - var17;
            var2 += var19;
            if (var6 == 0) {
               double var21 = 1.0 / (1.0 + (var17 + var2) * (var36 + var9));
               var17 = var15 * var21;
               var19 = var13 * var21;
            } else {
               double var59 = var17 * var36;
               double var23 = 1.0 + var59;
               double var25 = -(var23 - 1.0 - var59);
               var59 = var2 * var36 + var17 * var9;
               double var42 = var23 + var59;
               var25 += -(var42 - var23 - var59);
               var25 += var2 * var9;
               var17 = var15 / var42;
               double var43 = var17 * 1.0737418E9F;
               double var27 = var17 + var43 - var43;
               double var29 = var17 - var27;
               double var44 = var42 * 1.0737418E9F;
               double var31 = var42 + var44 - var44;
               double var33 = var42 - var31;
               var19 = (var15 - var27 * var31 - var27 * var33 - var29 * var31 - var29 * var33) / var42;
               var19 += -var15 * var25 / var42 / var42;
               var19 += var13 / var42;
            }

            double var61 = var17 * var17;
            double var51 = 0.07490822288864472 * var61 - 0.09088450866185192;
            double var52 = var51 * var61 + 0.11111095942313305;
            double var53 = var52 * var61 - 0.1428571423679182;
            double var54 = var53 * var61 + 0.19999999999923582;
            double var55 = var54 * var61 - 0.33333333333333287;
            double var56 = var55 * var61 * var17;
            var15 = var17 + var56;
            double var57 = -(var15 - var17 - var56);
            var19 = var57 + var19 / (1.0 + var17 * var17);
            double var63 = EIGHTHS[var6];
            double var66 = var63 + var15;
            double var68 = -(var66 - var63 - var15);
            var15 = var66 + var19;
            var68 += -(var15 - var66 - var19);
            double var72 = var15 + var68;
            if (var4) {
               double var73 = -(var72 - var15 - var68);
               var66 = Math.PI - var72;
               var68 = -(var66 - Math.PI + var72);
               var68 += 1.2246467991473532E-16 - var73;
               var72 = var66 + var68;
            }

            if (var5 ^ var4) {
               var72 = -var72;
            }

            return var72;
         }
      }
   }

   public static double atan2(double var0, double var2) {
      if (Double.isNaN(var2) || Double.isNaN(var0)) {
         return Double.NaN;
      } else if (var0 == 0.0) {
         double var16 = var2 * var0;
         double var18 = 1.0 / var2;
         double var21 = 1.0 / var0;
         if (var18 == 0.0) {
            return var2 > 0.0 ? var0 : copySign(Math.PI, var0);
         } else if (!(var2 < 0.0) && !(var18 < 0.0)) {
            return var16;
         } else {
            return !(var0 < 0.0) && !(var21 < 0.0) ? Math.PI : -Math.PI;
         }
      } else if (var0 == Double.POSITIVE_INFINITY) {
         if (var2 == Double.POSITIVE_INFINITY) {
            return Math.PI / 4;
         } else {
            return var2 == Double.NEGATIVE_INFINITY ? Math.PI * 3.0 / 4.0 : Math.PI / 2;
         }
      } else if (var0 != Double.NEGATIVE_INFINITY) {
         if (var2 == Double.POSITIVE_INFINITY) {
            if (var0 > 0.0 || 1.0 / var0 > 0.0) {
               return 0.0;
            }

            if (var0 < 0.0 || 1.0 / var0 < 0.0) {
               return -0.0;
            }
         }

         if (var2 == Double.NEGATIVE_INFINITY) {
            if (var0 > 0.0 || 1.0 / var0 > 0.0) {
               return Math.PI;
            }

            if (var0 < 0.0 || 1.0 / var0 < 0.0) {
               return -Math.PI;
            }
         }

         if (var2 == 0.0) {
            if (var0 > 0.0 || 1.0 / var0 > 0.0) {
               return Math.PI / 2;
            }

            if (var0 < 0.0 || 1.0 / var0 < 0.0) {
               return -Math.PI / 2;
            }
         }

         double var4 = var0 / var2;
         if (Double.isInfinite(var4)) {
            return atan(var4, 0.0, var2 < 0.0);
         } else {
            double var6 = doubleHighPart(var4);
            double var8 = var4 - var6;
            double var10 = doubleHighPart(var2);
            double var12 = var2 - var10;
            var8 += (var0 - var6 * var10 - var6 * var12 - var8 * var10 - var8 * var12) / var2;
            double var14 = var6 + var8;
            var8 = -(var14 - var6 - var8);
            var6 = var14;
            if (var14 == 0.0) {
               var6 = copySign(0.0, var0);
            }

            return atan(var6, var8, var2 < 0.0);
         }
      } else if (var2 == Double.POSITIVE_INFINITY) {
         return -Math.PI / 4;
      } else {
         return var2 == Double.NEGATIVE_INFINITY ? -Math.PI * 3.0 / 4.0 : -Math.PI / 2;
      }
   }

   public static double asin(double var0) {
      if (Double.isNaN(var0)) {
         return Double.NaN;
      } else if (var0 > 1.0 || var0 < -1.0) {
         return Double.NaN;
      } else if (var0 == 1.0) {
         return Math.PI / 2;
      } else if (var0 == -1.0) {
         return -Math.PI / 2;
      } else if (var0 == 0.0) {
         return var0;
      } else {
         double var2 = var0 * 1.0737418E9F;
         double var4 = var0 + var2 - var2;
         double var6 = var0 - var4;
         double var8 = var4 * var4;
         double var10 = var4 * var6 * 2.0 + var6 * var6;
         var8 = -var8;
         var10 = -var10;
         double var12 = 1.0 + var8;
         double var14 = -(var12 - 1.0 - var8);
         var2 = var12 + var10;
         var14 += -(var2 - var12 - var10);
         double var16 = sqrt(var2);
         double var27 = var16 * 1.0737418E9F;
         var8 = var16 + var27 - var27;
         var10 = var16 - var8;
         var10 += (var2 - var8 * var8 - 2.0 * var8 * var10 - var10 * var10) / (2.0 * var16);
         double var18 = var14 / (2.0 * var16);
         double var20 = var0 / var16;
         var2 = var20 * 1.0737418E9F;
         double var22 = var20 + var2 - var2;
         double var24 = var20 - var22;
         var24 += (var0 - var22 * var8 - var22 * var10 - var24 * var8 - var24 * var10) / var16;
         var24 += -var0 * var18 / var16 / var16;
         var2 = var22 + var24;
         var24 = -(var2 - var22 - var24);
         return atan(var2, var24, false);
      }
   }

   public static double acos(double var0) {
      if (Double.isNaN(var0)) {
         return Double.NaN;
      } else if (var0 > 1.0 || var0 < -1.0) {
         return Double.NaN;
      } else if (var0 == -1.0) {
         return Math.PI;
      } else if (var0 == 1.0) {
         return 0.0;
      } else if (var0 == 0.0) {
         return Math.PI / 2;
      } else {
         double var2 = var0 * 1.0737418E9F;
         double var4 = var0 + var2 - var2;
         double var6 = var0 - var4;
         double var8 = var4 * var4;
         double var10 = var4 * var6 * 2.0 + var6 * var6;
         var8 = -var8;
         var10 = -var10;
         double var12 = 1.0 + var8;
         double var14 = -(var12 - 1.0 - var8);
         var2 = var12 + var10;
         var14 += -(var2 - var12 - var10);
         double var16 = sqrt(var2);
         double var25 = var16 * 1.0737418E9F;
         var8 = var16 + var25 - var25;
         var10 = var16 - var8;
         var10 += (var2 - var8 * var8 - 2.0 * var8 * var10 - var10 * var10) / (2.0 * var16);
         var10 += var14 / (2.0 * var16);
         var16 = var8 + var10;
         var10 = -(var16 - var8 - var10);
         double var18 = var16 / var0;
         if (Double.isInfinite(var18)) {
            return Math.PI / 2;
         } else {
            double var20 = doubleHighPart(var18);
            double var22 = var18 - var20;
            var22 += (var16 - var20 * var4 - var20 * var6 - var22 * var4 - var22 * var6) / var0;
            var22 += var10 / var0;
            var2 = var20 + var22;
            var22 = -(var2 - var20 - var22);
            return atan(var2, var22, var0 < 0.0);
         }
      }
   }

   public static double cbrt(double var0) {
      long var2 = Double.doubleToRawLongBits(var0);
      int var4 = (int)(var2 >> 52 & 2047L) - 1023;
      boolean var5 = false;
      if (var4 == -1023) {
         if (var0 == 0.0) {
            return var0;
         }

         var5 = true;
         var0 *= 1.8014399E16F;
         var2 = Double.doubleToRawLongBits(var0);
         var4 = (int)(var2 >> 52 & 2047L) - 1023;
      }

      if (var4 == 1024) {
         return var0;
      } else {
         int var6 = var4 / 3;
         double var7 = Double.longBitsToDouble(var2 & Long.MIN_VALUE | (long)(var6 + 1023 & 2047) << 52);
         double var9 = Double.longBitsToDouble(var2 & 4503599627370495L | 4607182418800017408L);
         double var11 = -0.010714690733195933 * var9 + 0.0875862700108075;
         var11 = var11 * var9 + -0.3058015757857271;
         var11 = var11 * var9 + 0.7249995199969751;
         var11 = var11 * var9 + 0.5039018405998233;
         var11 *= CBRTTWO[var4 % 3 + 2];
         double var13 = var0 / (var7 * var7 * var7);
         var11 += (var13 - var11 * var11 * var11) / (3.0 * var11 * var11);
         var11 += (var13 - var11 * var11 * var11) / (3.0 * var11 * var11);
         double var15 = var11 * 1.0737418E9F;
         double var17 = var11 + var15 - var15;
         double var19 = var11 - var17;
         double var21 = var17 * var17;
         double var23 = var17 * var19 * 2.0 + var19 * var19;
         var15 = var21 * 1.0737418E9F;
         double var25 = var21 + var15 - var15;
         var23 += var21 - var25;
         var23 = var25 * var19 + var17 * var23 + var23 * var19;
         var21 = var25 * var17;
         double var27 = var13 - var21;
         double var29 = -(var27 - var13 + var21);
         var29 -= var23;
         var11 += (var27 + var29) / (3.0 * var11 * var11);
         var11 *= var7;
         if (var5) {
            var11 *= 3.8146973E-6F;
         }

         return var11;
      }
   }

   public static double toRadians(double var0) {
      if (!Double.isInfinite(var0) && var0 != 0.0) {
         double var6 = doubleHighPart(var0);
         double var8 = var0 - var6;
         double var10 = var8 * 1.997844754509471E-9 + var8 * 0.01745329F + var6 * 1.997844754509471E-9 + var6 * 0.01745329F;
         if (var10 == 0.0) {
            var10 *= var0;
         }

         return var10;
      } else {
         return var0;
      }
   }

   public static double toDegrees(double var0) {
      if (!Double.isInfinite(var0) && var0 != 0.0) {
         double var6 = doubleHighPart(var0);
         double var8 = var0 - var6;
         return var8 * 3.145894820876798E-6 + var8 * 180.0F / (float)Math.PI + var6 * 3.145894820876798E-6 + var6 * 180.0F / (float)Math.PI;
      } else {
         return var0;
      }
   }

   public static int abs(int var0) {
      int var1 = var0 >>> 31;
      return (var0 ^ ~var1 + 1) + var1;
   }

   public static long abs(long var0) {
      long var2 = var0 >>> 63;
      return (var0 ^ ~var2 + 1L) + var2;
   }

   public static float abs(float var0) {
      return Float.intBitsToFloat(2147483647 & Float.floatToRawIntBits(var0));
   }

   public static double abs(double var0) {
      return Double.longBitsToDouble(Long.MAX_VALUE & Double.doubleToRawLongBits(var0));
   }

   public static double ulp(double var0) {
      return Double.isInfinite(var0) ? Double.POSITIVE_INFINITY : abs(var0 - Double.longBitsToDouble(Double.doubleToRawLongBits(var0) ^ 1L));
   }

   public static float ulp(float var0) {
      return Float.isInfinite(var0) ? Float.POSITIVE_INFINITY : abs(var0 - Float.intBitsToFloat(Float.floatToIntBits(var0) ^ 1));
   }

   public static double scalb(double var0, int var2) {
      if (var2 > -1023 && var2 < 1024) {
         return var0 * Double.longBitsToDouble((long)(var2 + 1023) << 52);
      } else if (Double.isNaN(var0) || Double.isInfinite(var0) || var0 == 0.0) {
         return var0;
      } else if (var2 < -2098) {
         return var0 > 0.0 ? 0.0 : -0.0;
      } else if (var2 > 2097) {
         return var0 > 0.0 ? Double.POSITIVE_INFINITY : Double.NEGATIVE_INFINITY;
      } else {
         long var3 = Double.doubleToRawLongBits(var0);
         long var5 = var3 & Long.MIN_VALUE;
         int var7 = (int)(var3 >>> 52) & 2047;
         long var8 = var3 & 4503599627370495L;
         int var10 = var7 + var2;
         if (var2 < 0) {
            if (var10 > 0) {
               return Double.longBitsToDouble(var5 | (long)var10 << 52 | var8);
            } else if (var10 > -53) {
               var8 |= 4503599627370496L;
               long var11 = var8 & 1L << -var10;
               var8 >>>= 1 - var10;
               if (var11 != 0L) {
                  var8++;
               }

               return Double.longBitsToDouble(var5 | var8);
            } else {
               return var5 == 0L ? 0.0 : -0.0;
            }
         } else if (var7 == 0) {
            while (var8 >>> 52 != 1L) {
               var8 <<= 1;
               var10--;
            }

            var10++;
            var8 &= 4503599627370495L;
            if (var10 < 2047) {
               return Double.longBitsToDouble(var5 | (long)var10 << 52 | var8);
            } else {
               return var5 == 0L ? Double.POSITIVE_INFINITY : Double.NEGATIVE_INFINITY;
            }
         } else if (var10 < 2047) {
            return Double.longBitsToDouble(var5 | (long)var10 << 52 | var8);
         } else {
            return var5 == 0L ? Double.POSITIVE_INFINITY : Double.NEGATIVE_INFINITY;
         }
      }
   }

   public static float scalb(float var0, int var1) {
      if (var1 > -127 && var1 < 128) {
         return var0 * Float.intBitsToFloat(var1 + 127 << 23);
      } else if (Float.isNaN(var0) || Float.isInfinite(var0) || var0 == 0.0F) {
         return var0;
      } else if (var1 < -277) {
         return var0 > 0.0F ? 0.0F : -0.0F;
      } else if (var1 > 276) {
         return var0 > 0.0F ? Float.POSITIVE_INFINITY : Float.NEGATIVE_INFINITY;
      } else {
         int var2 = Float.floatToIntBits(var0);
         int var3 = var2 & -2147483648;
         int var4 = var2 >>> 23 & 0xFF;
         int var5 = var2 & 8388607;
         int var6 = var4 + var1;
         if (var1 < 0) {
            if (var6 > 0) {
               return Float.intBitsToFloat(var3 | var6 << 23 | var5);
            } else if (var6 > -24) {
               var5 |= 8388608;
               int var7 = var5 & 1 << -var6;
               var5 >>>= 1 - var6;
               if (var7 != 0) {
                  var5++;
               }

               return Float.intBitsToFloat(var3 | var5);
            } else {
               return var3 == 0 ? 0.0F : -0.0F;
            }
         } else if (var4 == 0) {
            while (var5 >>> 23 != 1) {
               var5 <<= 1;
               var6--;
            }

            var6++;
            var5 &= 8388607;
            if (var6 < 255) {
               return Float.intBitsToFloat(var3 | var6 << 23 | var5);
            } else {
               return var3 == 0 ? Float.POSITIVE_INFINITY : Float.NEGATIVE_INFINITY;
            }
         } else if (var6 < 255) {
            return Float.intBitsToFloat(var3 | var6 << 23 | var5);
         } else {
            return var3 == 0 ? Float.POSITIVE_INFINITY : Float.NEGATIVE_INFINITY;
         }
      }
   }

   public static double nextAfter(double var0, double var2) {
      if (Double.isNaN(var0) || Double.isNaN(var2)) {
         return Double.NaN;
      } else if (var0 == var2) {
         return var2;
      } else if (Double.isInfinite(var0)) {
         return var0 < 0.0 ? -Double.MAX_VALUE : Double.MAX_VALUE;
      } else if (var0 == 0.0) {
         return var2 < 0.0 ? -Double.MIN_VALUE : Double.MIN_VALUE;
      } else {
         long var4 = Double.doubleToRawLongBits(var0);
         long var6 = var4 & Long.MIN_VALUE;
         return var2 < var0 ^ var6 == 0L
            ? Double.longBitsToDouble(var6 | (var4 & Long.MAX_VALUE) + 1L)
            : Double.longBitsToDouble(var6 | (var4 & Long.MAX_VALUE) - 1L);
      }
   }

   public static float nextAfter(float var0, double var1) {
      if (!Double.isNaN((double)var0) && !Double.isNaN(var1)) {
         if ((double)var0 == var1) {
            return (float)var1;
         } else if (Float.isInfinite(var0)) {
            return var0 < 0.0F ? -Float.MAX_VALUE : Float.MAX_VALUE;
         } else if (var0 == 0.0F) {
            return var1 < 0.0 ? -Float.MIN_VALUE : Float.MIN_VALUE;
         } else {
            int var3 = Float.floatToIntBits(var0);
            int var4 = var3 & -2147483648;
            return var1 < (double)var0 ^ var4 == 0
               ? Float.intBitsToFloat(var4 | (var3 & 2147483647) + 1)
               : Float.intBitsToFloat(var4 | (var3 & 2147483647) - 1);
         }
      } else {
         return Float.NaN;
      }
   }

   public static int floorInt(double var0) {
      int var2 = (int)var0;
      return var0 < (double)var2 ? var2 - 1 : var2;
   }

   public static double floor(double var0) {
      if (Double.isNaN(var0)) {
         return var0;
      } else if (!(var0 >= 4.5035996E15F) && !(var0 <= -4.5035996E15F)) {
         long var2 = (long)var0;
         if (var0 < 0.0 && (double)var2 != var0) {
            var2--;
         }

         return var2 == 0L ? var0 * (double)var2 : (double)var2;
      } else {
         return var0;
      }
   }

   public static double ceil(double var0) {
      if (Double.isNaN(var0)) {
         return var0;
      } else {
         double var2 = floor(var0);
         if (var2 == var0) {
            return var2;
         } else {
            var2++;
            return var2 == 0.0 ? var0 * var2 : var2;
         }
      }
   }

   public static double rint(double var0) {
      double var2 = floor(var0);
      double var4 = var0 - var2;
      if (var4 > 0.5) {
         return var2 == -1.0 ? -0.0 : var2 + 1.0;
      } else if (var4 < 0.5) {
         return var2;
      } else {
         long var6 = (long)var2;
         return (var6 & 1L) == 0L ? var2 : var2 + 1.0;
      }
   }

   public static long round(double var0) {
      long var2 = Double.doubleToRawLongBits(var0);
      int var4 = (int)(var2 >> 52) & 2047;
      int var5 = 1074 - var4;
      if ((var5 & -64) == 0) {
         long var6 = 4503599627370496L | var2 & 4503599627370495L;
         if (var2 < 0L) {
            var6 = -var6;
         }

         return (var6 >> var5) + 1L >> 1;
      } else {
         return (long)var0;
      }
   }

   public static int round(float var0) {
      int var1 = Float.floatToRawIntBits(var0);
      int var2 = var1 >> 23 & 0xFF;
      int var3 = 149 - var2;
      if ((var3 & -32) == 0) {
         int var4 = 8388608 | var1 & 8388607;
         if (var1 < 0) {
            var4 = -var4;
         }

         return (var4 >> var3) + 1 >> 1;
      } else {
         return (int)var0;
      }
   }

   public static int min(int var0, int var1) {
      return var0 <= var1 ? var0 : var1;
   }

   public static long min(long var0, long var2) {
      return var0 <= var2 ? var0 : var2;
   }

   public static float min(float var0, float var1) {
      if (var0 > var1) {
         return var1;
      } else if (var0 < var1) {
         return var0;
      } else if (var0 != var1) {
         return Float.NaN;
      } else {
         int var2 = Float.floatToRawIntBits(var0);
         return var2 == Integer.MIN_VALUE ? var0 : var1;
      }
   }

   public static double min(double var0, double var2) {
      if (var0 > var2) {
         return var2;
      } else if (var0 < var2) {
         return var0;
      } else if (var0 != var2) {
         return Double.NaN;
      } else {
         long var4 = Double.doubleToRawLongBits(var0);
         return var4 == Long.MIN_VALUE ? var0 : var2;
      }
   }

   public static int max(int var0, int var1) {
      return var0 <= var1 ? var1 : var0;
   }

   public static long max(long var0, long var2) {
      return var0 <= var2 ? var2 : var0;
   }

   public static float max(float var0, float var1) {
      if (var0 > var1) {
         return var0;
      } else if (var0 < var1) {
         return var1;
      } else if (var0 != var1) {
         return Float.NaN;
      } else {
         int var2 = Float.floatToRawIntBits(var0);
         return var2 == Integer.MIN_VALUE ? var1 : var0;
      }
   }

   public static double max(double var0, double var2) {
      if (var0 > var2) {
         return var0;
      } else if (var0 < var2) {
         return var2;
      } else if (var0 != var2) {
         return Double.NaN;
      } else {
         long var4 = Double.doubleToRawLongBits(var0);
         return var4 == Long.MIN_VALUE ? var2 : var0;
      }
   }

   public static double hypot(double var0, double var2) {
      if (Double.isInfinite(var0) || Double.isInfinite(var2)) {
         return Double.POSITIVE_INFINITY;
      } else if (!Double.isNaN(var0) && !Double.isNaN(var2)) {
         int var4 = getExponent(var0);
         int var5 = getExponent(var2);
         if (var4 > var5 + 27) {
            return abs(var0);
         } else if (var5 > var4 + 27) {
            return abs(var2);
         } else {
            int var6 = (var4 + var5) / 2;
            double var7 = scalb(var0, -var6);
            double var9 = scalb(var2, -var6);
            double var11 = sqrt(var7 * var7 + var9 * var9);
            return scalb(var11, var6);
         }
      } else {
         return Double.NaN;
      }
   }

   public static double IEEEremainder(double var0, double var2) {
      if (getExponent(var0) != 1024 && getExponent(var2) != 1024 && var2 != 0.0) {
         double var4 = rint(var0 / var2);
         double var6 = Double.isInfinite(var4) ? 0.0 : var0 - var2 * var4;
         return var6 == 0.0 ? copySign(var6, var0) : var6;
      } else {
         return Double.isInfinite(var2) && !Double.isInfinite(var0) ? var0 : Double.NaN;
      }
   }

   public static int floorDiv(int var0, int var1) {
      if (var1 == 0) {
         return var1;
      } else {
         int var2 = var0 % var1;
         return (var0 ^ var1) < 0 && var2 != 0 ? var0 / var1 - 1 : var0 / var1;
      }
   }

   public static long floorDiv(long var0, long var2) {
      if (var2 == 0L) {
         return var2;
      } else {
         long var4 = var0 % var2;
         return (var0 ^ var2) < 0L && var4 != 0L ? var0 / var2 - 1L : var0 / var2;
      }
   }

   public static int floorMod(int var0, int var1) {
      if (var1 == 0) {
         return var1;
      } else {
         int var2 = var0 % var1;
         return (var0 ^ var1) < 0 && var2 != 0 ? var1 + var2 : var2;
      }
   }

   public static long floorMod(long var0, long var2) {
      if (var2 == 0L) {
         return var2;
      } else {
         long var4 = var0 % var2;
         return (var0 ^ var2) < 0L && var4 != 0L ? var2 + var4 : var4;
      }
   }

   public static double copySign(double var0, double var2) {
      long var4 = Double.doubleToRawLongBits(var0);
      long var6 = Double.doubleToRawLongBits(var2);
      return (var4 ^ var6) >= 0L ? var0 : -var0;
   }

   public static float copySign(float var0, float var1) {
      int var2 = Float.floatToRawIntBits(var0);
      int var3 = Float.floatToRawIntBits(var1);
      return (var2 ^ var3) >= 0 ? var0 : -var0;
   }

   public static int getExponent(double var0) {
      return (int)(Double.doubleToRawLongBits(var0) >>> 52 & 2047L) - 1023;
   }

   public static int getExponent(float var0) {
      return (Float.floatToRawIntBits(var0) >>> 23 & 0xFF) - 127;
   }

   private static class CodyWaite {
      private final int finalK;
      private final double finalRemA;
      private final double finalRemB;

      CodyWaite(double var1) {
         int var3 = (int)(var1 * 0.6366197723675814);

         while (true) {
            double var8 = (double)(-var3) * 1.5707963F;
            double var4 = var1 + var8;
            double var6 = -(var4 - var1 - var8);
            var8 = (double)(-var3) * 7.549789948768648E-8;
            double var12 = var8 + var4;
            var6 += -(var12 - var4 - var8);
            var8 = (double)(-var3) * 6.123233995736766E-17;
            var4 = var8 + var12;
            var6 += -(var4 - var12 - var8);
            if (var4 > 0.0) {
               this.finalK = var3;
               this.finalRemA = var4;
               this.finalRemB = var6;
               return;
            }

            var3--;
         }
      }

      int getK() {
         return this.finalK;
      }

      double getRemA() {
         return this.finalRemA;
      }

      double getRemB() {
         return this.finalRemB;
      }
   }

   private static class ExpFracTable {
      private static final double[] EXP_FRAC_TABLE_A = FastMathLiteralArrays.loadExpFracA();
      private static final double[] EXP_FRAC_TABLE_B = FastMathLiteralArrays.loadExpFracB();

      static double[] access$200() {
         return EXP_FRAC_TABLE_A;
      }

      static double[] access$300() {
         return EXP_FRAC_TABLE_B;
      }
   }

   private static class ExpIntTable {
      private static final double[] EXP_INT_TABLE_A = FastMathLiteralArrays.loadExpIntA();
      private static final double[] EXP_INT_TABLE_B = FastMathLiteralArrays.loadExpIntB();

      static double[] access$000() {
         return EXP_INT_TABLE_A;
      }

      static double[] access$100() {
         return EXP_INT_TABLE_B;
      }
   }

   private static class Split {
      public static final FastMath.Split NAN = new FastMath.Split(Double.NaN, 0.0);
      public static final FastMath.Split POSITIVE_INFINITY = new FastMath.Split(Double.POSITIVE_INFINITY, 0.0);
      public static final FastMath.Split NEGATIVE_INFINITY = new FastMath.Split(Double.NEGATIVE_INFINITY, 0.0);
      private final double full;
      private final double high;
      private final double low;

      Split(double var1) {
         this.full = var1;
         this.high = Double.longBitsToDouble(Double.doubleToRawLongBits(var1) & -134217728L);
         this.low = var1 - this.high;
      }

      Split(double var1, double var3) {
         this(var1 == 0.0 ? (var3 == 0.0 && Double.doubleToRawLongBits(var1) == Long.MIN_VALUE ? -0.0 : var3) : var1 + var3, var1, var3);
      }

      Split(double var1, double var3, double var5) {
         this.full = var1;
         this.high = var3;
         this.low = var5;
      }

      public FastMath.Split multiply(FastMath.Split var1) {
         FastMath.Split var2 = new FastMath.Split(this.full * var1.full);
         double var3 = this.low * var1.low - (var2.full - this.high * var1.high - this.low * var1.high - this.high * var1.low);
         return new FastMath.Split(var2.high, var2.low + var3);
      }

      public FastMath.Split reciprocal() {
         double var1 = 1.0 / this.full;
         FastMath.Split var3 = new FastMath.Split(var1);
         FastMath.Split var4 = this.multiply(var3);
         double var5 = var4.high - 1.0 + var4.low;
         return Double.isNaN(var5) ? var3 : new FastMath.Split(var3.high, var3.low - var5 / this.full);
      }

      private FastMath.Split pow(long var1) {
         FastMath.Split var3 = new FastMath.Split(1.0);
         FastMath.Split var4 = new FastMath.Split(this.full, this.high, this.low);

         for (long var5 = var1; var5 != 0L; var5 >>>= 1) {
            if ((var5 & 1L) != 0L) {
               var3 = var3.multiply(var4);
            }

            var4 = var4.multiply(var4);
         }

         if (!Double.isNaN(var3.full)) {
            return var3;
         } else if (Double.isNaN(this.full)) {
            return NAN;
         } else if (FastMath.abs(this.full) < 1.0) {
            return new FastMath.Split(FastMath.copySign(0.0, this.full), 0.0);
         } else {
            return this.full < 0.0 && (var1 & 1L) == 1L ? NEGATIVE_INFINITY : POSITIVE_INFINITY;
         }
      }

      static FastMath.Split access$500(FastMath.Split var0, long var1) {
         return var0.pow(var1);
      }

      static double access$600(FastMath.Split var0) {
         return var0.full;
      }
   }

   private static class lnMant {
      private static final double[][] LN_MANT = FastMathLiteralArrays.loadLnMant();

      static double[][] access$400() {
         return LN_MANT;
      }
   }
}
