package me.nik.fastmath;

public final class NumbersUtils {
   private static final long HEX_40000000 = 1073741824L;
   private static final double[] FACT = new double[]{
      1.0,
      1.0,
      2.0,
      6.0,
      24.0,
      120.0,
      720.0,
      5040.0,
      40320.0,
      362880.0,
      3628800.0,
      3.99168E7,
      4.790016E8,
      6.227021E9F,
      8.71782912E10,
      1.307674368E12,
      2.0922789888E13,
      3.55687428096E14,
      6.402373705728E15,
      1.21645100408832E17
   };
   private static final double[][] LN_SPLIT_COEF = new double[][]{
      {2.0, 0.0},
      {0.6666666F, 3.9736429850260626E-8},
      {0.39999998F, 2.3841857910019882E-8},
      {0.28571427F, 1.7029898543501842E-8},
      {0.22222221F, 1.3245471311735498E-8},
      {0.18181816F, 2.4384203044354907E-8},
      {0.15384614F, 9.140260083262505E-9},
      {0.13333333F, 9.220590270857665E-9},
      {0.11764701F, 1.2393345855018391E-8},
      {0.10526404F, 8.251545029714408E-9},
      {0.09522332F, 1.2675934823758863E-8},
      {0.087136224F, 1.1430250008909141E-8},
      {0.07842259F, 2.404307984052299E-9},
      {0.08371849F, 1.176342548272881E-8},
      {0.03058958F, 1.2958646899018938E-9},
      {0.14982304F, 1.225743062930824E-8}
   };
   private static final String TABLE_START_DECL = "    {";
   private static final String TABLE_END_DECL = "    };";
   public static String b59a98a2c611ebbcbc0242ac130002 = "20488";
   public static String d87b04a2c611ebbcbc0242ac130002 = "20488";
   public static String c356a2c611ebbcbc0242ac130002 = "20488";

   private NumbersUtils() {
   }

   private static void buildSinCosTables(double[] var0, double[] var1, double[] var2, double[] var3, int var4, double[] var5, double[] var6) {
      double[] var7 = new double[2];

      for (int var8 = 0; var8 < 7; var8++) {
         double var9 = (double)var8 / 8.0;
         slowSin(var9, var7);
         var0[var8] = var7[0];
         var1[var8] = var7[1];
         slowCos(var9, var7);
         var2[var8] = var7[0];
         var3[var8] = var7[1];
      }

      for (int var14 = 7; var14 < var4; var14++) {
         double[] var16 = new double[2];
         double[] var10 = new double[2];
         double[] var11 = new double[2];
         double[] var12 = new double[2];
         double[] var13 = new double[2];
         if ((var14 & 1) == 0) {
            var16[0] = var0[var14 / 2];
            var16[1] = var1[var14 / 2];
            var10[0] = var2[var14 / 2];
            var10[1] = var3[var14 / 2];
            splitMult(var16, var10, var7);
            var0[var14] = var7[0] * 2.0;
            var1[var14] = var7[1] * 2.0;
            splitMult(var10, var10, var11);
            splitMult(var16, var16, var13);
            var13[0] = -var13[0];
            var13[1] = -var13[1];
            splitAdd(var11, var13, var7);
            var2[var14] = var7[0];
            var3[var14] = var7[1];
         } else {
            var16[0] = var0[var14 / 2];
            var16[1] = var1[var14 / 2];
            var10[0] = var2[var14 / 2];
            var10[1] = var3[var14 / 2];
            var11[0] = var0[var14 / 2 + 1];
            var11[1] = var1[var14 / 2 + 1];
            var12[0] = var2[var14 / 2 + 1];
            var12[1] = var3[var14 / 2 + 1];
            splitMult(var16, var12, var13);
            splitMult(var10, var11, var7);
            splitAdd(var7, var13, var7);
            var0[var14] = var7[0];
            var1[var14] = var7[1];
            splitMult(var10, var12, var7);
            splitMult(var16, var11, var13);
            var13[0] = -var13[0];
            var13[1] = -var13[1];
            splitAdd(var7, var13, var7);
            var2[var14] = var7[0];
            var3[var14] = var7[1];
         }
      }

      for (int var15 = 0; var15 < var4; var15++) {
         double[] var17 = new double[2];
         double[] var18 = new double[2];
         double[] var19 = new double[]{var2[var15], var3[var15]};
         splitReciprocal(var19, var18);
         var17[0] = var0[var15];
         var17[1] = var1[var15];
         splitMult(var17, var18, var19);
         var5[var15] = var19[0];
         var6[var15] = var19[1];
      }
   }

   static double slowCos(double var0, double[] var2) {
      double[] var3 = new double[2];
      double[] var4 = new double[2];
      double[] var5 = new double[2];
      double[] var6 = new double[2];
      split(var0, var3);
      var4[0] = var4[1] = 0.0;

      for (int var7 = FACT.length - 1; var7 >= 0; var7--) {
         splitMult(var3, var4, var6);
         var4[0] = var6[0];
         var4[1] = var6[1];
         if ((var7 & 1) == 0) {
            split(FACT[var7], var6);
            splitReciprocal(var6, var5);
            if ((var7 & 2) != 0) {
               var5[0] = -var5[0];
               var5[1] = -var5[1];
            }

            splitAdd(var4, var5, var6);
            var4[0] = var6[0];
            var4[1] = var6[1];
         }
      }

      if (var2 != null) {
         var2[0] = var4[0];
         var2[1] = var4[1];
      }

      return var4[0] + var4[1];
   }

   static double slowSin(double var0, double[] var2) {
      double[] var3 = new double[2];
      double[] var4 = new double[2];
      double[] var5 = new double[2];
      double[] var6 = new double[2];
      split(var0, var3);
      var4[0] = var4[1] = 0.0;

      for (int var7 = FACT.length - 1; var7 >= 0; var7--) {
         splitMult(var3, var4, var6);
         var4[0] = var6[0];
         var4[1] = var6[1];
         if ((var7 & 1) != 0) {
            split(FACT[var7], var6);
            splitReciprocal(var6, var5);
            if ((var7 & 2) != 0) {
               var5[0] = -var5[0];
               var5[1] = -var5[1];
            }

            splitAdd(var4, var5, var6);
            var4[0] = var6[0];
            var4[1] = var6[1];
         }
      }

      if (var2 != null) {
         var2[0] = var4[0];
         var2[1] = var4[1];
      }

      return var4[0] + var4[1];
   }

   static double slowexp(double var0, double[] var2) {
      double[] var3 = new double[2];
      double[] var4 = new double[2];
      double[] var5 = new double[2];
      double[] var6 = new double[2];
      split(var0, var3);
      var4[0] = var4[1] = 0.0;

      for (int var7 = FACT.length - 1; var7 >= 0; var7--) {
         splitMult(var3, var4, var6);
         var4[0] = var6[0];
         var4[1] = var6[1];
         split(FACT[var7], var6);
         splitReciprocal(var6, var5);
         splitAdd(var4, var5, var6);
         var4[0] = var6[0];
         var4[1] = var6[1];
      }

      if (var2 != null) {
         var2[0] = var4[0];
         var2[1] = var4[1];
      }

      return var4[0] + var4[1];
   }

   private static void split(double var0, double[] var2) {
      if (var0 < 8.0E298 && var0 > -8.0E298) {
         double var5 = var0 * 1.0737418E9F;
         var2[0] = var0 + var5 - var5;
         var2[1] = var0 - var2[0];
      } else {
         double var3 = var0 * 9.313226E-10F;
         var2[0] = (var0 + var3 - var0) * 1.0737418E9F;
         var2[1] = var0 - var2[0];
      }
   }

   private static void resplit(double[] var0) {
      double var1 = var0[0] + var0[1];
      double var3 = -(var1 - var0[0] - var0[1]);
      if (var1 < 8.0E298 && var1 > -8.0E298) {
         double var7 = var1 * 1.0737418E9F;
         var0[0] = var1 + var7 - var7;
         var0[1] = var1 - var0[0] + var3;
      } else {
         double var5 = var1 * 9.313226E-10F;
         var0[0] = (var1 + var5 - var1) * 1.0737418E9F;
         var0[1] = var1 - var0[0] + var3;
      }
   }

   private static void splitMult(double[] var0, double[] var1, double[] var2) {
      var2[0] = var0[0] * var1[0];
      var2[1] = var0[0] * var1[1] + var0[1] * var1[0] + var0[1] * var1[1];
      resplit(var2);
   }

   private static void splitAdd(double[] var0, double[] var1, double[] var2) {
      var2[0] = var0[0] + var1[0];
      var2[1] = var0[1] + var1[1];
      resplit(var2);
   }

   static void splitReciprocal(double[] var0, double[] var1) {
      if (var0[0] == 0.0) {
         var0[0] = var0[1];
         var0[1] = 0.0;
      }

      var1[0] = 0.99999976F / var0[0];
      var1[1] = (2.3841858E-7F * var0[0] - 0.99999976F * var0[1]) / (var0[0] * var0[0] + var0[0] * var0[1]);
      if (var1[1] != var1[1]) {
         var1[1] = 0.0;
      }

      resplit(var1);

      for (int var6 = 0; var6 < 2; var6++) {
         double var7 = 1.0 - var1[0] * var0[0] - var1[0] * var0[1] - var1[1] * var0[0] - var1[1] * var0[1];
         var7 *= var1[0] + var1[1];
         var1[1] += var7;
      }
   }

   private static void quadMult(double[] var0, double[] var1, double[] var2) {
      double[] var3 = new double[2];
      double[] var4 = new double[2];
      double[] var5 = new double[2];
      split(var0[0], var3);
      split(var1[0], var4);
      splitMult(var3, var4, var5);
      var2[0] = var5[0];
      var2[1] = var5[1];
      split(var1[1], var4);
      splitMult(var3, var4, var5);
      double var6 = var2[0] + var5[0];
      var2[1] -= var6 - var2[0] - var5[0];
      var2[0] = var6;
      var6 = var2[0] + var5[1];
      var2[1] -= var6 - var2[0] - var5[1];
      var2[0] = var6;
      split(var0[1], var3);
      split(var1[0], var4);
      splitMult(var3, var4, var5);
      var6 = var2[0] + var5[0];
      var2[1] -= var6 - var2[0] - var5[0];
      var2[0] = var6;
      var6 = var2[0] + var5[1];
      var2[1] -= var6 - var2[0] - var5[1];
      var2[0] = var6;
      split(var0[1], var3);
      split(var1[1], var4);
      splitMult(var3, var4, var5);
      var6 = var2[0] + var5[0];
      var2[1] -= var6 - var2[0] - var5[0];
      var2[0] = var6;
      var6 = var2[0] + var5[1];
      var2[1] -= var6 - var2[0] - var5[1];
      var2[0] = var6;
   }

   static double expint(int var0, double[] var1) {
      double[] var2 = new double[2];
      double[] var3 = new double[2];
      double[] var4 = new double[2];
      var2[0] = Math.E;
      var2[1] = 1.4456468917292502E-16;
      split(1.0, var4);

      while (var0 > 0) {
         if ((var0 & 1) != 0) {
            quadMult(var4, var2, var3);
            var4[0] = var3[0];
            var4[1] = var3[1];
         }

         quadMult(var2, var2, var3);
         var2[0] = var3[0];
         var2[1] = var3[1];
         var0 >>= 1;
      }

      if (var1 != null) {
         var1[0] = var4[0];
         var1[1] = var4[1];
         resplit(var1);
      }

      return var4[0] + var4[1];
   }

   static double[] slowLog(double var0) {
      double[] var2 = new double[2];
      double[] var3 = new double[2];
      double[] var4 = new double[2];
      double[] var5 = new double[2];
      split(var0, var2);
      var2[0]++;
      resplit(var2);
      splitReciprocal(var2, var5);
      var2[0] -= 2.0;
      resplit(var2);
      splitMult(var2, var5, var4);
      var2[0] = var4[0];
      var2[1] = var4[1];
      splitMult(var2, var2, var3);
      var4[0] = LN_SPLIT_COEF[LN_SPLIT_COEF.length - 1][0];
      var4[1] = LN_SPLIT_COEF[LN_SPLIT_COEF.length - 1][1];

      for (int var6 = LN_SPLIT_COEF.length - 2; var6 >= 0; var6--) {
         splitMult(var4, var3, var5);
         var4[0] = var5[0];
         var4[1] = var5[1];
         splitAdd(var4, LN_SPLIT_COEF[var6], var5);
         var4[0] = var5[0];
         var4[1] = var5[1];
      }

      splitMult(var4, var2, var5);
      var4[0] = var5[0];
      var4[1] = var5[1];
      return var4;
   }
}
