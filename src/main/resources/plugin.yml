name: Alice
version: *******
main: alice.Alice
api-version: '1.21'
prefix: Alice
authors: [Nik]
depend: [ProtocolLib]
softdepend: [ floodgate-bukkit, CrazyEnchantments, mcMMO, ProtocolSupport, ViaVersion, PlaceholderAPI, AdvancedEnchantments, EcoEnchants ]
description: An advanced next generation Anti Cheat
website: https://discord.gg/m7j2Y9H
commands:
  alice:
    description: The main Alice command
permissions:
  alice.admin:
    description: Gain full access to Alice
    default: op
    children:
      alice.gui.checks: true
      alice.alerts: true
      alice.bypass: true
      alice.commands.*: true
  alice.bypass:
    description: Bypass the Anti Cheat
    default: op
  alice.commands.logs:
    description: See a player's logs
    default: op
  alice.commands.profiler:
    description: Gives you access to toggle the Profiler
    default: op
  alice.commands.alerts:
    description: Gives you access to toggle the alerts
    default: op
  alice.commands.lag:
    description: Gives you access to view the server's resources
    default: op
  alice.commands.debug:
    description: Gives you access to debug a check category
    default: op
  alice.commands.spectate:
    description: Gives you access to spectate a player
    default: op
  alice.commands.vl:
    description: Gives you access to see a player's violations
    default: op
  alice.commands.menu:
    description: Gives you access to open Alice's Menu
    default: op
  alice.commands.punish:
    description: Gives you access to punish players
    default: op
  alice.commands.reload:
    description: Gives you access to Reload Alice
    default: op
  alice.commands.info:
    description: Gives you access to see a player's Information
    default: op
  alice.commands.kick:
    description: Gives you access to Kick players
    default: op
  alice.commands.checks:
    description: Gives you access to open the Checks GUI
    default: op
  alice.commands.themes:
    description: Gives you access to open the Themes GUI
    default: op
  alice.gui.checks:
    description: Gives you access to Enable or Disable checks via the GUI
    default: op
  alice.gui.themes:
    description: Gives you access to change the Theme via the GUI
    default: op
  alice.commands.*:
    description: Gives you access to all of Alice's Commands
    default: op
    children:
      alice.commands.alerts: true
      alice.commands.checks: true
      alice.commands.themes: true
      alice.commands.lag: true
      alice.commands.menu: true
      alice.commands.debug: true
      alice.commands.ping: true
      alice.commands.punish: true
      alice.commands.reload: true
      alice.commands.logs: true
      alice.commands.spectate: true
      alice.commands.vl: true