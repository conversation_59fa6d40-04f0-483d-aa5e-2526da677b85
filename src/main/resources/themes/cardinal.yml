theme_author: 'Nik'
prefix: '&6&l[&eAlice&6&l]&f&l»&r '
update_reminder: '&fThere is a new version available, Your version &a%current% &fnew version &a%new%'
reconnect: '&fYou must wait &6%seconds% &fseconds before logging in.'
client_alert: '&6%player% &fhas joined the server using &6%client%'
client_non_vanilla: '&6%player% &fhas joined the server with a non vanilla client. &6%client%'
client_kick: '&6%client% &fis not allowed on this server!'
no_perm: '&cI am not permitted to let you execute this command.'
player_not_found: '&fCould not find the player &6%player%&c.'
kick_sender: '&fYou have kicked &6%player%'
kick_crasher: '&cYou''re sending irregular packets!'
punish_sender: '&fYou have punished &6%player%'
punish_broadcast: '&6%player% &fHas been punished by Alice'
punishwave_countdown: '&fThe punish wave will execute in &6%countdown% &fseconds'
punishwave_punished: '&6%count% &fplayers have been punished for cheating'
punishwave_added: '&6%player% &fHas been added to the punish wave'
punishwave_exists: '&fThat player has already been added to the punish wave'
commands_list: '&6%syntax% &e- &6%description%'
alert_message: '&6%player% &ffailed &6%check% %bar%'
debug_message: '&6%check% &f%information%'
bar:
  opening_bracket: '&7['
  closing_bracket: '&7]'
  fill: '&6|'
  empty: '&f|'
alert_hover:
  - '&6Description:&r'
  - '%description%'
  - ' '
  - '&6Information:&r'
  - '%information%'
  - ' '
  - '&6Version: &r%version%'
  - ' '
  - '&6Ping: &r%ping%'
  - ' '
  - '&6TPS: &r%tps%'
  - ' '
  - '&fClick to spectate'
vl_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&6World Violations: &f%worldviolations%'
  - '&6Movement Violations: &f%movementviolations%'
  - '&6Combat Violations: &f%combatviolations%'
  - ' '
  - '&6Checks:'
  - ' '
  - '&6%checks%'
  - ' '
info_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&6Ping: &f%ping%'
  - '&6Transaction Ping: &f%transactionping%'
  - '&6Lag Status: &f%lagstatus%'
  - '&6CPS: &f%cps%'
  - '&6Sensitivity: &f%sensitivity%'
  - '&6Client: &f%client%'
  - '&6Version: &f%version%'
  - '&6Violations: &f%violations%'
  - '&6Nearby Entities: &f%entities%'
  - ' '
lag_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&6Environment: &f%environment%'
  - '&6System: &f%system%'
  - '&6TPS: &f%tps%'
  - '&6Threads: &f%threads%'
  - '&6Java Version: &f%javaversion%'
  - '&6Free Memory: &f%freememory%'
  - '&6Max Memory: &f%maxmemory%'
  - '&6Plugins: &f%plugins%'
  - ' '
profiler_result:
  - ' '
  - '%prefix%'
  - ' '
  - '&6Samples Collected: &f%samples%'
  - ' '
  - '&6Average CPU Usage: &f%averagecpu%'
  - ' '
  - '&6Average TPS: &f%averagetps%'
  - '&6Average Tick Time: &f%averagetick%'
  - ' '
  - '&6Average Packet Data Process Time: &f%averagepacketdata%'
  - '&6Average Packet Checks Process Time: &f%averagepacketchecks%'
  - ' '
  - '&6Average Bukkit Data Process Time: &f%averagebukkitdata%'
  - '&6Average Bukkit Checks Process Time: &f%averagebukkitchecks%'
  - ' '
  - '&6Total: &f%total%'
  - ' '