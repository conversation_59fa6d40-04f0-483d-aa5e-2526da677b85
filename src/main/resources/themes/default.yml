theme_author: 'Nik'
prefix: '&8「&cAlice&8」&7»&r '
update_reminder: '&fThere is a new version available, Your version &c%current% &fnew version &c%new%'
reconnect: '&fYou must wait &c%seconds% &fseconds before logging in.'
client_alert: '&7%player% &fhas joined the server using &c%client%'
client_non_vanilla: '&7%player% &fhas joined the server with a non vanilla client. &c%client%'
client_kick: '&c%client% &fis not allowed on this server!'
no_perm: '&cI am not permitted to let you execute this command.'
player_not_found: '&fCould not find the player &7%player%&c.'
kick_sender: '&fYou have kicked &7%player%'
kick_crasher: '&cYou''re sending irregular packets!'
punish_sender: '&fYou have punished &7%player%'
punish_broadcast: '&7%player% &fHas been punished by Alice'
punishwave_countdown: '&fThe punish wave will execute in &c%countdown% &fseconds'
punishwave_punished: '&c%count% &fplayers have been punished for cheating'
punishwave_added: '&7%player% &fHas been added to the punish wave'
punishwave_exists: '&fThat player has already been added to the punish wave'
commands_list: '&7%syntax% &8- &7%description%'
alert_message: '&7%player% &ffailed &c%check% %bar%'
debug_message: '&c%check% &f%information%'
bar:
  opening_bracket: '&8['
  closing_bracket: '&8]'
  fill: '&f|'
  empty: '&7|'
alert_hover:
  - '&7Description:&r'
  - '%description%'
  - ' '
  - '&7Information:&r'
  - '%information%'
  - ' '
  - '&7Version: &r%version%'
  - ' '
  - '&7Ping: &r%ping%'
  - ' '
  - '&7TPS: &r%tps%'
  - ' '
  - '&fClick to spectate'
vl_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&8» &7World Violations: &f%worldviolations%'
  - '&8» &7Movement Violations: &f%movementviolations%'
  - '&8» &7Combat Violations: &f%combatviolations%'
  - ' '
  - '&8» &7Checks:'
  - ' '
  - '&7%checks%'
  - ' '
info_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&8» &7Ping: &f%ping%'
  - '&8» &7Transaction Ping: &f%transactionping%'
  - '&8» &7Lag Status: &f%lagstatus%'
  - '&8» &7CPS: &f%cps%'
  - '&8» &7Sensitivity: &f%sensitivity%'
  - '&8» &7Client: &f%client%'
  - '&8» &7Version: &f%version%'
  - '&8» &7Violations: &f%violations%'
  - '&8» &7Nearby Entities: &f%entities%'
  - ' '
lag_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&8» &7Environment: &f%environment%'
  - '&8» &7System: &f%system%'
  - '&8» &7TPS: &f%tps%'
  - '&8» &7Threads: &f%threads%'
  - '&8» &7Java Version: &f%javaversion%'
  - '&8» &7Free Memory: &f%freememory%'
  - '&8» &7Max Memory: &f%maxmemory%'
  - '&8» &7Plugins: &f%plugins%'
  - ' '
profiler_result:
  - ' '
  - '%prefix%'
  - ' '
  - '&8» &7Samples Collected: &f%samples%'
  - ' '
  - '&8» &7Average CPU Usage: &f%averagecpu%'
  - ' '
  - '&8» &7Average TPS: &f%averagetps%'
  - '&8» &7Average Tick Time: &f%averagetick%'
  - ' '
  - '&8» &7Average Packet Data Process Time: &f%averagepacketdata%'
  - '&8» &7Average Packet Checks Process Time: &f%averagepacketchecks%'
  - ' '
  - '&8» &7Average Bukkit Data Process Time: &f%averagebukkitdata%'
  - '&8» &7Average Bukkit Checks Process Time: &f%averagebukkitchecks%'
  - ' '
  - '&8» &7Total: &f%total%'
  - ' '