theme_author: 'Nik'
prefix: '&f&l[&bAlice&f&l]&f&l»&r '
update_reminder: '&fThere is a new version available, Your version &b%current% &fnew version &b%new%'
reconnect: '&fYou must wait &7%seconds% &fseconds before logging in.'
client_alert: '&b%player% &fhas joined the server using &7%client%'
client_non_vanilla: '&b%player% &fhas joined the server with a non vanilla client. &7%client%'
client_kick: '&b%client% &fis not allowed on this server!'
no_perm: '&cI am not permitted to let you execute this command.'
player_not_found: '&fCould not find the player &b%player%&c.'
kick_sender: '&fYou have kicked &b%player%'
kick_crasher: '&cYou''re sending irregular packets!'
punish_sender: '&fYou have punished &b%player%'
punish_broadcast: '&b%player% &fHas been punished by Alice'
punishwave_countdown: '&fThe punish wave will execute in &7%countdown% &fseconds'
punishwave_punished: '&7%count% &fplayers have been punished for cheating'
punishwave_added: '&b%player% &fHas been added to the punish wave'
punishwave_exists: '&fThat player has already been added to the punish wave'
commands_list: '&b%syntax% &7- &b%description%'
alert_message: '&b%player% &ffailed &b%check% %bar%'
debug_message: '&b%check% &f%information%'
bar:
  opening_bracket: '&7['
  closing_bracket: '&7]'
  fill: '&b|'
  empty: '&f|'
alert_hover:
  - '&bDescription:&r'
  - '%description%'
  - ' '
  - '&bInformation:&r'
  - '%information%'
  - ' '
  - '&bVersion: &r%version%'
  - ' '
  - '&bPing: &r%ping%'
  - ' '
  - '&bTPS: &r%tps%'
  - ' '
  - '&fClick to spectate'
vl_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&bWorld Violations: &f%worldviolations%'
  - '&bMovement Violations: &f%movementviolations%'
  - '&bCombat Violations: &f%combatviolations%'
  - ' '
  - '&bChecks:'
  - ' '
  - '&b%checks%'
  - ' '
info_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&bPing: &f%ping%'
  - '&bTransaction Ping: &f%transactionping%'
  - '&bLag Status: &f%lagstatus%'
  - '&bCPS: &f%cps%'
  - '&bSensitivity: &f%sensitivity%'
  - '&bClient: &f%client%'
  - '&bVersion: &f%version%'
  - '&bViolations: &f%violations%'
  - '&bNearby Entities: &f%entities%'
  - ' '
lag_command:
  - ' '
  - '%prefix%'
  - ' '
  - '&bEnvironment: &f%environment%'
  - '&bSystem: &f%system%'
  - '&bTPS: &f%tps%'
  - '&bThreads: &f%threads%'
  - '&bJava Version: &f%javaversion%'
  - '&bFree Memory: &f%freememory%'
  - '&bMax Memory: &f%maxmemory%'
  - '&bPlugins: &f%plugins%'
  - ' '
profiler_result:
  - ' '
  - '%prefix%'
  - ' '
  - '&bSamples Collected: &f%samples%'
  - ' '
  - '&bAverage CPU Usage: &f%averagecpu%'
  - ' '
  - '&bAverage TPS: &f%averagetps%'
  - '&bAverage Tick Time: &f%averagetick%'
  - ' '
  - '&bAverage Packet Data Process Time: &f%averagepacketdata%'
  - '&bAverage Packet Checks Process Time: &f%averagepacketchecks%'
  - ' '
  - '&bAverage Bukkit Data Process Time: &f%averagebukkitdata%'
  - '&bAverage Bukkit Checks Process Time: &f%averagebukkitchecks%'
  - ' '
  - '&bTotal: &f%total%'
  - ' '